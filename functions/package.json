{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "index.js", "dependencies": {"@google-cloud/aiplatform": "^4.2.0", "@google-cloud/vertexai": "^1.10.0", "@google/genai": "^1.7.0", "dotenv": "^16.5.0", "firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2", "googleapis": "^150.0.1", "nodemailer": "^6.9.16", "path": "^0.12.7"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}