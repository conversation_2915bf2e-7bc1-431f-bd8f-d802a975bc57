async function createFirestoreIndex(projectId, databaseId, collectionGroup, fields) {
    // const firestore = new db({
    //     projectId: projectId,
    // });
    console.log("Admin object:", admin);
    console.log("DB object:", db);
    const parent = db.databasePath(projectId, databaseId);

    const index = {
        fields: fields,
        collectionGroup: collectionGroup,
    };

    try {
        const [operation] = await db.createIndex({
            parent: parent,
            index: index,
        });

        console.log(`Index creation started: ${operation.name}`);

        // To wait for the operation to complete:
        const [result] = await operation.promise();
        console.log('Index creation complete');
        console.log(result);

    } catch (error) {
        console.error('Error creating index:', error);
    }
}


async function sendEmailToTeamMembers(to, pass) {
    try {
        sendEmailToUser(to, "Wedding-Ease", `<html>
        <body>
        <p>Welcome to Wedding-Ease!</p>
        <p>Your login credentials are:</p>
        <p>Email: ${to}</p> 
        <p>Password: ${pass}</p> 
        </body>
        </html>`);
    } catch (error) {
        console.log(error);
    }
}
async function sendEmailToUser(to, subject, html) {
    try {
        const mailOptions = {
            from: {
                name: 'Wedding-Ease',
                address: '<EMAIL>'
            },
            to: to,
            subject: subject,
            html: html
        };
        return transporter.sendMail(mailOptions, (error, data) => {
            if (error) {
                console.log(error)
                return
            }
            console.log("Sent!")
        });
    } catch (error) {
        console.log(error);
    }
}
