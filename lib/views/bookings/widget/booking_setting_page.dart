import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/models/bookings_model.dart';
import 'package:wedding_super_admin/models/settings.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import '../../../controller/home_ctrl.dart';
import '../../../shared/booking_methods.dart';
import '../../../shared/theme.dart';
import '../../common/page_header.dart';

class BookingSettings extends StatefulWidget {
  const BookingSettings({super.key});

  @override
  State<BookingSettings> createState() => _BookingSettingsState();
}

class _BookingSettingsState extends State<BookingSettings> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: GetBuilder<HomeCtrl>(
        builder: (hCtrl) {
          return SubBookingSettings(
            settingsModel: hCtrl.settings,
            blockedSlotModelList: hCtrl.blockedSlotModelList,
          );
        },
      ),
    );
  }
}

class SubBookingSettings extends StatefulWidget {
  const SubBookingSettings({
    super.key,
    required this.settingsModel,
    required this.blockedSlotModelList,
  });
  final SettingsModel? settingsModel;
  final List<BlockedSlotModel> blockedSlotModelList;
  @override
  State<SubBookingSettings> createState() => _SubBookingSettingsState();
}

class _SubBookingSettingsState extends State<SubBookingSettings> {
  List<String> slotsList = [];
  DateTime selectedDate = DateTime.now();
  String? selectedSlot;

  setSubSettingPage() {
    final settings = widget.settingsModel;
    if (settings != null) {
      DateTime bookingStartTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        settings.startHour,
        settings.startMin,
      );

      final bookingEndTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        settings.endHour,
        settings.endMin,
      );

      slotsList = generateTimeSlotsWithBlocked(bookingStartTime, bookingEndTime,
          widget.settingsModel!.slotGap, widget.blockedSlotModelList);
    }
  }

  @override
  Widget build(BuildContext context) {
    setSubSettingPage();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const PageHeaderWithTrailingAndBack(
          title: 'Booking Settings',
          showTrailing: false,
        ),
        const SizedBox(height: 25),
        widget.settingsModel != null
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CalendarDatePicker(
                              initialDate: DateTime.now(),
                              firstDate: DateTime.now(),
                              lastDate: DateTime(3000),
                              onDateChanged: (value) {
                                selectedDate = value;
                                setState(() {});
                              },
                            ),
                            const SizedBox(height: 25),
                            Text("Blocked Slot",
                                style: GoogleFonts.zcoolXiaoWei(
                                    color: themeColor, fontSize: 18)),
                            const SizedBox(height: 15),
                            StaggeredGrid.extent(
                              maxCrossAxisExtent: 170,
                              mainAxisSpacing: 15,
                              crossAxisSpacing: 15,
                              children: [
                                ...List.generate(
                                  widget.blockedSlotModelList.length,
                                  (index) {
                                    final currentBlock =
                                        widget.blockedSlotModelList[index];
                                    DateTime blockedDateTime =
                                        currentBlock.blockedDateTime;

                                    String str =
                                        '${blockedDateTime.hour.toString().padLeft(2, "0")}:${blockedDateTime.minute.toString().padLeft(2, "0")} ${blockedDateTime.hour < 12 ? 'AM' : 'PM'} - ${blockedDateTime.add(Duration(minutes: currentBlock.slotGap)).hour.toString().padLeft(2, "0")}:${blockedDateTime.add(Duration(minutes: currentBlock.slotGap)).minute.toString().padLeft(2, "0")} ${blockedDateTime.add(Duration(minutes: currentBlock.slotGap)).hour < 12 ? 'AM' : 'PM'}';
                                    return InkWell(
                                      onLongPress: () async {
                                        showDialog(
                                          context: context,
                                          builder: (context) {
                                            bool loading = false;
                                            return StatefulBuilder(
                                              builder: (context, setState2) {
                                                return AlertDialog(
                                                  backgroundColor: Colors.white,
                                                  surfaceTintColor:
                                                      Colors.white,
                                                  title: const Text("Un-Block"),
                                                  content: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                          "Date: ${blockedDateTime.convertToDDMMYY()}"),
                                                      if (!currentBlock
                                                          .dateBlock)
                                                        Text('Time: $str')
                                                    ],
                                                  ),
                                                  actions: loading
                                                      ? [
                                                          const SizedBox(
                                                            height: 25,
                                                            width: 25,
                                                            child: Center(
                                                              child:
                                                                  CircularProgressIndicator(
                                                                      strokeWidth:
                                                                          3.5),
                                                            ),
                                                          )
                                                        ]
                                                      : [
                                                          TextButton(
                                                            onPressed:
                                                                () async {
                                                              if (loading) {
                                                                return;
                                                              }

                                                              try {
                                                                setState2(() {
                                                                  loading =
                                                                      true;
                                                                });
                                                                await FBFireStore
                                                                    .blocked
                                                                    .doc(currentBlock
                                                                        .docId)
                                                                    .delete();
                                                                if (context
                                                                    .mounted) {
                                                                  setState2(() {
                                                                    loading =
                                                                        false;
                                                                  });
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                }
                                                              } on Exception catch (e) {
                                                                setState2(() {
                                                                  loading =
                                                                      false;
                                                                });
                                                                debugPrint(e
                                                                    .toString());
                                                              }
                                                            },
                                                            child: const Text(
                                                                'Unblock'),
                                                          ),
                                                          TextButton(
                                                            onPressed: () {
                                                              Navigator.of(
                                                                      context)
                                                                  .pop();
                                                            },
                                                            child: const Text(
                                                                'Cancel'),
                                                          ),
                                                        ],
                                                );
                                              },
                                            );
                                          },
                                        );
                                      },
                                      child: Container(
                                        // width: ,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(7),
                                          border: Border.all(
                                              color: const Color.fromARGB(
                                                  255, 177, 177, 177)),
                                        ),
                                        height: 65,
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 10),
                                        child: Center(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Text(blockedDateTime
                                                  .convertToDDMMYY()),
                                              if (!currentBlock.dateBlock)
                                                Text(str),
                                            ],
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                      const SizedBox(width: 15),
                      // const VerticalDivider(),
                      const SizedBox(width: 15),
                      Expanded(
                        flex: 2,
                        child: Container(
                          decoration: const BoxDecoration(
                              border: Border(
                                  left: BorderSide(color: dividerColor))),
                          padding: const EdgeInsets.only(left: 15),
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      "Slots",
                                      style: GoogleFonts.zcoolXiaoWei(
                                          color: themeColor, fontSize: 18),
                                    ),
                                    const SizedBox(width: 15),
                                    if (widget.blockedSlotModelList
                                        .where((element) {
                                      final res = ((DateTime(
                                                      element
                                                          .blockedDateTime.year,
                                                      element.blockedDateTime
                                                          .month,
                                                      element
                                                          .blockedDateTime.day)
                                                  .difference(DateTime(
                                                      selectedDate.year,
                                                      selectedDate.month,
                                                      selectedDate.day))
                                                  .inDays ==
                                              0) &&
                                          element.dateBlock);
                                      return res;
                                    }).isEmpty)
                                      OutlinedButton(
                                          // style: OutlinedButton.styleFrom(),
                                          onPressed: () async {
                                            showDialog(
                                              context: context,
                                              builder: (context) {
                                                bool loading = false;
                                                return StatefulBuilder(
                                                  builder:
                                                      (context, setState2) {
                                                    return AlertDialog(
                                                      backgroundColor:
                                                          Colors.white,
                                                      surfaceTintColor:
                                                          Colors.white,
                                                      title:
                                                          const Text("Block"),
                                                      content: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                              "Date: ${selectedDate.convertToDDMMYY()}"),
                                                          // Text(
                                                          //     "Slot: ${slotsList[index]}")
                                                        ],
                                                      ),
                                                      actions: loading
                                                          ? [
                                                              const SizedBox(
                                                                height: 25,
                                                                width: 25,
                                                                child: Center(
                                                                  child: CircularProgressIndicator(
                                                                      strokeWidth:
                                                                          3.5),
                                                                ),
                                                              )
                                                            ]
                                                          : [
                                                              TextButton(
                                                                onPressed:
                                                                    () async {
                                                                  if (loading) {
                                                                    return;
                                                                  }

                                                                  try {
                                                                    setState2(
                                                                        () {
                                                                      loading =
                                                                          true;
                                                                    });
                                                                    final slotDateTime =
                                                                        stringToDateTime(
                                                                            '00:00 AM - 00:00 AM');
                                                                    await FBFireStore
                                                                        .blocked
                                                                        .add({
                                                                      'slotGap': widget
                                                                          .settingsModel!
                                                                          .slotGap,
                                                                      'createdAt':
                                                                          DateTime.now()
                                                                              .millisecondsSinceEpoch,
                                                                      'dateBlock':
                                                                          true,
                                                                      'blockedDateTime':
                                                                          DateTime(
                                                                        selectedDate
                                                                            .year,
                                                                        selectedDate
                                                                            .month,
                                                                        selectedDate
                                                                            .day,
                                                                        slotDateTime['startTime']!
                                                                            .hour,
                                                                        slotDateTime['startTime']!
                                                                            .minute,
                                                                      ).millisecondsSinceEpoch
                                                                    });
                                                                    if (context
                                                                        .mounted) {
                                                                      setState2(
                                                                          () {
                                                                        loading =
                                                                            false;
                                                                      });
                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();
                                                                    }
                                                                  } on Exception catch (e) {
                                                                    setState2(
                                                                        () {
                                                                      loading =
                                                                          false;
                                                                    });
                                                                    debugPrint(e
                                                                        .toString());
                                                                  }
                                                                },
                                                                child:
                                                                    const Text(
                                                                        'Block'),
                                                              ),
                                                              TextButton(
                                                                onPressed: () {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                },
                                                                child: const Text(
                                                                    'Cancel'),
                                                              ),
                                                            ],
                                                    );
                                                  },
                                                );
                                              },
                                            );
                                          },
                                          child: const Text('Block Date'))
                                  ],
                                ),
                                const SizedBox(height: 15),
                                slotsList.isEmpty
                                    ? const Center(child: Text('Date Blocked'))
                                    : StaggeredGrid.extent(
                                        maxCrossAxisExtent: 170,
                                        mainAxisSpacing: 15,
                                        crossAxisSpacing: 15,
                                        children: [
                                          ...List.generate(
                                            slotsList.length,
                                            (index) {
                                              final selected = selectedSlot ==
                                                  slotsList[index];
                                              return InkWell(
                                                onTap: () async {
                                                  showDialog(
                                                    context: context,
                                                    builder: (context) {
                                                      bool loading = false;
                                                      return StatefulBuilder(
                                                        builder: (context,
                                                            setState2) {
                                                          return AlertDialog(
                                                            backgroundColor:
                                                                Colors.white,
                                                            surfaceTintColor:
                                                                Colors.white,
                                                            title: const Text(
                                                                "Block"),
                                                            content: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                    "Date: ${selectedDate.convertToDDMMYY()}"),
                                                                Text(
                                                                    "Slot: ${slotsList[index]}")
                                                              ],
                                                            ),
                                                            actions: loading
                                                                ? [
                                                                    const SizedBox(
                                                                      height:
                                                                          25,
                                                                      width: 25,
                                                                      child:
                                                                          Center(
                                                                        child: CircularProgressIndicator(
                                                                            strokeWidth:
                                                                                3.5),
                                                                      ),
                                                                    )
                                                                  ]
                                                                : [
                                                                    TextButton(
                                                                      onPressed:
                                                                          () async {
                                                                        if (loading) {
                                                                          return;
                                                                        }

                                                                        try {
                                                                          setState2(
                                                                              () {
                                                                            loading =
                                                                                true;
                                                                          });
                                                                          final slotDateTime =
                                                                              stringToDateTime(slotsList[index]);
                                                                          await FBFireStore
                                                                              .blocked
                                                                              .add({
                                                                            'slotGap':
                                                                                widget.settingsModel!.slotGap,
                                                                            'createdAt':
                                                                                DateTime.now().millisecondsSinceEpoch,
                                                                            'dateBlock':
                                                                                false,
                                                                            'blockedDateTime':
                                                                                DateTime(
                                                                              selectedDate.year,
                                                                              selectedDate.month,
                                                                              selectedDate.day,
                                                                              slotDateTime['startTime']!.hour,
                                                                              slotDateTime['startTime']!.minute,
                                                                            ).millisecondsSinceEpoch
                                                                          });
                                                                          if (context
                                                                              .mounted) {
                                                                            setState2(() {
                                                                              loading = false;
                                                                            });
                                                                            Navigator.of(context).pop();
                                                                          }
                                                                        } on Exception catch (e) {
                                                                          setState2(
                                                                              () {
                                                                            loading =
                                                                                false;
                                                                          });
                                                                          debugPrint(
                                                                              e.toString());
                                                                        }
                                                                      },
                                                                      child: const Text(
                                                                          'Block'),
                                                                    ),
                                                                    TextButton(
                                                                      onPressed:
                                                                          () {
                                                                        Navigator.of(context)
                                                                            .pop();
                                                                      },
                                                                      child: const Text(
                                                                          'Cancel'),
                                                                    ),
                                                                  ],
                                                          );
                                                        },
                                                      );
                                                    },
                                                  );
                                                },
                                                child: Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(vertical: 10),
                                                  decoration: BoxDecoration(
                                                      color: selected
                                                          ? themeColor
                                                          : null,
                                                      border: Border.all(
                                                          color: selected
                                                              ? themeColor
                                                              : const Color(
                                                                  0xff6C6C6C))),
                                                  child: Center(
                                                    child: Text(
                                                      slotsList[index],
                                                      style: GoogleFonts.livvic(
                                                        color: selected
                                                            ? Colors.white
                                                            : null,
                                                        fontSize: 15,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          )
                                        ],
                                      ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  /*  const SizedBox(height: 25),
                  Text("Blocked Slot",
                      style: GoogleFonts.zcoolXiaoWei(
                          color: themeColor, fontSize: 18)),
                  const SizedBox(height: 15),
                  StaggeredGrid.extent(
                    maxCrossAxisExtent: 170,
                    mainAxisSpacing: 15,
                    crossAxisSpacing: 15,
                    children: [
                      ...List.generate(
                        widget.blockedSlotModelList.length,
                        (index) {
                          final currentBlock =
                              widget.blockedSlotModelList[index];
                          DateTime blockedDateTime =
                              currentBlock.blockedDateTime;

                          String str =
                              '${blockedDateTime.hour.toString().padLeft(2, "0")}:${blockedDateTime.minute.toString().padLeft(2, "0")} ${blockedDateTime.hour < 12 ? 'AM' : 'PM'} - ${blockedDateTime.add(Duration(minutes: currentBlock.slotGap)).hour.toString().padLeft(2, "0")}:${blockedDateTime.add(Duration(minutes: currentBlock.slotGap)).minute.toString().padLeft(2, "0")} ${blockedDateTime.add(Duration(minutes: currentBlock.slotGap)).hour < 12 ? 'AM' : 'PM'}';
                          return InkWell(
                            onLongPress: () async {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  bool loading = false;
                                  return StatefulBuilder(
                                    builder: (context, setState2) {
                                      return AlertDialog(
                                        title: const Text("Un-Block"),
                                        content: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                                "Date: ${blockedDateTime.convertToDDMMYY()}"),
                                            if (!currentBlock.dateBlock)
                                              Text('Time: $str')
                                          ],
                                        ),
                                        actions: loading
                                            ? [
                                                const SizedBox(
                                                  height: 25,
                                                  width: 25,
                                                  child: Center(
                                                    child:
                                                        CircularProgressIndicator(
                                                            strokeWidth: 3.5),
                                                  ),
                                                )
                                              ]
                                            : [
                                                TextButton(
                                                  onPressed: () async {
                                                    if (loading) return;

                                                    try {
                                                      setState2(() {
                                                        loading = true;
                                                      });
                                                      await FBFireStore.blocked
                                                          .doc(currentBlock
                                                              .docId)
                                                          .delete();
                                                      if (context.mounted) {
                                                        setState2(() {
                                                          loading = false;
                                                        });
                                                        Navigator.of(context)
                                                            .pop();
                                                      }
                                                    } on Exception catch (e) {
                                                      setState2(() {
                                                        loading = false;
                                                      });
                                                      debugPrint(e.toString());
                                                    }
                                                  },
                                                  child: const Text('Unblock'),
                                                ),
                                                TextButton(
                                                  onPressed: () {
                                                    Navigator.of(context).pop();
                                                  },
                                                  child: const Text('Cancel'),
                                                ),
                                              ],
                                      );
                                    },
                                  );
                                },
                              );
                            },
                            child: Container(
                              // width: ,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(7),
                                border: Border.all(
                                    color: const Color.fromARGB(
                                        255, 177, 177, 177)),
                              ),
                              height: 65,
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: Center(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(blockedDateTime.convertToDDMMYY()),
                                    if (!currentBlock.dateBlock) Text(str),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      )
                    ],
                  )
                */
                ],
              )
            : const Center(child: Text("Unable to fetch data")),
      ],
    );
  }
}
