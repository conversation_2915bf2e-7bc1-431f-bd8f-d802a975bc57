import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_date_range_picker/flutter_date_range_picker.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/shared/router.dart';
import 'package:wedding_super_admin/views/common/header_search_feild.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';
import '../../models/bookings_model.dart';
import '../../shared/firebase.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';
import '../common/table_header.dart';
import 'widget/booking_tile_card.dart';

class BookingsPage extends StatefulWidget {
  const BookingsPage({super.key});

  @override
  State<BookingsPage> createState() => _BookingsPageState();
}

class _BookingsPageState extends State<BookingsPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (hCrl) {
        return BookingSubPage(
            key: ValueKey(DateTime.now()), bookingList: hCrl.bookingList);
      },
    );
  }
}

class BookingSubPage extends StatefulWidget {
  const BookingSubPage({super.key, required this.bookingList});
  final List<BookingsModel> bookingList;

  @override
  State<BookingSubPage> createState() => _BookingSubPageState();
}

class _BookingSubPageState extends State<BookingSubPage> {
  final searchCtrl = TextEditingController();
  List<BookingsModel> filteredBookingsList = [];
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? todaysBookingsStream;
  // List<VendorOrderModel> filteredVendorOrdersList = [];
  List<BookingsModel> dropdownfilteredBookingsList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  getSearchedData(String value) {
    filteredBookingsList.clear();
    if (value.isEmpty) {
      if (selectedDropdown != null) {
        filteredBookingsList.addAll(dropdownfilteredBookingsList);
      } else {
        filteredBookingsList.addAll(widget.bookingList);
      }
    } else {
      List<BookingsModel> temp = widget.bookingList
          .where((element) =>
              (element.fullnameAtBooking?.contains(value) ?? false))
          .toList();
      filteredBookingsList.addAll(temp);
    }
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    getSearchedData('');
  }

  todaysBookings() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysBookingsStream?.cancel();
      todaysBookingsStream = FBFireStore.bookings
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredBookingsList.clear();
        filteredBookingsList
            .addAll(event.docs.map((e) => BookingsModel.fromSnap(e)));
        filteredBookingsList
            .sort((a, b) => b.bookingDate.compareTo(a.bookingDate));
        dropdownfilteredBookingsList.clear();
        dropdownfilteredBookingsList.addAll(filteredBookingsList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyBookingsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyBookingSnap = await FBFireStore.bookings
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredBookingsList.clear();
      filteredBookingsList
          .addAll(weeklyBookingSnap.docs.map((e) => BookingsModel.fromSnap(e)));
      filteredBookingsList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredBookingsList.clear();
      dropdownfilteredBookingsList.addAll(filteredBookingsList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyBookingsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyBookingSnap = await FBFireStore.bookings
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredBookingsList.clear();
      filteredBookingsList.addAll(
          monthlyBookingSnap.docs.map((e) => BookingsModel.fromSnap(e)));
      filteredBookingsList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredBookingsList.clear();
      dropdownfilteredBookingsList.addAll(filteredBookingsList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customBookingsStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customBookingsSnap = await FBFireStore.bookings
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredBookingsList.clear();
      filteredBookingsList.addAll(
          customBookingsSnap.docs.map((e) => BookingsModel.fromSnap(e)));
      filteredBookingsList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredBookingsList.clear();
      dropdownfilteredBookingsList.addAll(filteredBookingsList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
            pageTile: 'Booking Management',
            pagesubTile: 'Manage and track all bookings'),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PageHeaderWithButton(
                  title: 'Bookings',
                  button: false,
                  onPressed: () {},
                  showTrailing: true,
                  trailing: Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          context.push(Routes.bookingSetting);
                        },
                        icon: const Icon(Icons.edit),
                      ),
                      // const SizedBox(width: 10),
                      // OutlinedButton(
                      //     style: OutlinedButton.styleFrom(
                      //       elevation: 0,
                      //       foregroundColor: themeColor,
                      //       side: const BorderSide(color: themeColor),
                      //       shape: RoundedRectangleBorder(
                      //           borderRadius: BorderRadius.circular(4)),
                      //     ),
                      //     onPressed: () {},
                      //     child: Padding(
                      //       padding: const EdgeInsets.symmetric(vertical: 8.0),
                      //       child: Text("History",
                      //           style: GoogleFonts.livvic(
                      //               letterSpacing: 1.3,
                      //               fontSize: 14,
                      //               fontWeight: FontWeight.w500)),
                      //     )),
                      const SizedBox(width: 10),
                      ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: themeColor,
                          elevation: 0,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4)),
                          // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                        ),
                        onPressed: () async {
                          context.push('${Routes.booking}/${null}');
                        },
                        icon: const Icon(
                          CupertinoIcons.calendar_badge_plus,
                          size: 20,
                        ),
                        label: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text("Booking",
                              style: GoogleFonts.livvic(
                                  letterSpacing: 1.3,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500)),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 25),
                Row(
                  children: [
                    SizedBox(
                      width: 400,
                      child: SearchField(
                        searchController: searchCtrl,
                        onChanged: (value) {
                          getSearchedData(value.toLowerCase().trim());
                        },
                      ),
                    ),
                    SizedBox(width: 15),
                    SizedBox(
                      width: 150,
                      child: DropdownButtonHideUnderline(
                          child: DropdownButtonFormField(
                        hint: Text(
                          'Filter',
                          style: TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        value: selectedDropdown,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                              borderSide:
                                  const BorderSide(color: Color(0xffede2de)),
                              borderRadius: BorderRadius.circular(7)),
                          enabledBorder: OutlineInputBorder(
                              borderSide:
                                  const BorderSide(color: Color(0xffede2de)),
                              borderRadius: BorderRadius.circular(7)),
                          focusedBorder: OutlineInputBorder(
                              borderSide:
                                  const BorderSide(color: Color(0xffede2de)),
                              borderRadius: BorderRadius.circular(7)),
                        ),
                        items: historyDropdownList
                            .map((e) =>
                                DropdownMenuItem(value: e, child: Text(e)))
                            .toList(),
                        onChanged: (value) async {
                          selectedDropdown = value;
                          searchCtrl.clear();
                          if (value == historyDropdownList[0]) {
                            todaysBookings();
                          } else if (value == historyDropdownList[1]) {
                            weeklyBookingsStream();
                          } else if (value == historyDropdownList[2]) {
                            monthlyBookingsStream();
                          } else if (value == historyDropdownList[3]) {
                            showDialog(
                              context: context,
                              builder: (context) {
                                DateRange? selectedDateRange;
                                return StatefulBuilder(
                                  builder: (context, setState2) {
                                    return AlertDialog(
                                      backgroundColor: Colors.white,
                                      surfaceTintColor: Colors.white,
                                      content: SizedBox(
                                          height: 400,
                                          width: 600,
                                          child: DateRangePickerWidget(
                                            minDate: DateTime(2010),
                                            maxDate: DateTime.now(),
                                            onDateRangeChanged: (value) {
                                              selectedDateRange = value;
                                              setState2(() {});
                                            },
                                          )),
                                      actions: [
                                        TextButton(
                                          onPressed: () {
                                            // print("Range confirmed: $_selectedRange");
                                            if (selectedDateRange != null) {
                                              selEndDate =
                                                  selectedDateRange!.end;
                                              selstartDate =
                                                  selectedDateRange!.start;
                                              customBookingsStream();
                                            }
                                            Navigator.of(context).pop();
                                          },
                                          child: Text("Confirm"),
                                        ),
                                      ],
                                    );
                                  },
                                );
                              },
                            );
                          }
                        },
                      )),
                    ),
                    if (selectedDropdown == historyDropdownList[3]) ...[
                      SizedBox(width: 20),
                      InkWell(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Date Range',
                              style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w400,
                                  color: Color.fromARGB(255, 92, 92, 92)),
                            ),
                            Text(
                              '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.w500),
                            )
                          ],
                        ),
                      ),
                    ],
                    if (selectedDropdown != null) ...[
                      SizedBox(width: 15),
                      ElevatedButton(
                          style: ElevatedButton.styleFrom(elevation: 0),
                          onPressed: () {
                            selectedDropdown = null;
                            searchCtrl.clear();
                            getSearchedData('');
                          },
                          child: Text("Clear"))
                    ]
                  ],
                ),

                const SizedBox(height: 25),

                BookingTableHeader(),
                if (filteredBookingsList.isEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom: BorderSide(color: dividerColor),
                            left: BorderSide(color: dividerColor),
                            right: BorderSide(color: dividerColor)),
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8))),
                    child: const Center(
                      child: Text(
                        'No Data Available',
                        style: TextStyle(
                            color: Color(0xff737373),
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
                ...List.generate(
                  filteredBookingsList.length,
                  (index) {
                    final isLast = index == filteredBookingsList.length - 1;
                    final bookingData = filteredBookingsList[index];
                    final teamCtrl = Get.find<HomeCtrl>().teamMembers;
                    final consultant = teamCtrl.firstWhereOrNull(
                        (element) => element.docId == bookingData.teamMemberId);
                    final styler = teamCtrl.firstWhereOrNull(
                        (element) => element.docId == bookingData.stylerId);
                    return InkWell(
                      highlightColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      onTap: () {
                        context.push('${Routes.booking}/${bookingData.docID}');
                      },
                      child: BookingTile(
                          index: index,
                          isLast: isLast,
                          bookingData: bookingData,
                          consultant: consultant,
                          styler: styler),
                    );
                  },
                )

                // filteredList.isNotEmpty
                //     ? StaggeredGrid.extent(
                //         maxCrossAxisExtent: 377,
                //         crossAxisSpacing: 25,
                //         mainAxisSpacing: 25,
                //         children: [
                //           ...List.generate(
                //             filteredList.length,
                //             (index) {
                //               final bookingData = filteredList[index];
                //               return BookingCard(bookingsModel: bookingData);
                //             },
                //           )
                //         ],
                //       )
                //     : const Center(
                //         child: Text('No Bookings Currently'),
                //       )
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class BookingTableHeader extends StatelessWidget {
  const BookingTableHeader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: dashboardSelectedColor,
          border: Border.all(color: dividerColor),
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8))

          // color: const Color.fromARGB(255, 228, 228, 228),
          // color: themeColor,
          // color: const Color.fromARGB(255, 177, 139, 86),
          // color: tableHeaderColor,
          // borderRadius: BorderRadius.circular(4),
          ),
      child: Row(
        children: [
          const SizedBox(
            width: 80,
            child: Text(
              'Sr No',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                letterSpacing: 1.2,
              ),
            ),
          ),

          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Booking Date')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Slot Time & Gap')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'User')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Consultants')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Styler')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Status')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Created At')),
          const SizedBox(width: 5),
          // const SizedBox(width: 5),
          // const Expanded(child: TableHeaderText(headerName: 'Completed On')),
          Opacity(
            opacity: 0,
            child: IgnorePointer(
              ignoring: true,
              child: SizedBox(
                width: 60,
                child: IconButton(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  onPressed: () {},
                  icon: const Icon(
                    Icons.delete,
                    color: themeColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
