import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_date_range_picker/flutter_date_range_picker.dart';
import 'package:get/get.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import 'package:wedding_super_admin/models/vendor_order_model.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';

import '../../controller/home_ctrl.dart';
import '../../shared/const.dart';
import '../../shared/theme.dart';
import '../common/header_search_feild.dart';
import '../common/top_sections.dart';
import 'widget/vendor_order_table_header.dart';
import 'widget/vendor_order_tile.dart';

class VendorOrders extends StatefulWidget {
  const VendorOrders({super.key});

  @override
  State<VendorOrders> createState() => _VendorOrdersState();
}

class _VendorOrdersState extends State<VendorOrders> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (hCtrl) {
      return VendorOrderPage(
          allVendorOrders: hCtrl.allVendorOrderList, allVendors: hCtrl.vendors);
    });
  }
}

class VendorOrderPage extends StatefulWidget {
  const VendorOrderPage({
    super.key,
    required this.allVendorOrders,
    required this.allVendors,
  });

  final List<VendorOrderModel> allVendorOrders;
  final List<VendorModel> allVendors;

  @override
  State<VendorOrderPage> createState() => _VendorOrderPageState();
}

class _VendorOrderPageState extends State<VendorOrderPage> {
  final searchCtrl = TextEditingController();
  List<VendorOrderModel> filteredVendorOrdersList = [];
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      todaysVendorOrdersStream;
  // List<VendorOrderModel> filteredVendorOrdersList = [];
  List<VendorOrderModel> dropdownfilteredVednorOrdersList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    getSearchedData('');
  }

  getSearchedData(String str) {
    filteredVendorOrdersList.clear();
    if (str.isEmpty) {
      if (selectedDropdown != null) {
        filteredVendorOrdersList.addAll(dropdownfilteredVednorOrdersList);
      } else {
        filteredVendorOrdersList.addAll(widget.allVendorOrders);
      }
    } else {
      filteredVendorOrdersList.addAll(widget.allVendorOrders.where((element) {
        return element.orderId.toLowerCase().contains(str) ||
            (widget.allVendors
                    .firstWhereOrNull((e) => e.docId == element.vendorId)
                    ?.name
                    .toLowerCase()
                    .contains(str) ??
                false);
      }));
    }
    filteredVendorOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    setState(() {});
  }

  todaysVendorOrders() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      print('currentStartDate: ${currentStartDate.goodDayDate()}');
      print('currentEndDate: ${currentEndDate.goodDayDate()}');
      todaysVendorOrdersStream?.cancel();
      todaysVendorOrdersStream = FBFireStore.vendorOrder
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredVendorOrdersList.clear();
        filteredVendorOrdersList
            .addAll(event.docs.map((e) => VendorOrderModel.fromSnap(e)));
        filteredVendorOrdersList
            .sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredVednorOrdersList.clear();
        dropdownfilteredVednorOrdersList.addAll(filteredVendorOrdersList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyVendorOrdersStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyOrderSnap = await FBFireStore.vendorOrder
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredVendorOrdersList.clear();
      filteredVendorOrdersList.addAll(
          weeklyOrderSnap.docs.map((e) => VendorOrderModel.fromSnap(e)));
      filteredVendorOrdersList
          .sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredVednorOrdersList.clear();
      dropdownfilteredVednorOrdersList.addAll(filteredVendorOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyVendorOrdersStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyOrdersSnap = await FBFireStore.vendorOrder
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredVendorOrdersList.clear();
      filteredVendorOrdersList.addAll(
          monthlyOrdersSnap.docs.map((e) => VendorOrderModel.fromSnap(e)));
      filteredVendorOrdersList
          .sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredVednorOrdersList.clear();
      dropdownfilteredVednorOrdersList.addAll(filteredVendorOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customvendorOrdersStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customOrdersSnap = await FBFireStore.vendorOrder
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredVendorOrdersList.clear();
      filteredVendorOrdersList.addAll(
          customOrdersSnap.docs.map((e) => VendorOrderModel.fromSnap(e)));
      filteredVendorOrdersList
          .sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredVednorOrdersList.clear();
      dropdownfilteredVednorOrdersList.addAll(filteredVendorOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void dispose() {
    searchCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
          pageTile: 'Vendor Order',
          pagesubTile: 'Manage vendor order',
        ),
        Expanded(
          child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      SizedBox(
                        width: 400,
                        child: SearchField(
                          searchController: searchCtrl,
                          onChanged: (value) {
                            getSearchedData(value.toLowerCase().trim());
                          },
                        ),
                      ),
                      SizedBox(width: 15),
                      SizedBox(
                        width: 150,
                        child: DropdownButtonHideUnderline(
                            child: DropdownButtonFormField(
                          hint: Text(
                            'Filter',
                            style: TextStyle(
                              fontSize: 14,
                            ),
                          ),
                          value: selectedDropdown,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                                borderSide:
                                    const BorderSide(color: Color(0xffede2de)),
                                borderRadius: BorderRadius.circular(7)),
                            enabledBorder: OutlineInputBorder(
                                borderSide:
                                    const BorderSide(color: Color(0xffede2de)),
                                borderRadius: BorderRadius.circular(7)),
                            focusedBorder: OutlineInputBorder(
                                borderSide:
                                    const BorderSide(color: Color(0xffede2de)),
                                borderRadius: BorderRadius.circular(7)),
                          ),
                          items: historyDropdownList
                              .map((e) =>
                                  DropdownMenuItem(value: e, child: Text(e)))
                              .toList(),
                          onChanged: (value) async {
                            selectedDropdown = value;
                            searchCtrl.clear();
                            if (value == historyDropdownList[0]) {
                              todaysVendorOrders();
                            } else if (value == historyDropdownList[1]) {
                              weeklyVendorOrdersStream();
                            } else if (value == historyDropdownList[2]) {
                              monthlyVendorOrdersStream();
                            } else if (value == historyDropdownList[3]) {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  DateRange? selectedDateRange;
                                  return StatefulBuilder(
                                    builder: (context, setState2) {
                                      return AlertDialog(
                                        backgroundColor: Colors.white,
                                        surfaceTintColor: Colors.white,
                                        content: SizedBox(
                                            height: 400,
                                            width: 600,
                                            child: DateRangePickerWidget(
                                              minDate: DateTime(2010),
                                              maxDate: DateTime.now(),
                                              onDateRangeChanged: (value) {
                                                selectedDateRange = value;
                                                setState2(() {});
                                              },
                                            )),
                                        actions: [
                                          TextButton(
                                            onPressed: () {
                                              // print("Range confirmed: $_selectedRange");
                                              if (selectedDateRange != null) {
                                                selEndDate =
                                                    selectedDateRange!.end;
                                                selstartDate =
                                                    selectedDateRange!.start;
                                                customvendorOrdersStream();
                                              }
                                              Navigator.of(context).pop();
                                            },
                                            child: Text("Confirm"),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                              );
                            }
                          },
                        )),
                      ),
                      if (selectedDropdown == historyDropdownList[3]) ...[
                        SizedBox(width: 20),
                        InkWell(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Date Range',
                                style: TextStyle(
                                    fontSize: 13,
                                    fontWeight: FontWeight.w400,
                                    color: Color.fromARGB(255, 92, 92, 92)),
                              ),
                              Text(
                                '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.w500),
                              )
                            ],
                          ),
                        ),
                      ],
                      if (selectedDropdown != null) ...[
                        SizedBox(width: 15),
                        ElevatedButton(
                            style: ElevatedButton.styleFrom(elevation: 0),
                            onPressed: () {
                              selectedDropdown = null;
                              searchCtrl.clear();
                              getSearchedData('');
                            },
                            child: Text("Clear"))
                      ]
                    ],
                  ),
                  const SizedBox(height: 20),
                  const VendorOrderTableHeader(),
                  if (filteredVendorOrdersList.isEmpty) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: const BoxDecoration(
                          border: Border(
                              bottom: BorderSide(color: dividerColor),
                              left: BorderSide(color: dividerColor),
                              right: BorderSide(color: dividerColor)),
                          borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(8))),
                      child: const Center(
                        child: Text(
                          'No Data Available',
                          style: TextStyle(
                              color: Color(0xff737373),
                              fontSize: 14.5,
                              fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                  ],
                  if (filteredVendorOrdersList.isNotEmpty)
                    ...List.generate(
                      filteredVendorOrdersList.length,
                      (index) {
                        return VendorOrderTile(
                            vendor: widget.allVendors.firstWhereOrNull(
                                (element) =>
                                    element.docId ==
                                    filteredVendorOrdersList[index].vendorId),
                            isLast:
                                index == (filteredVendorOrdersList.length - 1),
                            index: index,
                            vendororderModel: filteredVendorOrdersList[index]);
                      },
                    )
                ],
              )),
        ),
      ],
    );
  }
}
