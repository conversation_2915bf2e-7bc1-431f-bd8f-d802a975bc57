import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import '../../../models/vendor.dart';
import '../../../models/vendor_order_model.dart';
import '../../../shared/router.dart';
import '../../../shared/theme.dart';

class VendorOrderTile extends StatefulWidget {
  const VendorOrderTile(
      {super.key,
      required this.index,
      required this.vendororderModel,
      required this.isLast,
      required this.vendor});
  final int index;
  final VendorOrderModel vendororderModel;
  final VendorModel? vendor;
  final bool isLast;

  @override
  State<VendorOrderTile> createState() => _VendorOrderTileState();
}

class _VendorOrderTileState extends State<VendorOrderTile> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: () {
        context.push('${Routes.vendorOrder}/${widget.vendororderModel.docId}');
      },
      child: Container(
        decoration: BoxDecoration(
            border: const Border(
                bottom: BorderSide(color: dividerColor),
                left: BorderSide(color: dividerColor),
                right: BorderSide(color: dividerColor)),
            borderRadius: widget.isLast
                ? const BorderRadius.only(
                    bottomLeft: Radius.circular(8),
                    bottomRight: Radius.circular(8))
                : null),
        child: Row(
          children: [
            SizedBox(
              width: 80,
              child: Text((widget.index + 1).toString(),
                  textAlign: TextAlign.center),
            ),
            const SizedBox(width: 5),
            Expanded(child: Text(widget.vendororderModel.orderId)),

            const SizedBox(width: 5),
            Expanded(
                child:
                    Text(capilatlizeFirstLetter(widget.vendor?.name ?? '-'))),

            const SizedBox(width: 5),
            Expanded(
                child: Text(
                    widget.vendororderModel.orderProducts.length.toString())),
            const SizedBox(width: 5),
            Expanded(
                child: Text(
              widget.vendororderModel.status.toUpperCase(),
              style: const TextStyle(fontSize: 12),
            )),
            const SizedBox(width: 5),
            Expanded(
                child: Text(
                    '${widget.vendororderModel.createdAt.goodDayDate()}  ${widget.vendororderModel.createdAt.goodTime()}')),
            // const SizedBox(width: 5),
            // Opacity(
            //   opacity: 0,
            //   child: IgnorePointer(
            //     ignoring: true,
            //     child: SizedBox(
            //         width: 60,
            //         child: Transform.scale(
            //           scale: .65,
            //           child: CupertinoSwitch(
            //             value: true,
            //             onChanged: (value) {},
            //           ),
            //         )),
            //   ),
            // ),
            Opacity(
              opacity: 0,
              child: IgnorePointer(
                ignoring: true,
                child: SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {},
                    icon: const Icon(
                      Icons.edit,
                      size: 22,
                    ),
                  ),
                ),
              ),
            ),
            Opacity(
              opacity: 0,
              child: IgnorePointer(
                ignoring: true,
                child: SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {},
                    icon: const Icon(
                      Icons.delete,
                      color: themeColor,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
