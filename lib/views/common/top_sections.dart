import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controller/home_ctrl.dart';
import '../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../../shared/theme.dart';

class TopSectionOfPages extends StatefulWidget {
  const TopSectionOfPages(
      {super.key, required this.pageTile, required this.pagesubTile});
  final String pageTile;
  final String pagesubTile;
  @override
  State<TopSectionOfPages> createState() => _TopSectionOfPagesState();
}

class _TopSectionOfPagesState extends State<TopSectionOfPages> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (hCtrl) {
        return Container(
          decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: dividerColor))),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.pageTile,
                    style: const TextStyle(
                        fontSize: 20,
                        color: Color(0xff333333),
                        fontWeight: FontWeight.w600),
                  ),
                  Text(
                    widget.pagesubTile,
                    style: const TextStyle(
                        fontSize: 15,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.w500),
                  ),
                ],
              ),
              const Spacer(),
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: themeColor,
                ),
                child: const Row(
                  children: [
                    Icon(
                      CupertinoIcons.add_circled,
                      color: Colors.white,
                      size: 16,
                    ),
                    SizedBox(width: 7),
                    Text(
                      'Quick Actions',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        // letterSpacing: 1,
                      ),
                    )
                  ],
                ),
              ),
              const SizedBox(width: 15),
              Container(
                  padding: const EdgeInsets.all(9),
                  decoration: const BoxDecoration(
                      shape: BoxShape.circle, color: Color(0xfffef8f6)),
                  child: const Icon(
                    CupertinoIcons.person,
                    color: themeColor,
                    size: 20,
                  )),
              const SizedBox(width: 11),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    capilatlizeFirstLetter(hCtrl.currentUser?.name ?? '-'),
                    style: const TextStyle(
                        color: Color(0xff333333),
                        fontSize: 13,
                        fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 1),
                  Text(
                    FBAuth.auth.currentUser?.email ?? '-',
                    style: const TextStyle(
                      color: Color(0xff737373),
                      fontSize: 12,
                    ),
                  ),
                ],
              )
            ],
          ),
        );
      },
    );
  }
}
