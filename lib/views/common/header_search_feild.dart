import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../shared/theme.dart';

class SearchField extends StatelessWidget {
  const SearchField({
    super.key,
    required this.searchController,
    this.onChanged,
    this.hintText,
  });
  final TextEditingController searchController;
  final Function(String)? onChanged;
  final String? hintText;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 45,
      child: TextFormField(
        controller: searchController,
        cursorHeight: 20,
        // onChanged: (value) => Get.find<HomeCtrl>().update(),
        onChanged: onChanged,
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(),
          // fillColor: const Color.fromARGB(255, 252, 237, 214),
          // fillColor: dashboardColor.withOpacity(.7),
          // filled: true,
          prefixIcon: const Icon(
            CupertinoIcons.search,
            size: 22,
            color: themeColor,
          ),
          border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xffede2de)),
              borderRadius: BorderRadius.circular(7)),
          enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xffede2de)),
              borderRadius: BorderRadius.circular(7)),
          focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xffede2de)),
              borderRadius: BorderRadius.circular(7)),
          hintText: hintText ?? ' Search',
          hintStyle: TextStyle(
              color: Color(0xff737373),
              fontSize: 14.5,
              fontWeight: FontWeight.w500),
        ),
      ),
    );
  }
}
