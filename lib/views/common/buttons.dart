import 'package:flutter/material.dart';

import '../../shared/theme.dart';

class ButtonWithIcon extends StatelessWidget {
  const ButtonWithIcon(
      {super.key,
      required this.icon,
      required this.buttonName,
      required this.onTap});
  final IconData icon;
  final String buttonName;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          color: themeColor,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 7),
            Text(
              buttonName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                // letterSpacing: 1,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class ButtonWithOutIcon extends StatelessWidget {
  const ButtonWithOutIcon(
      {super.key, required this.buttonName, required this.onTap});
  final String buttonName;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          color: themeColor,
        ),
        child: Row(
          children: [
            Text(
              buttonName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                // letterSpacing: 1,
              ),
            )
          ],
        ),
      ),
    );
  }
}
