import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_date_range_picker/flutter_date_range_picker.dart';
import 'package:get/get.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/order_model.dart';
import 'package:wedding_super_admin/views/common/header_search_feild.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';

import 'package:wedding_super_admin/views/orders/widgets/order_tile.dart';

import '../../shared/const.dart';
import '../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../../shared/theme.dart';
import 'widgets/order_table_header.dart';

class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage> {
  // late final TabController _tabController;
  final searchCtrl = TextEditingController();

  @override
  void initState() {
    super.initState();
    // _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    searchCtrl.dispose();
    // _tabController.dispose();
    super.dispose();
  }

  String selectedSegment = 'waitingforapproval';

  @override
  Widget build(BuildContext context) {
    // final screenHeight = MediaQuery.sizeOf(context).height;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const TopSectionOfPages(
          pageTile: 'Order Management',
          pagesubTile: 'Process orders and track deliveries',
        ),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
            child: GetBuilder<HomeCtrl>(builder: (hCtrl) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /*   PageHeaderWithButton(
                      title: "Orders",
                      button: false,
                      onPressed: () {},
                    ),*/
                  const SizedBox(height: 10),
                  CupertinoSlidingSegmentedControl<String>(
                    padding: EdgeInsets.all(7),
                    backgroundColor: Color(0xfff6f5f4),

                    // borderColor: Colors.transparent,
                    // selectedColor: Colors.white,
                    // unselectedColor: Colors.transparent,
                    groupValue: selectedSegment,
                    // pressedColor: null,
                    children: {
                      'waitingforapproval': Text(
                        'Waiting for Approval',
                        style: TextStyle(
                            letterSpacing: .7,
                            color: selectedSegment == 'waitingforapproval'
                                ? const Color(0xff333333)
                                : const Color(0xff737373),
                            fontWeight: FontWeight.w600),
                      ),
                      'pendingorder': Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: 7, horizontal: 5),
                        child: Text(
                          'Pending Orders',
                          style: TextStyle(
                              letterSpacing: .7,
                              color: selectedSegment == 'pendingorder'
                                  ? const Color(0xff333333)
                                  : const Color(0xff737373),
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                      'history': Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: 7, horizontal: 5),
                        child: Text(
                          'History',
                          style: TextStyle(
                              letterSpacing: .7,
                              color: selectedSegment == 'history'
                                  ? const Color(0xff333333)
                                  : const Color(0xff737373),
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                    },
                    onValueChanged: (value) {
                      selectedSegment = value!;
                      setState(() {});
                    },
                  ),
                  const SizedBox(height: 20),
                  // SearchField(searchController: searchCtrl),
                  /*  Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            decoration: InputDecoration(
                                border: OutlineInputBorder(
                                    borderSide:
                                        const BorderSide(color: dividerColor),
                                    borderRadius: BorderRadius.circular(6)),
                                enabledBorder: OutlineInputBorder(
                                    borderSide:
                                        const BorderSide(color: dividerColor),
                                    borderRadius: BorderRadius.circular(6)),
                                focusedBorder: OutlineInputBorder(
                                    borderSide:
                                        const BorderSide(color: dividerColor),
                                    borderRadius: BorderRadius.circular(6)),
                                hintText: ' Search orders...',
                                hintStyle: const TextStyle(
                                    fontSize: 14, color: Color(0xff737373)),
                                prefixIcon: const Icon(
                                  CupertinoIcons.search,
                                  color: Color(0xff737373),
                                  size: 20,
                                )),
                          ),
                        ),
                      ],
                    ), */
                  // const SizedBox(height: 12),
                  if (selectedSegment == 'pendingorder')
                    OrderDisplayTilesWid(
                        key: ValueKey(DateTime.now()),
                        orders: hCtrl.pendingOrdersList),
                  if (selectedSegment == 'waitingforapproval')
                    OrderDisplayTilesWid(
                        key: ValueKey(DateTime.now()),
                        orders: hCtrl.userActionOrderList),
                  if (selectedSegment == 'history')
                    HistoryOrderDisplayWid(key: ValueKey(DateTime.now())),
                ],
              );
            }),
          ),
        ),
      ],
    );
  }
}

class OrderDisplayTilesWid extends StatefulWidget {
  const OrderDisplayTilesWid({
    super.key,
    required this.orders,
  });
  final List<OrderModel> orders;

  @override
  State<OrderDisplayTilesWid> createState() => _OrderDisplayTilesWidState();
}

class _OrderDisplayTilesWidState extends State<OrderDisplayTilesWid> {
  final searchCtrl = TextEditingController();
  List<OrderModel> filteredOrderList = [];
  getSearchedData() {
    filteredOrderList = searchCtrl.text.trim().isEmpty
        ? widget.orders
        : widget.orders
            .where(
                (element) => element.orderId.contains(searchCtrl.text.trim()))
            .toList();
    filteredOrderList.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    getSearchedData();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SearchField(
          searchController: searchCtrl,
          onChanged: (value) {
            getSearchedData();
          },
        ),
        SizedBox(height: 12),
        const OrderTableHeader(),
        if (filteredOrderList.isEmpty) ...[
          Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: dividerColor),
                      left: BorderSide(color: dividerColor),
                      right: BorderSide(color: dividerColor)),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8))),
              child: Center(
                  child: const Text(
                'No Data Available',
                style: TextStyle(
                    color: Color(0xff737373),
                    fontSize: 14.5,
                    fontWeight: FontWeight.w500),
              ))),
        ],
        if (filteredOrderList.isNotEmpty)
          ...List.generate(
            filteredOrderList.length,
            (index) {
              return OrderTile(
                  isLast: index == (filteredOrderList.length - 1),
                  index: index,
                  orderModel: filteredOrderList[index]);
            },
          )
      ],
    );
  }
}

class HistoryOrderDisplayWid extends StatefulWidget {
  const HistoryOrderDisplayWid({super.key});

  @override
  State<HistoryOrderDisplayWid> createState() => _HistoryOrderDisplayWidState();
}

class _HistoryOrderDisplayWidState extends State<HistoryOrderDisplayWid> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? todaysOrdersStream;
  final searchCtrl = TextEditingController();
  List<OrderModel> filteredOrdersList = [];
  List<OrderModel> dropdownfilteredOrdersList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todaysOrders();
    selectedDropdown = historyDropdownList[0];
  }

  todaysOrders() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysOrdersStream?.cancel();
      todaysOrdersStream = FBFireStore.orders
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredOrdersList.clear();
        filteredOrdersList
            .addAll(event.docs.map((e) => OrderModel.fromSnap(e)));
        filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredOrdersList.clear();
        dropdownfilteredOrdersList.addAll(filteredOrdersList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyOrdersStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyOrderSnap = await FBFireStore.orders
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredOrdersList.clear();
      filteredOrdersList
          .addAll(weeklyOrderSnap.docs.map((e) => OrderModel.fromSnap(e)));
      filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredOrdersList.clear();
      dropdownfilteredOrdersList.addAll(filteredOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyOrdersStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyOrdersSnap = await FBFireStore.orders
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredOrdersList.clear();
      filteredOrdersList
          .addAll(monthlyOrdersSnap.docs.map((e) => OrderModel.fromSnap(e)));
      filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredOrdersList.clear();
      dropdownfilteredOrdersList.addAll(filteredOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customOrdersStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customOrdersSnap = await FBFireStore.orders
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredOrdersList.clear();
      filteredOrdersList
          .addAll(customOrdersSnap.docs.map((e) => OrderModel.fromSnap(e)));
      filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredOrdersList.clear();
      dropdownfilteredOrdersList.addAll(filteredOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredOrdersList.clear();
    if (str.isEmpty) {
      filteredOrdersList.addAll(dropdownfilteredOrdersList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: 400,
              child: SearchField(
                searchController: searchCtrl,
                onChanged: (value) {
                  getSearchedData(value.trim());
                },
              ),
            ),
            SizedBox(width: 20),
            SizedBox(
              width: 150,
              child: DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
                  value: selectedDropdown,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                  ),
                  items: historyDropdownList
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  onChanged: (value) async {
                    selectedDropdown = value;
                    searchCtrl.clear();
                    if (value == historyDropdownList[0]) {
                      todaysOrders();
                    } else if (value == historyDropdownList[1]) {
                      weeklyOrdersStream();
                    } else if (value == historyDropdownList[2]) {
                      monthlyOrdersStream();
                    } else if (value == historyDropdownList[3]) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          DateRange? selectedDateRange;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                content: SizedBox(
                                    height: 400,
                                    width: 600,
                                    child: DateRangePickerWidget(
                                      minDate: DateTime(2010),
                                      maxDate: DateTime.now(),
                                      onDateRangeChanged: (value) {
                                        selectedDateRange = value;
                                        setState2(() {});
                                      },
                                    )),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      // print("Range confirmed: $_selectedRange");
                                      if (selectedDateRange != null) {
                                        selEndDate = selectedDateRange!.end;
                                        selstartDate = selectedDateRange!.start;
                                        customOrdersStream();
                                      }
                                      Navigator.of(context).pop();
                                    },
                                    child: Text("Confirm"),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      );
                    }
                  },
                ),
              ),
            ),
            if (selectedDropdown == historyDropdownList[3]) ...[
              SizedBox(width: 20),
              InkWell(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date Range',
                      style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                          color: Color.fromARGB(255, 92, 92, 92)),
                    ),
                    Text(
                      '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                    )
                  ],
                ),
              ),
            ]
          ],
        ),
        SizedBox(height: 12),
        const OrderTableHeader(),
        if (filteredOrdersList.isEmpty) ...[
          Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: dividerColor),
                      left: BorderSide(color: dividerColor),
                      right: BorderSide(color: dividerColor)),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8))),
              child: Center(
                  child: const Text(
                'No Data Available',
                style: TextStyle(
                    color: Color(0xff737373),
                    fontSize: 14.5,
                    fontWeight: FontWeight.w500),
              ))),
        ],
        if (filteredOrdersList.isNotEmpty)
          ...List.generate(
            filteredOrdersList.length,
            (index) {
              return OrderTile(
                isLast: index == (filteredOrdersList.length - 1),
                index: index,
                orderModel: filteredOrdersList[index],
              );
            },
          )
      ],
    );
  }
}
