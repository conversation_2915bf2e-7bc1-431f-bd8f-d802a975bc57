import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:readmore/readmore.dart';
import 'package:shopify_flutter/shopify_flutter.dart';
import 'package:wedding_super_admin/models/session_model.dart';

import '../../../models/display_order_details_model.dart';
import '../../../models/order_model.dart';
import '../../../models/teammodel.dart';
import '../../../models/user_model.dart';
import '../../../shared/const.dart';
import '../../../shared/methods.dart';
import '../../../shared/router.dart';
import '../../../shared/theme.dart';

class OrderTextHeader extends StatelessWidget {
  const OrderTextHeader({
    super.key,
    required this.text,
  });
  final String text;
  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14.5,
          color: Color(0xff3E3E3E)),
    );
  }
}

class OrderTextValue extends StatelessWidget {
  const OrderTextValue({
    super.key,
    required this.text,
  });
  final String text;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: const TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 13.5,
          color: Color(0xff6C6C6C)),
    );
  }
}

class TransactionTile extends StatefulWidget {
  const TransactionTile({
    super.key,
    required this.index,
    required this.transactionData,
    this.onTap,
  });
  final int index;
  final Function()? onTap;
  final TransactionModel transactionData;
  @override
  State<TransactionTile> createState() => _TransactionTileState();
}

class _TransactionTileState extends State<TransactionTile> {
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(top: widget.index == 0 ? 0 : 12.0),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: widget.onTap,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: const Color(0xfff8fafc),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(color: Color(0xffe5ebf2))),
                  child: Icon(
                    widget.transactionData.isDebit
                        ? CupertinoIcons.arrow_down_right
                        : CupertinoIcons.arrow_up_left,
                    size: 19,
                    color: const Color(0xff6C6C6C),
                  ),
                ),
                SizedBox(width: 15),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      capilatlizeFirstLetter(widget.transactionData.note),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xff0e1729)),
                    ),
                    SizedBox(height: 5),
                    Text.rich(
                      TextSpan(
                        children: [
                          const TextSpan(
                              text: 'Transaction ID: ',
                              style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xff6c7b91))),
                          TextSpan(
                              text: widget.transactionData.transactionId ?? '-',
                              style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 15,
                                  color: Color(0xff6c7b91))),
                        ],
                      ),
                    ),
                    SizedBox(height: 5),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 2.5, horizontal: 15),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              color: widget.transactionData.isPaid
                                  ? Colors.lightGreen.withValues(alpha: .1)
                                  : Colors.red.withValues(alpha: .1)),
                          child: Text(
                            widget.transactionData.isPaid
                                ? 'Completed'
                                : 'Requested',
                            style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 13,
                                color: widget.transactionData.isPaid
                                    ? Colors.green
                                    : Colors.redAccent),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          widget.transactionData.isPaid
                              ? "${widget.transactionData.paymentTime?.goodDayDate() ?? "-"} ${widget.transactionData.paymentTime?.goodTime() ?? ""}"
                              : "${widget.transactionData.createdAt.goodDayDate()} ${widget.transactionData.createdAt.goodTime()}",
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Color(0xff6c7b91),
                            fontSize: 13.5,
                          ),
                        )
                      ],
                    )
                  ],
                ),
                SizedBox(width: 15),
                Spacer(),
                Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(
                          text: '\$',
                          style: TextStyle(
                              fontSize: 13, fontWeight: FontWeight.w600)),
                      TextSpan(
                          text: widget.transactionData.amount.toString(),
                          style: const TextStyle(
                              fontSize: 15.5, fontWeight: FontWeight.w600)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}

class DeliveryDetailTile extends StatefulWidget {
  const DeliveryDetailTile({
    super.key,
    required this.index,
    required this.allOrderedProductsLength,
    required this.deliveryDetail,
    this.onTap,
    this.onDelete,
  });
  final int index;
  final Function()? onTap;
  final Function()? onDelete;
  final DeliveryDetailsModel deliveryDetail;
  final int allOrderedProductsLength;
  @override
  State<DeliveryDetailTile> createState() => _DeliveryDetailTileState();
}

class _DeliveryDetailTileState extends State<DeliveryDetailTile> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: widget.index == 0 ? 0 : 12.0),
      child: InkWell(
          onTap: widget.onTap,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: const Color(0xfff8fafc),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(color: Color(0xffe5ebf2))),
                  child: Icon(
                    CupertinoIcons.cube_box,
                    size: 19,
                    color: const Color(0xff6C6C6C),
                  ),
                ),
                SizedBox(width: 15),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      capilatlizeFirstLetter(
                          widget.deliveryDetail.deliveryPartner),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xff0e1729)),
                    ),
                    SizedBox(height: 5),
                    Text.rich(
                      TextSpan(
                        children: [
                          const TextSpan(
                              text: 'Transaction ID: ',
                              style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xff6c7b91))),
                          TextSpan(
                              text: widget.deliveryDetail.trackingId,
                              style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 15,
                                  color: Color(0xff6c7b91))),
                        ],
                      ),
                    ),
                    SizedBox(height: 5),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 2.5, horizontal: 15),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              color: widget.deliveryDetail.isDelivered
                                  ? Colors.lightGreen.withValues(alpha: .1)
                                  : Colors.redAccent.withValues(alpha: .1)),
                          child: Text(
                            widget.deliveryDetail.isDelivered
                                ? 'Delivered'
                                : 'Out for delivery',
                            style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 13,
                                color: widget.deliveryDetail.isDelivered
                                    ? Colors.green
                                    : Colors.redAccent),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          widget.deliveryDetail.isDelivered
                              ? "${widget.deliveryDetail.deliveredOn?.goodDayDate() ?? "-"} ${widget.deliveryDetail.deliveredOn?.goodTime() ?? ""}"
                              : "${widget.deliveryDetail.createdAt.goodDayDate()} ${widget.deliveryDetail.createdAt.goodTime()}",
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Color(0xff6c7b91),
                            fontSize: 13.5,
                          ),
                        )
                      ],
                    )
                  ],
                ),
                SizedBox(width: 15),
                Spacer(),
                Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(
                          text: '\$',
                          style: TextStyle(
                              fontSize: 13, fontWeight: FontWeight.w600)),
                      TextSpan(
                          text: widget.deliveryDetail.charges.toString(),
                          style: const TextStyle(
                              fontSize: 15.5, fontWeight: FontWeight.w600)),
                    ],
                  ),
                ),
              ],
            ),
          )),
    );
  }
}

class MainScreenOrderProductTile extends StatefulWidget {
  const MainScreenOrderProductTile({
    super.key,
    required this.index,
    required this.displayOrderData,
    this.onDelete,
    required this.wanttoEdit,
    this.onDateChange,
    this.onSellingPriceChange,
    this.onQtyChange,
    this.onAdminNoteChange,
  });
  final int index;
  final DisplayProductsDetails displayOrderData;
  final Function()? onDelete;
  final Function()? onDateChange;
  final bool wanttoEdit;
  final Function(String)? onSellingPriceChange;
  final Function(String)? onQtyChange;
  final Function(String)? onAdminNoteChange;
  @override
  State<MainScreenOrderProductTile> createState() =>
      _MainScreenOrderProductTileState();
}

class _MainScreenOrderProductTileState
    extends State<MainScreenOrderProductTile> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(color: Color.fromARGB(255, 225, 225, 225)))),
      padding: EdgeInsets.symmetric(
        vertical: 10,
        horizontal: 18,
      ),
      child: Row(
        // crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 55,
            child: Text(
              '${widget.index + 1}',
              style: GoogleFonts.mulish(
                  fontSize: 14.5, color: const Color(0xff3E3E3E)),
            ),
          ),
          const SizedBox(width: 10),
          Container(
            height: 50,
            width: 50,
            clipBehavior: Clip.antiAlias,
            decoration: const BoxDecoration(shape: BoxShape.circle),
            child: Image.network(
              widget.displayOrderData.variant.images.first,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 50,
                  width: 50,
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.black38),
                      shape: BoxShape.circle),
                  child: const Icon(
                    CupertinoIcons.photo,
                    size: 17,
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Text(
              widget.displayOrderData.product.sku.trim().isNotEmpty
                  ? widget.displayOrderData.product.sku
                  : '-',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: GoogleFonts.mulish(
                  fontSize: 13.5, color: const Color(0xff6C6C6C)),
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Text(
              capilatlizeFirstLetter(widget.displayOrderData.variant.lowerName),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: GoogleFonts.mulish(
                  fontSize: 14.5, color: const Color(0xff3E3E3E)),
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: InkWell(
              splashColor: Colors.transparent,
              hoverColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () {
                context.push(
                    '${Routes.vendor}/${widget.displayOrderData.vendorData.docId}');
              },
              child: Text(
                capilatlizeFirstLetter(widget.displayOrderData.vendorData.name),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.mulish(
                    fontSize: 14.5, color: const Color(0xff3E3E3E)),
              ),
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Text(
              capilatlizeFirstLetter(
                  widget.displayOrderData.orderData.purchasePrice.toString()),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: GoogleFonts.mulish(
                  fontSize: 14.5, color: const Color(0xff3E3E3E)),
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: IgnorePointer(
              ignoring: !widget.wanttoEdit,
              child: TextFormField(
                  style: GoogleFonts.mulish(
                      fontSize: 14.5, color: const Color(0xff3E3E3E)),
                  decoration: widget.wanttoEdit
                      ? inpDecor()
                      : InputDecoration(
                          border:
                              OutlineInputBorder(borderSide: BorderSide.none),
                        ),
                  controller: TextEditingController(
                      text: widget.displayOrderData.orderData.sellingPrice
                          .toString()),
                  onChanged: widget.onSellingPriceChange),
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: IgnorePointer(
              ignoring: !widget.wanttoEdit,
              child: TextFormField(
                style: GoogleFonts.mulish(
                    fontSize: 14.5, color: const Color(0xff3E3E3E)),
                decoration: widget.wanttoEdit
                    ? inpDecor()
                    : InputDecoration(
                        border: OutlineInputBorder(borderSide: BorderSide.none),
                      ),
                controller: TextEditingController(
                    text: widget.displayOrderData.orderData.qty.toString()),
                onChanged: widget.onQtyChange,
              ),
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: ReadMoreText(
              widget.displayOrderData.orderData.userNote,
              trimMode: TrimMode.Line,
              trimLines: 2,
              colorClickableText: Colors.red,
              trimCollapsedText: 'more',
              trimExpandedText: 'less',
              moreStyle: GoogleFonts.mulish(
                  fontSize: 14.5, color: const Color(0xff3E3E3E)),
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Text(
              widget.displayOrderData.orderData.currentStatus ?? '-',
              style: GoogleFonts.mulish(
                  fontSize: 14.5, color: const Color(0xff3E3E3E)),
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: !widget.wanttoEdit
                ? ReadMoreText(
                    widget.displayOrderData.orderData.adminNote,
                    trimMode: TrimMode.Line,
                    trimLines: 2,
                    colorClickableText: Colors.red,
                    trimCollapsedText: 'more',
                    trimExpandedText: 'less',
                    moreStyle: GoogleFonts.mulish(
                        fontSize: 14.5, color: const Color(0xff3E3E3E)),
                  )
                : IgnorePointer(
                    ignoring: !widget.wanttoEdit,
                    child: TextFormField(
                      style: GoogleFonts.mulish(
                          fontSize: 14.5, color: const Color(0xff3E3E3E)),
                      decoration: widget.wanttoEdit
                          ? inpDecor()
                          : InputDecoration(
                              border: OutlineInputBorder(
                                  borderSide: BorderSide.none),
                            ),
                      controller: TextEditingController(
                          text: widget.displayOrderData.orderData.adminNote
                              .toString()),
                      onChanged: widget.onAdminNoteChange,
                    ),
                  ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: IgnorePointer(
              ignoring: !widget.wanttoEdit,
              child: InkWell(
                onTap: widget.onDateChange,
                child: TextFormField(
                  style: GoogleFonts.mulish(
                      fontSize: 14.5, color: const Color(0xff3E3E3E)),
                  decoration: widget.wanttoEdit
                      ? inpDecor()
                      : InputDecoration(
                          border:
                              OutlineInputBorder(borderSide: BorderSide.none),
                        ),
                  enabled: false,
                  controller: TextEditingController(
                      text: widget.displayOrderData.orderData.deliveryDate
                              ?.convertToDDMMYY() ??
                          '-'),
                ),
              ),
            ),
          ),
          if (widget.wanttoEdit) ...[
            const SizedBox(width: 12),
            IconButton(
              onPressed: widget.onDelete,
              icon: const Icon(
                CupertinoIcons.delete,
                size: 18,
              ),
            ),
          ]
        ],
      ),
    );
  }
}

class UserDetailsContainer extends StatelessWidget {
  const UserDetailsContainer({
    super.key,
    required this.userModel,
    required this.orderModel,
  });

  final UserModel? userModel;
  final OrderModel orderModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        // padding: const EdgeInsets.symmetric(vertical: 17, horizontal: 17),
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color.fromARGB(255, 225, 225, 225)),
          // boxShadow: const [
          //   BoxShadow(
          //     color: Color.fromARGB(12, 0, 0, 0),
          //     blurRadius: 2,
          //     offset: Offset(1, 2),
          //   )
          // ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.maxFinite,
              decoration: BoxDecoration(
                  // color: tableHeaderColor,
                  borderRadius: BorderRadius.circular(5)),
              child: Row(
                children: [
                  Text(
                    'User Details',
                    style: GoogleFonts.zcoolXiaoWei(
                        fontSize: 18,
                        color: themeColor,
                        fontWeight: FontWeight.w500),
                  ),
                  const Spacer(),
                  if (userModel != null)
                    InkWell(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      onTap: () {
                        context.push('${Routes.user}/${userModel?.docId}');
                      },
                      child: Icon(
                        CupertinoIcons.arrow_up_right_square,
                        size: 25,
                        color: themeColor.withValues(alpha: .7),
                      ),
                    )
                ],
              ),
            ),
            // const Divider(color: Color.fromARGB(35, 0, 0, 0), thickness: .3),
            const SizedBox(height: 18),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(
                      shape: BoxShape.circle, color: Color(0xfffef8f6)),
                  child: const Icon(
                    CupertinoIcons.person,
                    color: themeColor,
                    size: 17,
                  ),
                ),
                const SizedBox(width: 10),
                Text(capilatlizeFirstLetter(userModel?.name ?? "-"),
                    style: GoogleFonts.mulish(color: const Color(0xff3E3E3E))),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(
                      shape: BoxShape.circle, color: Color(0xfffef8f6)),
                  child: const Icon(
                    CupertinoIcons.phone,
                    color: themeColor,
                    size: 17,
                  ),
                ),
                const SizedBox(width: 10),
                Text(userModel?.phone?.trim() ?? "-",
                    style: GoogleFonts.mulish(color: const Color(0xff3E3E3E))),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(
                      shape: BoxShape.circle, color: Color(0xfffef8f6)),
                  child: const Icon(
                    CupertinoIcons.mail,
                    color: themeColor,
                    size: 17,
                  ),
                ),
                // const Icon(
                //   CupertinoIcons.mail,
                //   size: 17,
                //   color: Color(0xff3E3E3E),
                // ),
                const SizedBox(width: 10),
                Text(userModel?.email.trim() ?? "-",
                    style: GoogleFonts.mulish(color: const Color(0xff3E3E3E))),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(
                      shape: BoxShape.circle, color: Color(0xfffef8f6)),
                  child: const Icon(
                    Icons.location_pin,
                    color: themeColor,
                    size: 17,
                  ),
                ),
                // const Icon(
                //   Icons.location_pin,
                //   size: 17,
                //   color: Color(0xff3E3E3E),
                // ),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(orderModel.address ?? "-",
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      style:
                          GoogleFonts.mulish(color: const Color(0xff3E3E3E))),
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }
}

class GeneralOrderDetails extends StatefulWidget {
  const GeneralOrderDetails({
    super.key,
    required this.orderModel,
    required this.createdByModel,
    required this.currentOrderStatus,
    required this.statusUpdatedByModel,
    this.changeOrderStatus,
    this.sessionModel,
  });

  final OrderModel orderModel;
  final TeamModel? createdByModel;
  final SessionModel? sessionModel;
  final String? currentOrderStatus;
  final TeamModel? statusUpdatedByModel;
  final Function()? changeOrderStatus;

  @override
  State<GeneralOrderDetails> createState() => _GeneralOrderDetailsState();
}

class _GeneralOrderDetailsState extends State<GeneralOrderDetails> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color.fromARGB(255, 225, 225, 225))),
      child: Column(
        children: [
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                      child: OrderDetailDataDisply(
                          title: 'Order ID', data: widget.orderModel.orderId)),
                  const SizedBox(width: 10),
                  Expanded(
                      child: OrderDetailDataDisply(
                          title: 'Created by',
                          data: widget.createdByModel?.name ?? '-')),
                  const SizedBox(width: 10),
                  Expanded(
                      child: OrderDetailDataDisply(
                          title: 'Order date',
                          data:
                              '${widget.orderModel.createdAt.goodDayDate()} ${widget.orderModel.createdAt.goodTime()}')),
                  const SizedBox(width: 10),
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Order status',
                        style: TextStyle(
                            fontSize: 13,
                            color: Color.fromARGB(255, 128, 128, 128),
                            fontWeight: FontWeight.w400),
                      ),
                      const SizedBox(height: 5),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 5, horizontal: 10),
                            decoration: BoxDecoration(
                              color: const Color(0xffFFEDD5),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Text(
                              widget.currentOrderStatus!,
                              style: const TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xff9A3412)),
                            ),
                          ),
                          const SizedBox(width: 10),
                          (widget.currentOrderStatus ==
                                      OrderStatus.userConfirmation ||
                                  (widget.currentOrderStatus ==
                                      OrderStatus.cancelled))
                              ? const SizedBox()
                              : InkWell(
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  splashColor: Colors.transparent,
                                  onTap: widget.changeOrderStatus,
                                  child: const Icon(Icons.edit,
                                      color: Color(0xff3E3E3E), size: 13),
                                )
                        ],
                      )
                    ],
                  )),
                  const SizedBox(width: 10),
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Session ID',
                        style: TextStyle(
                            fontSize: 13,
                            color: Color.fromARGB(255, 128, 128, 128),
                            fontWeight: FontWeight.w400),
                      ),
                      const SizedBox(height: 5),
                      Row(
                        children: [
                          Text(widget.sessionModel?.inquiryId ?? '-',
                              style: const TextStyle(
                                  fontSize: 15,
                                  color: Color.fromARGB(255, 35, 35, 35),
                                  fontWeight: FontWeight.w500)),
                          const SizedBox(width: 10),
                          widget.sessionModel == null
                              ? const SizedBox()
                              : InkWell(
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  splashColor: Colors.transparent,
                                  onTap: () {
                                    context.push(
                                        '${Routes.session}/${widget.sessionModel?.docId}');
                                  },
                                  child: const Icon(
                                      CupertinoIcons.arrow_up_right_square,
                                      color: themeColor,
                                      size: 17),
                                )
                        ],
                      ),
                    ],
                  )),
                ],
              ),
              const SizedBox(height: 15),
              Row(
                children: [
                  Expanded(
                      child: OrderDetailDataDisply(
                          title: 'User confirmed',
                          data: (widget.orderModel.userConfirmed != null)
                              ? widget.orderModel.userConfirmed != null
                                  ? 'Accepted'
                                  : 'Rejected'
                              : '-')),
                  const SizedBox(width: 10),
                  Expanded(
                      child: OrderDetailDataDisply(
                          title: 'User confirmed on',
                          data:
                              '${widget.orderModel.userConfirmedOn?.goodDayDate() ?? "-"} ${widget.orderModel.userConfirmedOn?.goodTime() ?? ""}')),
                  const SizedBox(width: 10),
                  Expanded(
                      child: OrderDetailDataDisply(
                          title: 'Status changed by',
                          data: capilatlizeFirstLetter(
                              widget.statusUpdatedByModel?.name ?? '-'))),
                  const SizedBox(width: 10),
                  Expanded(
                      child: OrderDetailDataDisply(
                          title: 'Status changed at',
                          data:
                              '${widget.orderModel.statusUpdatedAt?.goodDayDate() ?? "-"} ${widget.orderModel.statusUpdatedAt?.goodTime() ?? ""}')),
                  const SizedBox(width: 10),
                  Expanded(
                    child: OrderDetailDataDisply(
                        title: 'Order Type',
                        data: (widget.orderModel.isDirectOrder
                            ? 'Direct Order'
                            : 'In-direct Order')),
                  )
                ],
              ),
            ],
          )
        ],
      ),
    );
  }
}

class OrderDetailDataDisply extends StatelessWidget {
  const OrderDetailDataDisply({
    super.key,
    required this.title,
    required this.data,
    this.status = false,
    this.onTap,
  });
  final String title;
  final String data;
  final bool status;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
              fontSize: 13,
              color: Color.fromARGB(255, 128, 128, 128),
              fontWeight: FontWeight.w400),
        ),
        const SizedBox(height: 5),
        Row(
          children: [
            Text(data,
                style: const TextStyle(
                    fontSize: 15,
                    color: Color.fromARGB(255, 35, 35, 35),
                    fontWeight: FontWeight.w500)),
            if (status) ...[
              const SizedBox(width: 8),
              InkWell(
                hoverColor: Colors.transparent,
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: onTap,
                child:
                    const Icon(Icons.edit, color: Color(0xff3E3E3E), size: 13),
              )
            ]
          ],
        ),
      ],
    );
  }
}

class SelectProductInDialogForDelivery extends StatefulWidget {
  const SelectProductInDialogForDelivery({
    super.key,
    required this.allOrderedProducts,
    required this.selectedDeliveryProducts,
    required this.refresh,
    required this.allDeliveryDetails,
  });

  final List<DisplayProductsDetails> allOrderedProducts;
  final List<DeliveryProductData> selectedDeliveryProducts;
  final List<DeliveryDetailsModel> allDeliveryDetails;
  final Function refresh;

  @override
  State<SelectProductInDialogForDelivery> createState() =>
      _SelectProductInDialogForDeliveryState();
}

class _SelectProductInDialogForDeliveryState
    extends State<SelectProductInDialogForDelivery> {
  List<OrderProductData> deliveredOroutfordeliveryProducts = [];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getRequiredData();
  }

  getRequiredData() {
    deliveredOroutfordeliveryProducts = widget.allOrderedProducts
        .where((element) {
          int deliveredOroutfordeliveryQty = 0;
          final varaintDeliveries = widget.allDeliveryDetails
              .where((ele) => ele.dispatchedProduct
                  .map((e) => e.variantId)
                  .contains(element.orderData.variantId))
              .toList();

          for (var delivery in varaintDeliveries) {
            final deliveredQty = delivery.dispatchedProduct
                .firstWhere((e) => e.variantId == element.orderData.variantId)
                .qty;
            deliveredOroutfordeliveryQty += deliveredQty;
          }
          return (element.orderData.qty - deliveredOroutfordeliveryQty) <= 0;
          // return (element.orderData.currentStatus ==
          //         OrderProductStatus.delivered) ||
          //     (element.orderData.currentStatus ==
          //         OrderProductStatus.outfordeliver);
        })
        .map((e) => e.orderData)
        .toList();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'Delivery Products',
          style: TextStyle(fontSize: 18),
        ),
        Tooltip(
          message: 'Add Products',
          child: IconButton(
              onPressed: () async {
                bool? res = await showDialog(
                  context: context,
                  builder: (context) {
                    List<OrderProductData> selectedProdutcsInPopup = [];
                    selectedProdutcsInPopup
                        .addAll(deliveredOroutfordeliveryProducts);
                    List<OrderProductData> popupselectedProduct = widget
                        .selectedDeliveryProducts
                        .map((e) => OrderProductData.fromJson(e.toJson()))
                        .toList();
                    selectedProdutcsInPopup.addAll(popupselectedProduct);

                    return StatefulBuilder(builder: (context, setState3) {
                      return Dialog(
                        backgroundColor: Colors.white,
                        surfaceTintColor: Colors.white,
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          constraints: const BoxConstraints(
                            maxHeight: 700,
                            maxWidth: 1000,
                          ),
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Select Products',
                                  style: GoogleFonts.zcoolXiaoWei(
                                      fontSize: 24, color: themeColor),
                                ),
                                const SizedBox(height: 20),
                                Row(
                                  children: [
                                    Checkbox(
                                      value: selectedProdutcsInPopup.length ==
                                          widget.allOrderedProducts.length,
                                      onChanged: (value) {
                                        if (value == true) {
                                          selectedProdutcsInPopup.clear();

                                          selectedProdutcsInPopup.addAll(widget
                                              .allOrderedProducts
                                              .map((e) => e.orderData)
                                              .toList());
                                        }
                                        if (value == false) {
                                          selectedProdutcsInPopup.clear();
                                          selectedProdutcsInPopup.addAll(
                                              deliveredOroutfordeliveryProducts);
                                        }
                                        setState3(() {});
                                      },
                                    ),
                                    const SizedBox(width: 5),
                                    const SizedBox(
                                        width: 50, child: Text('Image')),
                                    const SizedBox(width: 15),
                                    const Expanded(child: Text('Product Code')),
                                    const SizedBox(width: 15),
                                    const Expanded(child: Text('Product Name')),
                                    const SizedBox(width: 15),
                                    const Expanded(child: Text('Vendor Name')),
                                    const SizedBox(width: 15),
                                    const Expanded(
                                        child: Text('Current Status')),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                const Divider(color: Colors.grey, height: 0),
                                const SizedBox(height: 10),
                                ...List.generate(
                                  widget.allOrderedProducts.length,
                                  (index) {
                                    final isSelected = selectedProdutcsInPopup
                                        .map((e) => e.id)
                                        .contains(widget
                                            .allOrderedProducts[index]
                                            .orderData
                                            .id);
                                    final alreadyDelivered =
                                        deliveredOroutfordeliveryProducts
                                            .map((e) => e.id)
                                            .contains(widget
                                                .allOrderedProducts[index]
                                                .orderData
                                                .id);
                                    return Padding(
                                      padding: EdgeInsets.only(
                                          top: index == 0 ? 0 : 12.0),
                                      child: IgnorePointer(
                                        ignoring: alreadyDelivered,
                                        child: InkWell(
                                          onTap: () {
                                            if (isSelected) {
                                              selectedProdutcsInPopup
                                                  .removeWhere((element) =>
                                                      element.id ==
                                                      widget
                                                          .allOrderedProducts[
                                                              index]
                                                          .orderData
                                                          .id);
                                            } else {
                                              selectedProdutcsInPopup.add(widget
                                                  .allOrderedProducts[index]
                                                  .orderData);
                                            }
                                            setState3(() {});
                                          },
                                          child: Row(
                                            children: [
                                              Checkbox(
                                                value: isSelected,
                                                onChanged: (value) {
                                                  if (value == true) {
                                                    selectedProdutcsInPopup.add(
                                                        widget
                                                            .allOrderedProducts[
                                                                index]
                                                            .orderData);
                                                  }
                                                  if (value == false) {
                                                    selectedProdutcsInPopup
                                                        .removeWhere((element) =>
                                                            element.id ==
                                                            widget
                                                                .allOrderedProducts[
                                                                    index]
                                                                .orderData
                                                                .id);
                                                  }
                                                  setState3(() {});
                                                },
                                              ),
                                              const SizedBox(width: 5),
                                              Container(
                                                height: 50,
                                                width: 50,
                                                clipBehavior: Clip.antiAlias,
                                                decoration: const BoxDecoration(
                                                    shape: BoxShape.circle),
                                                child: Image.network(
                                                  widget
                                                      .allOrderedProducts[index]
                                                      .variant
                                                      .images
                                                      .first,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                              const SizedBox(width: 15),
                                              Expanded(
                                                child: Text(widget
                                                    .allOrderedProducts[index]
                                                    .product
                                                    .sku),
                                              ),
                                              const SizedBox(width: 15),
                                              Expanded(
                                                  child: Text(
                                                      capilatlizeFirstLetter(
                                                          widget
                                                              .allOrderedProducts[
                                                                  index]
                                                              .product
                                                              .name))),
                                              const SizedBox(width: 15),
                                              Expanded(
                                                  child: Text(
                                                      capilatlizeFirstLetter(
                                                          widget
                                                              .allOrderedProducts[
                                                                  index]
                                                              .vendorData
                                                              .name))),
                                              const SizedBox(width: 15),
                                              Expanded(
                                                  child: Text(
                                                      capilatlizeFirstLetter(widget
                                                              .allOrderedProducts[
                                                                  index]
                                                              .orderData
                                                              .currentStatus ??
                                                          '-'))),
                                            ],
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                                const SizedBox(height: 20),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text('Cancel'),
                                    ),
                                    const SizedBox(width: 10),
                                    ElevatedButton(
                                      onPressed: () {
                                        widget.selectedDeliveryProducts.clear();
                                        selectedProdutcsInPopup.removeWhere(
                                            (element) =>
                                                deliveredOroutfordeliveryProducts
                                                    .map((e) => e.id)
                                                    .contains(element.id));
                                        widget.selectedDeliveryProducts.addAll(
                                            selectedProdutcsInPopup
                                                .map((e) => DeliveryProductData
                                                    .fromJson(e.toJson()))
                                                .toList());

                                        Navigator.of(context).pop(true);
                                      },
                                      child: const Text('Done'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    });
                  },
                );
                if (res == true) {
                  widget.refresh();
                }
              },
              icon: const Icon(CupertinoIcons.add)),
        )
      ],
    );
  }
}
