// ignore: unnecessary_import
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../../shared/theme.dart';
import '../../common/table_header.dart';

class OrderTableHeader extends StatelessWidget {
  const OrderTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: dashboardSelectedColor,
          border: Border.all(color: dividerColor),
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8))

          // color: const Color.fromARGB(255, 228, 228, 228),
          // color: themeColor,
          // color: const Color.fromARGB(255, 177, 139, 86),
          // color: tableHeaderColor,
          // borderRadius: BorderRadius.circular(4),
          ),
      child: Row(
        children: [
          const SizedBox(
            width: 80,
            child: Text(
              'Sr No',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                letterSpacing: 1.2,
              ),
            ),
          ),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Order ID')),
          const SizedBox(width: 5),
          const Expanded(flex: 2, child: TableHeaderText(headerName: 'Amount')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Items')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Status')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Order Type')),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Created At')),
          // Opacity(
          //   opacity: 0,
          //   child: IgnorePointer(
          //     ignoring: true,
          //     child: SizedBox(
          //         width: 60,
          //         child: Transform.scale(
          //           scale: .65,
          //           child: CupertinoSwitch(
          //             value: true,
          //             onChanged: (value) {},
          //           ),
          //         )),
          //   ),
          // ),
          Opacity(
            opacity: 0,
            child: IgnorePointer(
              ignoring: true,
              child: SizedBox(
                width: 60,
                child: IconButton(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  onPressed: () {},
                  icon: const Icon(
                    Icons.edit,
                    size: 22,
                  ),
                ),
              ),
            ),
          ),
          Opacity(
            opacity: 0,
            child: IgnorePointer(
              ignoring: true,
              child: SizedBox(
                width: 60,
                child: IconButton(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  onPressed: () {},
                  icon: const Icon(
                    Icons.delete,
                    color: themeColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
