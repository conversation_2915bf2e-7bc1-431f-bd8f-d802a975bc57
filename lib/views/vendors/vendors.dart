import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/models/subcategories.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/shared/state_city.dart';
import '../../controller/home_ctrl.dart';
import '../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../../shared/router.dart';
import '../../shared/theme.dart';
import '../common/header_search_feild.dart';
import '../common/top_sections.dart';
import 'widgets/vendor_table_header.dart';
import 'widgets/vendor_tile.dart';

class VendorsPage extends StatefulWidget {
  const VendorsPage({super.key});

  @override
  State<VendorsPage> createState() => _VendorsPageState();
}

class _VendorsPageState extends State<VendorsPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (hctrl) {
      return VendorMainPage(
          allVendors: hctrl.vendors, allSubCategories: hctrl.subcategories);
    });
  }
}

class VendorMainPage extends StatefulWidget {
  const VendorMainPage(
      {super.key, required this.allVendors, required this.allSubCategories});
  final List<VendorModel> allVendors;
  final List<SubCategory> allSubCategories;
  @override
  State<VendorMainPage> createState() => _VendorMainPageState();
}

class _VendorMainPageState extends State<VendorMainPage> {
  List<VendorModel> finalFilteredList = <VendorModel>[];
  TextEditingController searchController = TextEditingController();
  String? selectedCity;
  String? selectedLevel;
  String? selectedSubCatDocId;

  getSearchedData() {
    finalFilteredList = widget.allVendors.where(
      (element) {
        return (selectedCity == null ? true : (element.city == selectedCity)) &&
            (selectedLevel == null ? true : (element.level == selectedLevel)) &&
            (selectedSubCatDocId == null
                ? true
                : (element.subcatids.contains(selectedSubCatDocId)));
      },
    ).where((element) {
      return element.name
              .toLowerCase()
              .contains(searchController.text.toLowerCase().trim()) ||
          element.phone
              .toLowerCase()
              .contains(searchController.text.toLowerCase().trim()) ||
          element.email
              .toLowerCase()
              .contains(searchController.text.toLowerCase().trim());
    }).toList();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    getSearchedData();
    final cities = sortedCities();

    return Column(
      children: [
        const TopSectionOfPages(
          pageTile: 'Vendor Management',
          pagesubTile: 'Manage vendors and their performance',
        ),
        SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // PageHeaderWithButton(
                //   title: 'Vendors',
                //   button: true,
                //   buttonName: 'Vendor',
                //   icon: CupertinoIcons.person_add,
                //   onPressed: () {
                //     addEditVendorForm(context, null);
                //   },
                // ),
                // const SizedBox(height: 20),
                Row(
                  children: [
                    Container(
                      constraints: const BoxConstraints(maxWidth: 700),
                      child: Row(
                        children: [
                          Expanded(
                            child: DropdownButtonHideUnderline(
                                child: DropdownButtonFormField(
                              isExpanded: true,
                              hint: const Text("Select sub-category"),
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                    borderSide:
                                        const BorderSide(color: dividerColor),
                                    borderRadius: BorderRadius.circular(7)),
                                enabledBorder: OutlineInputBorder(
                                    borderSide:
                                        const BorderSide(color: dividerColor),
                                    borderRadius: BorderRadius.circular(7)),
                                focusedBorder: OutlineInputBorder(
                                    borderSide:
                                        const BorderSide(color: dividerColor),
                                    borderRadius: BorderRadius.circular(7)),
                              ),
                              value: selectedSubCatDocId,
                              items: [
                                ...List.generate(
                                  widget.allSubCategories.length,
                                  (index) {
                                    return DropdownMenuItem(
                                      value:
                                          widget.allSubCategories[index].docId,
                                      child: Text(
                                          widget.allSubCategories[index].name),
                                    );
                                  },
                                )
                              ],
                              onChanged: (value) {
                                selectedSubCatDocId = value;
                                getSearchedData();
                                setState(() {});
                              },
                            )),
                          ),
                          const SizedBox(width: 7),
                          Expanded(
                            child: DropdownSearch<String>(
                              onChanged: (value) {
                                selectedCity = value!;
                                getSearchedData();

                                setState(() {});
                              },
                              items: (filter, loadProps) {
                                return cities;
                              },
                              selectedItem: selectedCity,
                              popupProps:
                                  const PopupProps.menu(showSearchBox: true),
                              decoratorProps: DropDownDecoratorProps(
                                // expands: true,

                                decoration: InputDecoration(
                                  // labelText: 'Examples for: ',
                                  hintText: 'Select city',
                                  hintStyle: TextStyle(
                                    color: Theme.of(context).hintColor,
                                  ),
                                  // labelText: 'City',
                                  border: OutlineInputBorder(
                                      borderSide:
                                          const BorderSide(color: dividerColor),
                                      borderRadius: BorderRadius.circular(7)),
                                  enabledBorder: OutlineInputBorder(
                                      borderSide:
                                          const BorderSide(color: dividerColor),
                                      borderRadius: BorderRadius.circular(7)),
                                  focusedBorder: OutlineInputBorder(
                                      borderSide:
                                          const BorderSide(color: dividerColor),
                                      borderRadius: BorderRadius.circular(7)),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 7),
                          // PopupMenuButton(
                          //   position: PopupMenuPosition.under,
                          //   child: Text("data"),
                          //   itemBuilder: (context) {
                          //     return vendorLevelList
                          //         .map((e) => PopupMenuItem(child: Text(e)))
                          //         .toList();
                          //   },
                          // ),
                          Expanded(
                            child: DropdownButtonHideUnderline(
                                child: DropdownButtonFormField(
                              isExpanded: true,
                              hint: const Text("Select level"),
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                    borderSide:
                                        const BorderSide(color: dividerColor),
                                    borderRadius: BorderRadius.circular(7)),
                                enabledBorder: OutlineInputBorder(
                                    borderSide:
                                        const BorderSide(color: dividerColor),
                                    borderRadius: BorderRadius.circular(7)),
                                focusedBorder: OutlineInputBorder(
                                    borderSide:
                                        const BorderSide(color: dividerColor),
                                    borderRadius: BorderRadius.circular(7)),
                              ),
                              value: selectedLevel,
                              items: [
                                ...List.generate(
                                  vendorLevelList.length,
                                  (index) {
                                    return DropdownMenuItem(
                                      value: vendorLevelList[index],
                                      child: Text(vendorLevelList[index]),
                                    );
                                  },
                                )
                              ],
                              onChanged: (value) {
                                selectedLevel = value;
                                getSearchedData();

                                setState(() {});
                              },
                            )),
                          ),
                          if (selectedCity != null ||
                              selectedSubCatDocId != null ||
                              selectedLevel != null) ...[
                            const SizedBox(width: 10),
                            ElevatedButton(
                                onPressed: () {
                                  selectedCity = null;
                                  selectedSubCatDocId = null;
                                  selectedLevel = null;
                                  setState(() {});
                                },
                                child: const Text("Clear"))
                          ],
                        ],
                      ),
                    ),
                    Spacer(),

                    // SizedBox(
                    //   width: 10,
                    // ),
                    ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: themeColor,
                        elevation: 0,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4)),
                        // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                      ),
                      onPressed: () => addEditVendorForm(context, null),
                      icon: Icon(
                        CupertinoIcons.person_add,
                        size: 20,
                      ),
                      label: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text('Vendor',
                            style: GoogleFonts.livvic(
                                letterSpacing: 1.3,
                                fontSize: 14,
                                fontWeight: FontWeight.w500)),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                SearchField(
                  searchController: searchController,
                  onChanged: (p0) async {
                    getSearchedData();
                    setState(() {});
                  },
                ),
                const SizedBox(height: 20),
                const VendorTableHeader(),
                finalFilteredList.isEmpty
                    ? Container(
                        padding: EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                            border: Border(
                                bottom: BorderSide(color: dividerColor),
                                left: BorderSide(color: dividerColor),
                                right: BorderSide(color: dividerColor)),
                            borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(8),
                                bottomRight: Radius.circular(8))),
                        child: const Center(
                            child: Text(
                          'No Data Available',
                          style: TextStyle(
                              color: Color(0xff737373),
                              fontSize: 14.5,
                              fontWeight: FontWeight.w500),
                        )))
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...List.generate(
                            finalFilteredList.length,
                            (index) {
                              return InkWell(
                                highlightColor: Colors.transparent,
                                overlayColor: const WidgetStatePropertyAll(
                                    Colors.transparent),
                                hoverColor: Colors.transparent,
                                onTap: () {
                                  context.push(
                                      '${Routes.vendor}/${finalFilteredList[index].docId}');
                                },
                                child: VendorTile(
                                  onEdit: () {
                                    addEditVendorForm(
                                        context, finalFilteredList[index]);
                                  },
                                  index: index,
                                  vendorModel: finalFilteredList[index],
                                  isLast:
                                      index == (finalFilteredList.length - 1),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
              ],
            )),
      ],
    );
  }
}

Future<dynamic> addEditVendorForm(BuildContext context, VendorModel? vendor) {
  bool loading = false;
  TextEditingController nameCtrl = TextEditingController();
  TextEditingController numberCtrl = TextEditingController();
  TextEditingController emailCtrl = TextEditingController();
  TextEditingController addressCtrl = TextEditingController();
  TextEditingController serviceDescriptionCtrl = TextEditingController();
  TextEditingController contactPersonCtrl = TextEditingController();
  String? selectedLevel;
  String? selectedCity;
  String? selectedState;
  if (vendor != null) {
    selectedLevel = vendor.level;
    nameCtrl.text = vendor.name;
    numberCtrl.text = vendor.phone;
    emailCtrl.text = vendor.email;
    addressCtrl.text = vendor.address;
    selectedState = vendor.state;
    selectedCity = vendor.city;
    serviceDescriptionCtrl.text = vendor.serviceDescription;
    contactPersonCtrl.text = vendor.contactPerson;
  }
  return showDialog(
    context: context,
    builder: (context) {
      return StatefulBuilder(builder: (context, setState2) {
        // statesAndCities.keys.toList().sort();
        // statesAndCities[selectedState]?.sort();
        return AlertDialog(
          // backgroundColor: const Color(0xffFEF2D0),
          // surfaceTintColor: const Color(0xffFEF2D0),
          // shadowColor: const Color(0xffFEF2D0),
          // backgroundColor: dashboardColor,
          // surfaceTintColor: dashboardColor,
          // shadowColor: dashboardColor,
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          // backgroundColor: const Color.fromARGB(255, 255, 244, 228),
          // surfaceTintColor: const Color.fromARGB(255, 255, 244, 228),
          // shadowColor: const Color.fromARGB(255, 255, 244, 228),
          contentPadding:
              const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
          title: Text(vendor != null ? "Edit Vendor" : "Add Vendor"),
          content: SizedBox(
            width: 550,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: nameCtrl,
                        cursorHeight: 20,
                        decoration: inpDecor().copyWith(labelText: 'Name*'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: numberCtrl,
                        cursorHeight: 20,
                        decoration: inpDecor().copyWith(labelText: 'Number*'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: TextFormField(
                        controller: emailCtrl,
                        enabled: vendor == null,
                        cursorHeight: 20,
                        decoration: inpDecor().copyWith(labelText: 'Email*'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                        child: DropdownButtonHideUnderline(
                            child: DropdownButtonFormField(
                      hint: const Text("select level"),
                      value: selectedLevel,
                      decoration: inpDecor().copyWith(labelText: 'Level*'),
                      items: [
                        ...List.generate(
                          vendorLevelList.length,
                          (index) {
                            return DropdownMenuItem(
                                value: vendorLevelList[index],
                                child: Text(vendorLevelList[index]));
                          },
                        )
                      ],
                      onChanged: (value) {
                        selectedLevel = value;
                        setState2(() {});
                      },
                    )))
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: addressCtrl,
                        cursorHeight: 20,
                        maxLines: 4,
                        decoration: inpDecor().copyWith(labelText: 'Address'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: serviceDescriptionCtrl,
                        cursorHeight: 20,
                        maxLines: 4,
                        decoration: inpDecor()
                            .copyWith(labelText: 'Service Description'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: contactPersonCtrl,
                        cursorHeight: 20,
                        decoration:
                            inpDecor().copyWith(labelText: 'Contact Person'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonHideUnderline(
                          child: DropdownButtonFormField(
                        hint: const Text("select state"),
                        value: selectedState,
                        decoration: inpDecor().copyWith(labelText: 'State*'),
                        items: [
                          ...List.generate(
                            statesAndCities.keys.length,
                            (index) {
                              return DropdownMenuItem(
                                value: statesAndCities.keys.toList()[index],
                                child:
                                    Text(statesAndCities.keys.toList()[index]),
                              );
                            },
                          )
                        ],
                        onChanged: (value) {
                          selectedState = value;
                          setState2(() {});
                        },
                      )),
                    ),
                  ],
                ),
                if (selectedState != null) ...[
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonHideUnderline(
                            child: DropdownButtonFormField(
                          hint: const Text("select city"),
                          value: statesAndCities[selectedState]!
                                  .contains(selectedCity)
                              ? selectedCity
                              : null,
                          decoration: inpDecor().copyWith(labelText: 'City*'),
                          items: [
                            ...List.generate(
                              statesAndCities[selectedState]?.length ?? 0,
                              (index) {
                                return DropdownMenuItem(
                                  value: statesAndCities[selectedState]![index],
                                  child: Text(
                                      statesAndCities[selectedState]![index]),
                                );
                              },
                            )
                          ],
                          onChanged: (value) {
                            selectedCity = value;
                            setState2(() {});
                          },
                        )),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.center,
          actions: loading
              ? [
                  const Center(
                    child: SizedBox(
                      height: 25,
                      width: 25,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        color: themeColor,
                      ),
                    ),
                  )
                ]
              : [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeColor,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () async {
                      if (loading) return;

                      try {
                        if (nameCtrl.text.isEmpty) {
                          showErrorAppSnackBar(context, 'Name Required');
                          return;
                        }
                        if (numberCtrl.text.isEmpty) {
                          showErrorAppSnackBar(context, 'Number Required');
                          return;
                        }
                        if (emailCtrl.text.isEmpty) {
                          showErrorAppSnackBar(context, 'Email Required');
                          return;
                        }
                        if (selectedState == null) {
                          showErrorAppSnackBar(context, 'Select State');
                          return;
                        }
                        if (selectedLevel == null) {
                          showErrorAppSnackBar(context, 'Select Level');
                          return;
                        }
                        if (selectedCity == null) {
                          showErrorAppSnackBar(context, 'Select City');
                          return;
                        }
                        if (!isValidEmail(
                            emailCtrl.text.toLowerCase().trim())) {
                          showErrorAppSnackBar(context, 'Invalid Email');
                          return;
                        }

                        final phone =
                            numberCtrl.text.trim().substring(0, 3) == '+91'
                                ? numberCtrl.text.trim()
                                : '+91${numberCtrl.text.trim()}';
                        if (!isValidIndianPhoneNumber(phone)) {
                          showErrorAppSnackBar(context, 'Invalid Phone number');
                          return;
                        }
                        final vendorSnap = vendor != null
                            ? await FBFireStore.vendors
                                .where('email',
                                    isEqualTo:
                                        emailCtrl.text.toLowerCase().trim())
                                .where(FieldPath.documentId,
                                    isNotEqualTo: vendor.docId)
                                .get()
                            : await FBFireStore.vendors
                                .where('email',
                                    isEqualTo:
                                        emailCtrl.text.toLowerCase().trim())
                                .get();
                        if (vendorSnap.size != 0) {
                          showErrorAppSnackBar(context, 'Email Already exist');
                          return;
                        }

                        setState2(() {
                          loading = true;
                        });

                        final data = {
                          'name': nameCtrl.text.toLowerCase().trim(),
                          'phone': phone,
                          'address': addressCtrl.text.trim(),
                          'email': emailCtrl.text.toLowerCase().trim(),
                          'password': vendor != null
                              ? vendor.password
                              : getRandomMix(8),
                          'contactPerson': contactPersonCtrl.text.trim(),
                          'subcatids': vendor != null ? vendor.subcatids : [],
                          'userType': UserTypes.vendor,
                          'speciality': null,
                          'isActive': vendor != null ? vendor.isActive : true,
                          'createdAt': vendor?.createdAt,
                          'city': selectedCity,
                          'state': selectedState,
                          'level': selectedLevel,
                          'websiteLink': null,
                          'apiRequiredData': {},
                          'apiSubCatIds': [],
                          'lastUpdatedOn':
                              vendor != null ? DateTime.now() : null,
                          'serviceDescription':
                              serviceDescriptionCtrl.text.trim(),
                        };

                        if (vendor != null) {
                          bool res = await FBFireStore.vendors
                              .doc(vendor.docId)
                              .update(data)
                              .then((value) => true);
                          if (res) showAppSnackBar(context, 'Vendor Updated');
                        }
                        if (vendor == null) {
                          final res = await FBFunctions.ff
                              .httpsCallable('createVendor')
                              .call(data);
                          if (res.data['success'] as bool) {
                            showAppSnackBar(context, 'Vendor Added');
                          }
                        }
                        setState2(() {
                          loading = false;
                        });
                        // const snackBar = SnackBar(
                        //   content: Text("Vendor Added"),
                        //   duration: Duration(seconds: 2),
                        // );
                        nameCtrl.clear();
                        numberCtrl.clear();
                        emailCtrl.clear();
                        addressCtrl.clear();
                        serviceDescriptionCtrl.clear();
                        if (context.mounted) {
                          Navigator.of(context).pop();
                          // showAppSnackBar(
                          //     context,
                          //     vendor != null
                          //         ? 'Vendor Updated'
                          //         : 'Vendor Added');
                          // ScaffoldMessenger.of(context).showSnackBar(snackBar);
                        }
                      } catch (e) {
                        debugPrint(e.toString());
                        showAppSnackBar(context, 'Something went wrong');
                        setState2(() {
                          loading = false;
                        });
                      }
                    },
                    child: const Text("Save"),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text("Cancel"),
                  ),
                ],
        );
      });
    },
  );
}
