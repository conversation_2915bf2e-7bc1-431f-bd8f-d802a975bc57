import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:image_network/image_network.dart';
import 'package:wedding_super_admin/services/image_picker.dart';
import '../../../../models/product.dart';
import '../../../../shared/methods.dart';
import '../../../../shared/theme.dart';

class ProductTile extends StatefulWidget {
  const ProductTile({
    super.key,
    required this.index,
    this.productModel,
    required this.onDelete,
    required this.onSwitch,
    required this.onCheckbox,
  });
  final int index;
  final ProductModel? productModel;
  final Function() onDelete;
  final Function(bool) onSwitch;
  final Function(bool) onCheckbox;
  @override
  State<ProductTile> createState() => _ProductTileState();
}

class _ProductTileState extends State<ProductTile> {
  @override
  Widget build(BuildContext context) {
    // final hctrl = Get.find<HomeCtrl>();
    // final mainCategoryName = hctrl.offerings
    //     .firstWhereOrNull(
    //         (element) => element.docId == widget.productModel?.mainCatDocId)
    //     ?.name;
    // final subCategoryName = hctrl.subcategories
    //     .firstWhereOrNull((element) =>
    //         element.docId == widget.productModel?.defaultSubCatDocId)
    //     ?.name;
    return widget.productModel != null
        ? Container(
            padding: const EdgeInsets.symmetric(vertical: 5),
            decoration: widget.index % 2 != 0
                ? BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    // color: Colors.grey[200],
                    color: dashboardColor)
                : null,
            child: Row(
              children: [
                const SizedBox(width: 5),
                Container(
                  clipBehavior: Clip.antiAlias,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  width: 80,
                  child: IgnorePointer(
                    ignoring: true,
                    child: ImageNetwork(
                      image: widget.productModel?.defaultImage ?? '',
                      width: 50,
                      height: 50,
                      duration: 1500,
                      curve: Curves.easeIn,
                      onPointer: true,
                      debugPrint: false,
                      backgroundColor: Colors.white,
                      fitAndroidIos: BoxFit.cover,
                      fitWeb: BoxFitWeb.cover,
                      borderRadius: BorderRadius.circular(70),
                      onLoading: const CircularProgressIndicator(
                        color: themeColor,
                      ),
                      onError: const Icon(
                        Icons.web_asset_off_outlined,
                        color: Colors.red,
                      ),
                      onTap: () {
                        debugPrint("©gabriel_patrick_souza");
                      },
                    ),
                  ),
                ),
                // Container(
                //     clipBehavior: Clip.antiAlias,
                //     decoration: const BoxDecoration(
                //       shape: BoxShape.circle,
                //     ),
                //     width: 80,
                //     child: CachedNetworkImage(
                //       imageUrl: widget.productModel?.defaultImage ?? '',
                //       fit: BoxFit.cover,
                //       height: 50,
                //     )),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(capilatlizeFirstLetter(
                        widget.productModel?.sku.trim().isNotEmpty ?? false
                            ? widget.productModel?.sku ?? "-"
                            : '-'))),

                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                        capilatlizeFirstLetter(widget.productModel!.name))),
                const SizedBox(width: 5),
                // Expanded(
                //     child:
                //         Text(capilatlizeFirstLetter(mainCategoryName ?? ""))),
                // const SizedBox(width: 5),
                // Expanded(
                //     child: Text(capilatlizeFirstLetter(subCategoryName ?? ""))),
                // const SizedBox(width: 5),
                const Expanded(child: Text("-")),
                const SizedBox(width: 5),
                // Expanded(
                //     child: Text(
                //         widget.productModel?.available.toString() ?? "false")),
                // const SizedBox(width: 5),
                SizedBox(
                    width: 100,
                    child: Center(
                      child: Checkbox(
                        side: const BorderSide(color: Colors.grey, width: 1.5),
                        checkColor: Colors.white,
                        activeColor: themeColor,
                        hoverColor: Colors.transparent,
                        // focusColor: Colors.transparent,
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        value: widget.productModel!.topSelling,
                        onChanged: (value) {
                          widget.onCheckbox(value!);
                        },
                      ),
                    )),
                // Expanded(
                //     child: Text(
                //         widget.productModel?.topSelling.toString() ?? "false")),
                const SizedBox(width: 5),
                // SizedBox(
                //   width: 60,
                //   child: IconButton(
                //     highlightColor: Colors.transparent,
                //     hoverColor: Colors.transparent,
                //     onPressed: () {
                //       context.push(
                //           '${Routes.product}/${widget.productModel?.docId}',
                //           extra: [
                //             widget.productModel?.vendorDocId,
                //             widget.productModel?.subCatDocId
                //           ]);
                //     },
                //     icon: const Icon(
                //       Icons.edit_outlined,
                //       size: 22,
                //     ),
                //   ),
                // ),
                SizedBox(
                    width: 100,
                    child: Center(
                      child: Transform.scale(
                        scale: .65,
                        child: CupertinoSwitch(
                          value: widget.productModel!.show,
                          onChanged: (value) {
                            widget.onSwitch(value);
                          },
                        ),
                      ),
                    )),
                const SizedBox(width: 5),

                SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {
                      widget.onDelete();
                      /* 
                      showDialog(
                        context: context,
                        builder: (context) {
                          bool loading = false;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                title: const Text("Alert"),
                                content: const Text(
                                    "Are you sure you want to delete"),
                                actions: loading
                                    ? [
                                        const Center(
                                          child: SizedBox(
                                            height: 25,
                                            width: 25,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                            ),
                                          ),
                                        )
                                      ]
                                    : [
                                        TextButton(
                                            onPressed: () async {
                                              try {
                                                setState2(() {
                                                  loading = true;
                                                });
                                                try {
                                                  await FBFireStore.products
                                                      .doc(widget
                                                          .productModel?.docId)
                                                      .delete();
                                                  widget.refresh(widget.index);
                                                } on Exception catch (e) {
                                                  debugPrint(e.toString());
                                                }
                                                const snackBar = SnackBar(
                                                    content: Text(
                                                        "Product deleted successfully"));
                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(snackBar);
                                                }
                                              } catch (e) {
                                                debugPrint(e.toString());

                                                if (context.mounted) {
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop();
                                                }
                                              }
                                            },
                                            child: const Text('Yes')),
                                        TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('No')),
                                      ],
                              );
                            },
                          );
                        },
                      );
                     */
                    },
                    icon: const Icon(
                      Icons.delete,
                      color: themeColor,
                    ),
                  ),
                ),
              ],
            ),
          )
        : const SizedBox();
  }
}

class DisplayImageModel {
  final String? url;
  final SelectedImage? selectedImage;

  DisplayImageModel({required this.url, required this.selectedImage});
}
