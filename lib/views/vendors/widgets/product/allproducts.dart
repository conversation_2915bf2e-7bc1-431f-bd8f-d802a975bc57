import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/models/offerings.dart';
import 'package:wedding_super_admin/models/subcategories.dart';
import '../../../../controller/home_ctrl.dart';
import '../../../../models/product.dart';
import '../../../../shared/firebase.dart';
import '../../../../shared/router.dart';
import '../../../../shared/theme.dart';
import '../../../common/header_search_feild.dart';
import '../../../common/page_header.dart';
import 'product_table_header.dart';
import 'product_tile.dart';

class SubCatAllProducts extends StatefulWidget {
  const SubCatAllProducts({super.key, this.subcatid, required this.vendorId});
  final String? subcatid;
  final String? vendorId;
  @override
  State<SubCatAllProducts> createState() => _SubCatAllProductsState();
}

class _SubCatAllProductsState extends State<SubCatAllProducts> {
  // List<SubCategory> subCategories = <SubCategory>[];
  List<ProductModel> filteredProductList = <ProductModel>[];
  List<ProductModel> allproducts = <ProductModel>[];
  TextEditingController searchController = TextEditingController();
  bool dataLoaded = false;
  SubCategory? subCategory;
  OfferingsModel? mainCat;
  @override
  void initState() {
    super.initState();
    getAllProducts();
  }

  getAllProducts() async {
    subCategory = Get.find<HomeCtrl>()
        .subcategories
        .firstWhereOrNull((element) => element.docId == widget.subcatid);
    mainCat = Get.find<HomeCtrl>().offerings.firstWhereOrNull(
        (element) => element.docId == subCategory?.offeringId);
    final allproductsSnap = await FBFireStore.products
        .where('subCatDocId', isEqualTo: widget.subcatid)
        .where('vendorDocId', isEqualTo: widget.vendorId)
        .get();
    allproducts =
        allproductsSnap.docs.map((e) => ProductModel.fromSnap(e)).toList();
    filteredProductList.clear();
    filteredProductList.addAll(allproducts);
    dataLoaded = true;
    setState(() {});
  }

  getSearchData() {
    filteredProductList.clear();
    if (allproducts.isNotEmpty) {
      if (searchController.text.trim().isEmpty) {
        filteredProductList.addAll(allproducts);
      } else {
        filteredProductList.addAll(allproducts.where((product) => (product.name
                .toLowerCase()
                .contains(searchController.text.toLowerCase().trim()) ||
            product.sku
                .toLowerCase()
                .contains(searchController.text.toLowerCase().trim()))));
      }
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return dataLoaded
        ? SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            physics: const ClampingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PageHeaderWithButton(
                  title: 'Products',
                  back: true,
                  button: true,
                  buttonName: 'New',
                  icon: CupertinoIcons.add,
                  onPressed: () async {
                    ProductModel? res = await context.push(
                        '${Routes.product}/${null}',
                        extra: [widget.vendorId, widget.subcatid]);
                    if (res != null) {
                      filteredProductList.add(res);
                      setState(() {});
                    }
                  },
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Container(
                      height: 35,
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                          color: dashboardColor,
                          borderRadius: BorderRadius.circular(8)),
                      child: Row(
                        children: [
                          //   const Icon(
                          //     CupertinoIcons.pen,
                          //     color: themeColor,
                          //     size: 20,
                          //   ),
                          //   const SizedBox(width: 10),
                          Text(mainCat?.name ?? ""),
                        ],
                      ),
                    ),
                    const SizedBox(width: 15),
                    Container(
                      height: 35,
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                          color: dashboardColor,
                          borderRadius: BorderRadius.circular(8)),
                      child: Row(
                        children: [
                          // const Icon(
                          //   CupertinoIcons.pen,
                          //   color: themeColor,
                          //   size: 20,
                          // ),
                          // const SizedBox(width: 10),
                          Text(subCategory?.name ?? ""),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                SearchField(
                  hintText: ' Search title/sku',
                  searchController: searchController,
                  onChanged: (p0) async {
                    getSearchData();
                    // getSearchData();
                  },
                ),
                const SizedBox(height: 25),
                const ProductTableHeader(),
                if (filteredProductList.isNotEmpty)
                  ...List.generate(
                    filteredProductList.length,
                    (index) {
                      return InkWell(
                        highlightColor: Colors.transparent,
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        hoverColor: Colors.transparent,
                        onTap: () async {
                          ProductModel? res = await context.push(
                              '${Routes.product}/${filteredProductList[index].docId}',
                              extra: [
                                filteredProductList[index].vendorDocId,
                                widget.subcatid
                              ]);
                          if (res != null) {
                            filteredProductList.removeWhere((element) {
                              return element.docId == res.docId;
                            });
                            allproducts.removeWhere((element) {
                              return element.docId == res.docId;
                            });
                            filteredProductList.insert(index, res);
                            allproducts.insert(index, res);
                          }
                          setState(() {});
                        },
                        child: ProductTile(
                            onCheckbox: (p0) async {
                              await FBFireStore.products
                                  .doc(filteredProductList[index].docId)
                                  .update({
                                'topSelling': p0,
                              });
                              filteredProductList[index].topSelling = p0;
                              setState(() {});
                            },
                            onSwitch: (p0) async {
                              await FBFireStore.products
                                  .doc(filteredProductList[index].docId)
                                  .update({
                                'show': p0,
                              });
                              filteredProductList[index].show = p0;
                              setState(() {});
                            },
                            onDelete: () async {
                              bool res = await showDialog(
                                context: context,
                                builder: (context) {
                                  bool loading = false;
                                  return StatefulBuilder(
                                    builder: (context, setState2) {
                                      return AlertDialog(
                                        backgroundColor: Colors.white,
                                        surfaceTintColor: Colors.white,
                                        title: const Text("Alert"),
                                        content: const Text(
                                            "Are you sure you want to delete"),
                                        actions: loading
                                            ? [
                                                const Center(
                                                  child: SizedBox(
                                                    height: 25,
                                                    width: 25,
                                                    child:
                                                        CircularProgressIndicator(
                                                      strokeWidth: 2.5,
                                                    ),
                                                  ),
                                                )
                                              ]
                                            : [
                                                TextButton(
                                                    onPressed: () async {
                                                      if (loading) return;

                                                      try {
                                                        setState2(() {
                                                          loading = true;
                                                        });
                                                        try {
                                                          await FBFireStore
                                                              .products
                                                              .doc(
                                                                  filteredProductList[
                                                                          index]
                                                                      .docId)
                                                              .delete();
                                                          filteredProductList
                                                              .removeAt(index);
                                                        } on Exception catch (e) {
                                                          debugPrint(
                                                              e.toString());
                                                        }

                                                        if (context.mounted) {
                                                          Navigator.of(context)
                                                              .pop(true);
                                                          setState2(() {
                                                            loading = false;
                                                          });
                                                        }
                                                      } catch (e) {
                                                        debugPrint(
                                                            e.toString());

                                                        if (context.mounted) {
                                                          setState2(() {
                                                            loading = false;
                                                          });
                                                          Navigator.of(context)
                                                              .pop(false);
                                                        }
                                                      }
                                                    },
                                                    child: const Text('Yes')),
                                                TextButton(
                                                    onPressed: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                    child: const Text('No')),
                                              ],
                                      );
                                    },
                                  );
                                },
                              );
                              setState(() {});
                              if (res && context.mounted) {
                                const snackBar = SnackBar(
                                    content:
                                        Text("Product deleted successfully"));
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(snackBar);
                              } else if (!res && context.mounted) {
                                const snackBar = SnackBar(
                                    content: Text("Error deleting product"));
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(snackBar);
                              }
                            },
                            index: index,
                            productModel: filteredProductList[index]),
                      );
                    },
                  ),
                if (filteredProductList.isEmpty)
                  const Padding(
                    padding: EdgeInsets.only(top: 20.0),
                    child: Center(child: Text("No Products to Display")),
                  )
              ],
            ),
          )
        : const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(child: CircularProgressIndicator()),
            ],
          );
  }
}
