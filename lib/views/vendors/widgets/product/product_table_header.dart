import 'package:flutter/material.dart';
import '../../../../shared/theme.dart';
import '../../../common/table_header.dart';

class ProductTableHeader extends StatelessWidget {
  const ProductTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        // color: const Color.fromARGB(255, 228, 228, 228),
        // color: themeColor,
        // color: const Color.fromARGB(255, 177, 139, 86),
        color: tableHeaderColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const SizedBox(width: 5),
          const SizedBox(
              width: 80,
              child: Text(
                'Images',
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    letterSpacing: 1.2,
                    color: Colors.black),
              )),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Product Sku')),

          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Product Name')),
          const SizedBox(width: 5),
          // const Expanded(child: TableHeaderText(headerName: 'Main Category')),
          // const SizedBox(width: 5),
          // const Expanded(child: TableHeaderText(headerName: 'Sub Category')),
          // const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Order Count')),
          // const SizedBox(width: 5),
          // const Expanded(child: TableHeaderText(headerName: 'Avaialable')),
          const SizedBox(width: 5),
          const SizedBox(
              width: 100,
              child: Center(child: TableHeaderText(headerName: 'Top Selling'))),
          const SizedBox(width: 5),
          const SizedBox(
              width: 100,
              child: Center(child: TableHeaderText(headerName: 'Available'))),

          const SizedBox(width: 5),

          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //     width: 60,
          //     child: IconButton(
          //       highlightColor: Colors.transparent,
          //       hoverColor: Colors.transparent,
          //       onPressed: () {},
          //       icon: const Icon(
          //         CupertinoIcons.pencil,
          //         size: 22,
          //       ),
          //     ),
          //   ),
          // ),
          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //       width: 60,
          //       child: Transform.scale(
          //         scale: .65,
          //         child: CupertinoSwitch(
          //           value: true,
          //           onChanged: (value) {},
          //         ),
          //       )),
          // ),

          Opacity(
            opacity: 0,
            child: SizedBox(
              width: 60,
              child: IconButton(
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onPressed: () {},
                icon: const Icon(
                  Icons.delete,
                  color: themeColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
