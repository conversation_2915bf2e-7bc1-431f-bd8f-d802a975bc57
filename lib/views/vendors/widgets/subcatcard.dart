import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:image_network/image_network.dart';
import 'package:wedding_super_admin/models/subcategories.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/theme.dart';

import '../../../shared/methods.dart';

class SubCatCard extends StatefulWidget {
  const SubCatCard(
      {super.key,
      required this.subCategory,
      required this.onRemove,
      required this.onSwitch,
      required this.vendorDocId});
  final SubCategory subCategory;
  final Function() onRemove;
  final Function(bool) onSwitch;
  final String vendorDocId;
  @override
  State<SubCatCard> createState() => _SubCatCardState();
}

class _SubCatCardState extends State<SubCatCard> {
  int totalProducts = 0;
  @override
  void initState() {
    super.initState();
    getTotalProducts();
  }

  getTotalProducts() async {
    final data = await FBFireStore.products
        .where('vendorDocId', isEqualTo: widget.vendorDocId)
        .where('subCatDocId', isEqualTo: widget.subCategory.docId)
        .count()
        .get();
    totalProducts = data.count ?? 0;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(7),
              color: Colors.white,
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  offset: Offset(0, 2),
                  blurRadius: 5,
                ),
              ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              IgnorePointer(
                ignoring: true,
                child: ImageNetwork(
                  image: widget.subCategory.image,
                  width: getWidth(200),
                  height: 200,
                  duration: 1500,
                  curve: Curves.easeIn,
                  onPointer: true,
                  debugPrint: false,
                  backgroundColor: Colors.white,
                  fitAndroidIos: BoxFit.cover,
                  fitWeb: BoxFitWeb.cover,
                  // borderRadius: BorderRadius.circular(70),
                  onLoading: const CircularProgressIndicator(
                    color: themeColor,
                  ),
                  onError: const Icon(
                    Icons.web_asset_off_outlined,
                    color: Colors.red,
                  ),
                  onTap: () {
                    debugPrint("©gabriel_patrick_souza");
                  },
                ),
              ),

              // CachedNetworkImage(
              //   imageUrl: widget.subCategory.image,
              //   height: 200,
              //   width: double.maxFinite,
              //   fit: BoxFit.cover,
              // ),
              const SizedBox(height: 5),
              Row(
                children: [
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      capilatlizeFirstLetter(widget.subCategory.name),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                          fontWeight: FontWeight.w600, fontSize: 15.5),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Transform.scale(
                    scale: .8,
                    child: CupertinoSwitch(
                      value: widget.subCategory.isActive,
                      onChanged: widget.onSwitch,
                    ),
                  ),
                  const SizedBox(width: 4),
                ],
              ),
              const SizedBox(height: 3),
              Row(
                children: [
                  const SizedBox(width: 8),
                  const Text(
                    'Total products:',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
                  ),
                  const SizedBox(width: 7),
                  Text(
                    totalProducts.toString(),
                    style: const TextStyle(
                        fontWeight: FontWeight.w500, fontSize: 15),
                  ),
                  const SizedBox(width: 4),
                ],
              ),
              const SizedBox(height: 5),
            ],
          ),
        ),
        Align(
          alignment: Alignment.topRight,
          child: Padding(
            padding: const EdgeInsets.only(right: 8.0, top: 8),
            child: InkWell(
              onTap: widget.onRemove,
              child: Container(
                padding: const EdgeInsets.all(3),
                decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                          color: Colors.black26,
                          blurRadius: 2,
                          // spreadRadius: 0,
                          spreadRadius: 1,
                          offset: Offset(0, 2)),
                    ]),
                child: const Icon(
                  CupertinoIcons.xmark,
                  size: 18,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
