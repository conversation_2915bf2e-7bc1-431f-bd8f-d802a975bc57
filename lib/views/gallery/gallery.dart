import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/models/gallery.dart';
import 'package:wedding_super_admin/services/image_picker.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/views/common/page_header.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';

import '../../controller/home_ctrl.dart';
import '../../shared/firebase.dart';
import '../../shared/theme.dart';

class Gallery extends StatefulWidget {
  const Gallery({super.key});

  @override
  State<Gallery> createState() => _GalleryState();
}

class _GalleryState extends State<Gallery> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
            pageTile: 'Gallery Management',
            pagesubTile: 'Upload and manage event photos'),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child: GetBuilder<HomeCtrl>(
              builder: (hctrl) {
                return Column(
                  children: [
                    PageHeaderWithTrailingAndBack(
                      title: 'Gallery',
                      showTrailing: true,
                      trailing: ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: themeColor,
                          elevation: 0,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4)),
                          // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                        ),
                        onPressed: () {
                          addEditGalleryForm(context, null);
                        },
                        icon: Icon(
                          CupertinoIcons.add,
                          size: 20,
                        ),
                        label: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text('Add',
                              style: GoogleFonts.livvic(
                                  letterSpacing: 1.3,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500)),
                        ),
                      ),
                    ),
                    // PageHeaderWithButton(
                    //   title: "Gallery",
                    //   button: hctrl.gallery.length < 6,
                    //   onPressed: () {
                    //     addEditGalleryForm(context, null);
                    //   },
                    //   buttonName: 'Add',
                    //   icon: CupertinoIcons.add,
                    // ),
                    const SizedBox(height: 20),
                    StaggeredGrid.extent(
                      maxCrossAxisExtent: 300,
                      mainAxisSpacing: 15,
                      crossAxisSpacing: 15,
                      children: [
                        ...List.generate(
                          hctrl.gallery.length,
                          (index) {
                            final gallery = hctrl.gallery[index];
                            return Stack(
                              children: [
                                InkWell(
                                  onTap: () {
                                    addEditGalleryForm(context, gallery);
                                  },
                                  child: AspectRatio(
                                    aspectRatio: 1,
                                    child: Container(
                                      clipBehavior: Clip.antiAlias,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: CachedNetworkImage(
                                        imageUrl: gallery.image,
                                        fit: BoxFit.cover,
                                        errorWidget: (context, url, error) {
                                          return Container(
                                            decoration: BoxDecoration(
                                              color: const Color.fromARGB(
                                                  6, 0, 0, 0),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: const Icon(
                                              CupertinoIcons
                                                  .exclamationmark_circle_fill,
                                              color: Color.fromARGB(
                                                  255, 196, 196, 196),
                                            ),
                                          );
                                        },
                                        placeholder: (context, url) {
                                          return Container(
                                            decoration: BoxDecoration(
                                              color: const Color.fromARGB(
                                                  6, 0, 0, 0),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ),

                                // DISCUSSED WITH SAMEER BHAI AND DELETED

                                /* Align(
                                  alignment: Alignment.topRight,
                                  child: Tooltip(
                                    message: 'Delete',
                                    child: Padding(
                                      padding:
                                          const EdgeInsets.only(right: 8.0, top: 8),
                                      child: InkWell(
                                        onTap: () {
                                          showDialog(
                                            context: context,
                                            builder: (context) {
                                              bool loading = false;
                                              return StatefulBuilder(
                                                builder: (context, setState2) {
                                                  return AlertDialog(
                                                    title: const Text('Delete'),
                                                    content: const Text(
                                                        "Are you sure you want to delete?"),
                                                    actions: loading
                                                        ? [
                                                            const Center(
                                                              child: SizedBox(
                                                                height: 25,
                                                                width: 25,
                                                                child:
                                                                    CircularProgressIndicator(
                                                                  strokeWidth: 2.5,
                                                                  color: themeColor,
                                                                ),
                                                              ),
                                                            )
                                                          ]
                                                        : [
                                                            TextButton(
                                                                onPressed: () async {
                                                                  setState2(() {
                                                                    loading = true;
                                                                  });
                                                                  try {
                                                                    await FBFireStore
                                                                        .gallery
                                                                        .doc(gallery
                                                                            .docId)
                                                                        .delete();
                                                                    if (context
                                                                        .mounted) {
                                                                      setState2(() {
                                                                        loading =
                                                                            false;
                                                                      });
                                                                      ScaffoldMessenger.of(
                                                                              context)
                                                                          .showSnackBar(const SnackBar(
                                                                              content:
                                                                                  Text("Image Deleted Successfully")));
                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();
                                                                    }
                                                                  } on Exception catch (e) {
                                                                    setState2(() {
                                                                      loading = false;
                                                                    });
                                                                    debugPrint(
                                                                        e.toString());
                                                                    if (context
                                                                        .mounted) {
                                                                      ScaffoldMessenger.of(
                                                                              context)
                                                                          .showSnackBar(const SnackBar(
                                                                              content:
                                                                                  Text("Facing Some Issue")));
                                                                    }
                                                                  }
                                                                },
                                                                child: const Text(
                                                                    "Yes")),
                                                            TextButton(
                                                                onPressed: () async {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                },
                                                                child:
                                                                    const Text("No")),
                                                          ],
                                                  );
                                                },
                                              );
                                            },
                                          );
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(3),
                                          decoration: const BoxDecoration(
                                              color: Colors.white,
                                              shape: BoxShape.circle,
                                              boxShadow: [
                                                BoxShadow(
                                                    color: Colors.black26,
                                                    blurRadius: 2,
                                                    // spreadRadius: 0,
                                                    spreadRadius: 1,
                                                    offset: Offset(0, 2)),
                                              ]),
                                          child: const Icon(
                                            CupertinoIcons.xmark,
                                            size: 18,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ), */
                              ],
                            );
                          },
                        )
                      ],
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Future<dynamic> addEditGalleryForm(
      BuildContext context, GalleryModel? gallery) {
    bool loading = false;
    TextEditingController priorityCtrl = TextEditingController();
    SelectedImage? selectedImage;
    String? image;
    if (gallery != null) {
      priorityCtrl.text = gallery.priorityNo.toString();
      image = gallery.image;
    }
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            // backgroundColor: const Color(0xffFEF2D0),
            // surfaceTintColor: const Color(0xffFEF2D0),
            // shadowColor: const Color(0xffFEF2D0),
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            // backgroundColor: dashboardColor,
            // surfaceTintColor: dashboardColor,
            // shadowColor: dashboardColor,
            // backgroundColor: const Color.fromARGB(255, 255, 244, 228),
            // surfaceTintColor: const Color.fromARGB(255, 255, 244, 228),
            // shadowColor: const Color.fromARGB(255, 255, 244, 228),
            contentPadding:
                const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
            title: Text(gallery != null ? "Edit Gallery" : "Add Gallery"),
            content: SizedBox(
              width: 280,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: priorityCtrl,
                    cursorHeight: 20,
                    decoration: inpDecor().copyWith(labelText: 'Priority No'),
                  ),
                  const SizedBox(height: 20),
                  InkWell(
                    onTap: () async {
                      final res = await ImagePickerService()
                          .pickImageNew(context, useCompressor: true);
                      if (res != null) {
                        selectedImage = res;
                      }
                      setState2(() {});
                    },
                    borderRadius: BorderRadius.circular(3),
                    child: Container(
                        width: double.maxFinite,
                        height: 150,
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(7),
                            // color: const Color.fromARGB(9, 0, 0, 0),
                            color: Colors.transparent),
                        child: selectedImage != null
                            ? Image.memory(
                                selectedImage!.uInt8List,
                                width: double.maxFinite,
                                fit: BoxFit.cover,
                              )
                            : image != null
                                ? CachedNetworkImage(
                                    imageUrl: image,
                                    fit: BoxFit.cover,
                                  )
                                : const Icon(CupertinoIcons
                                    .photo_fill_on_rectangle_fill)),
                  )

                  // const SizedBox(height: 20),
                ],
              ),
            ),
            actionsAlignment: MainAxisAlignment.center,
            actions: loading
                ? [
                    const Center(
                      child: SizedBox(
                        height: 25,
                        width: 25,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                          color: themeColor,
                        ),
                      ),
                    )
                  ]
                : [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: themeColor,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: () async {
                        if (loading) return;

                        if (priorityCtrl.text.trim().isEmpty) {
                          showAppSnackBar(context, 'Priority number required');
                        }
                        if (selectedImage == null && gallery?.image == null) {
                          showErrorAppSnackBar(context, 'Image required');
                          return;
                        }
                        if (int.tryParse(priorityCtrl.text.trim()) == null) {
                          showErrorAppSnackBar(
                              context, 'only numeric value required');
                          return;
                        }

                        try {
                          setState2(() {
                            loading = true;
                          });

                          final priorityNo = int.tryParse(priorityCtrl.text);
                          String? uploadedImage;

                          uploadedImage = selectedImage != null
                              ? await uploadGalleryFiles(selectedImage!)
                              : image;

                          final data = {
                            'image': uploadedImage,
                            'priorityNo': priorityNo,
                          };
                          gallery != null
                              ? await FBFireStore.gallery
                                  .doc(gallery.docId)
                                  .update(data)
                              : await FBFireStore.gallery.add(data);
                          setState2(() {
                            loading = false;
                          });
                          priorityCtrl.clear();
                          if (context.mounted) {
                            Navigator.of(context).pop();
                            showAppSnackBar(
                                context,
                                gallery != null
                                    ? 'Gallery Updated'
                                    : 'Gallery Added');
                            // ScaffoldMessenger.of(context).showSnackBar(snackBar);
                          }
                        } catch (e) {
                          debugPrint(e.toString());
                          showAppSnackBar(context, e.toString());
                        }
                      },
                      child: const Text("Add"),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text("Cancel"),
                    ),
                  ],
          );
        });
      },
    );
  }

  // _imagePicker() async {
  //   final res =
  //       await ImagePickerService().pickImageNew(context, useCompressor: true);
  //   if (res == null) {
  //     return;
  //   }
  //   final uploadedIamge = await uploadGalleryFiles(res);
  // }
}
