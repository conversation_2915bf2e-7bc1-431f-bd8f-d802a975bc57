import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';

import '../../controller/home_ctrl.dart';
import '../../shared/methods.dart';
import '../../shared/router.dart';
import '../common/page_header.dart';

class QuestionairePage extends StatefulWidget {
  const QuestionairePage({super.key});

  @override
  State<QuestionairePage> createState() => _QuestionairePageState();
}

class _QuestionairePageState extends State<QuestionairePage> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
            pageTile: 'Questionaire Management',
            pagesubTile: 'Customize questions for clients'),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PageHeaderWithTrailingAndBack(
                  title: 'Questionaire',
                  showTrailing: false,
                ),
                // PageHeaderWithButton(
                //   title: "Questionaire",
                //   button: false,
                //   onPressed: () {},
                // ),
                const SizedBox(height: 20),
                GetBuilder<HomeCtrl>(builder: (hctrl) {
                  final filteredList = hctrl.offerings
                      // .where((element) => element.name
                      //     .toLowerCase()
                      //     .contains(searchController.text.toLowerCase()))
                      .toList();
                  return filteredList.isEmpty
                      ? const Center(child: Text('No Offering available'))
                      : StaggeredGrid.extent(
                          maxCrossAxisExtent: 320,
                          crossAxisSpacing: 20,
                          mainAxisSpacing: 20,
                          children: [
                            ...List.generate(
                              filteredList.length,
                              (index) => InkWell(
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                overlayColor: const WidgetStatePropertyAll(
                                    Colors.transparent),
                                onTap: () {
                                  context.push(
                                      '${Routes.questionaire}/${filteredList[index].docId}');
                                },
                                child: Container(
                                  height: 320,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: Colors.black12, width: 1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      AspectRatio(
                                        aspectRatio: 1.6,
                                        child: CachedNetworkImage(
                                          imageUrl: filteredList[index].image,
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) {
                                            return const AspectRatio(
                                              aspectRatio: 1.6,
                                              child:
                                                  Icon(CupertinoIcons.camera),
                                            );
                                          },
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10.0, vertical: 10),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // SizedBox(height: 10),
                                            Text(
                                              capilatlizeFirstLetter(
                                                  filteredList[index].name),
                                              style: GoogleFonts.zcoolXiaoWei(
                                                  fontSize: 17.5,
                                                  letterSpacing: .8,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                            const SizedBox(height: 7),
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    filteredList[index].desc,
                                                    maxLines: 3,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: GoogleFonts.livvic(
                                                      fontSize: 13,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          ],
                        );
                })
              ],
            ),
          ),
        ),
      ],
    );
  }
}
