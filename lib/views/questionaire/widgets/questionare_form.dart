import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/questionaire_model.dart';
import 'package:wedding_super_admin/services/image_picker.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import '../../../shared/theme.dart';
import '../../common/page_header.dart';

class QuestionareForm extends StatefulWidget {
  const QuestionareForm({super.key, this.offeringId});
  final String? offeringId;
  @override
  State<QuestionareForm> createState() => _QuestionareFormState();
}

class _QuestionareFormState extends State<QuestionareForm> {
  List<OneQuestionModel> questions = [];
  QuestionnaireModel? questionaire;
  OneQuestionModel? radioDefalutQuestion;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      final offering = ctrl.offerings
          .firstWhereOrNull((element) => element.docId == widget.offeringId);
      questionaire = ctrl.allQuestionaire.firstWhereOrNull(
          (element) => element.offeringId == widget.offeringId);
      if (questionaire != null) {
        questions = questionaire!.questions;
        radioDefalutQuestion =
            questions.firstWhereOrNull((element) => element.defaultt);
      }
      return SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PageHeaderWithTrailingAndBack(
              title:
                  capilatlizeFirstLetter(offering?.name ?? "Questionaire Form"),
              showTrailing: true,
              trailing: ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  backgroundColor: themeColor,
                  elevation: 0,
                  overlayColor: Colors.transparent,
                  // shadowColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4)),
                  // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                ),
                onPressed: () async {
                  await addEditQuestionDialog(context, null, questions);
                },
                icon: const Icon(
                  CupertinoIcons.add,
                  size: 20,
                ),
                label: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text("Question",
                      style: GoogleFonts.livvic(
                        letterSpacing: 1.3,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      )),
                ),
              ),
            ),
            const SizedBox(height: 20),
            if (questions.isNotEmpty) const Text("* NQ = Next Question"),

            const SizedBox(height: 20),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (context, index) {
                // return const Divider(
                //   color: Color.fromARGB(255, 63, 63, 63),
                //   height: 50,
                //   thickness: .3,
                // );
                return const SizedBox(height: 30);
              },
              itemCount: questions.length,
              itemBuilder: (context, index) {
                final currentQuestion = questions[index];

                return Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                  // decoration: const BoxDecoration(color: Color.fromARGB(16, 0, 0, 0)),
                  decoration: BoxDecoration(
                      color: themeData.scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(
                        color: dividerColor,
                        // color: const Color.fromARGB(10, 0, 0, 0),
                      )
                      // boxShadow: const [
                      //   BoxShadow(
                      //     offset: Offset(0, 0),
                      //     color: Colors.black12,
                      //     blurRadius: 1.5,
                      //     // spreadRadius: 2.5,
                      //   ),
                      // ],
                      ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Question ${index + 1}",
                              style: const TextStyle(
                                color: Color(0xff4F4F4F),
                                fontWeight: FontWeight.w600,
                                fontSize: 15.5,
                              ),
                            ),
                            IconButton(
                                onPressed: () async {
                                  showDialog(
                                    context: context,
                                    builder: (context) {
                                      return AlertDialog(
                                        backgroundColor: Colors.white,
                                        surfaceTintColor: Colors.white,
                                        title: const Text("Delete Question"),
                                        content: const Text(
                                            "Are you sure you want to delete this question?"),
                                        actions: [
                                          TextButton(
                                            onPressed: () async {
                                              questions.removeAt(index);
                                              await FBFireStore.questionaire
                                                  .doc(questionaire?.docId)
                                                  .update({
                                                'questions': questions
                                                    .map((e) => e.toJson())
                                                    .toList(),
                                              });
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text("Yes"),
                                          ),
                                          TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text("No"),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                                icon: const Icon(CupertinoIcons.delete))
                          ],
                        ),
                      ),
                      const SizedBox(height: 15),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Radio<OneQuestionModel>(
                            value: currentQuestion,
                            groupValue: radioDefalutQuestion,
                            onChanged: (value) {
                              radioDefalutQuestion = value;
                              updateDefaultQuestion();
                            },
                          ),
                          const SizedBox(width: 15),
                          questionDetails(currentQuestion),
                          const SizedBox(width: 15),
                          /*   Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              IconButton(
                                  onPressed: () {
                                    questions.removeAt(index);
                                    setState(() {});
                                  },
                                  icon: const Icon(CupertinoIcons.delete)),
                              const SizedBox(height: 10),
                              Tooltip(
                                message: 'Duplicate',
                                child: IconButton(
                                    onPressed: () async {
                                      if (questions.isEmpty) return;
                                      final toDuplicateoneQuestion = oneQuestion;
                                      questions.insert(
                                        index + 1,
                                        oneQuestionInputModel(
                                          priceCtrl:
                                              toDuplicateoneQuestion.priceCtrl,
                                          vId: getRandomId(8),
                                          sizeCtrl: TextEditingController(
                                              text: toDuplicateoneQuestion
                                                  .sizeCtrl.text),
                                          colorCtrl: TextEditingController(
                                              text: toDuplicateoneQuestion
                                                  .colorCtrl.text),
                                          show: toDuplicateoneQuestion.show,
                                          descriptionCtrl:
                                              TextEditingController(
                                                  text: toDuplicateoneQuestion
                                                      .descriptionCtrl.text),
                                          // fixedPriceCtrl: TextEditingController(
                                          //     text: toDuplicateoneQuestion
                                          //         .fixedPriceCtrl.text),
                                          materialCtrl: TextEditingController(
                                              text: toDuplicateoneQuestion
                                                  .materialCtrl.text),
                                          // priceType: toDuplicateoneQuestion.priceType,
                                          // priceRange: toDuplicateoneQuestion.priceRange,
                                          uploadedImages: [
                                            ...toDuplicateoneQuestion.uploadedImages
                                          ],
                                          newImages: [
                                            ...toDuplicateoneQuestion.newImages
                                          ],
                                        ),
                                      );
                                      setState(() {});
                                      await Future.delayed(
                                          const Duration(milliseconds: 200));
                                      scrCtrl.animateTo(
                                          scrCtrl.initialScrollOffset + 400,
                                          duration: Durations.medium1,
                                          curve: Curves.linear);
                                    },
                                    icon: const Icon(CupertinoIcons
                                        .plus_rectangle_on_rectangle)),
                              ),
                            ],
                          ),
                         */
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
            // const SizedBox(height: 40),
            // _tagsDetails(),
            // const SizedBox(height: 40),
            // _subCat(),
            const SizedBox(height: 20),
          ],
        ),
      );
    });
  }

  questionDetails(OneQuestionModel oneQuestion) {
    List<String> options = [];
    List<String?> optionNextQuesIds = [];
    List<String?> optionImages = [];
    List<List<String>> optionsTags = [];

// selectedQuestionType = oneQuestion.type;
    if (oneQuestion.answer != null) {
      for (var element in oneQuestion.answer ?? <AnswerModel>[]) {
        options.add(element.optionString);
        optionImages.add(element.imageUrl);
        optionNextQuesIds.add(element.nextQuestionId);
        optionsTags.add(element.tags);
      }

      // oneQuestion.answer!.forEach((key, value) {
      //   options.add(key);
      //   optionNextQuesIds.add(value.nextQuestionId);
      //   optionsTags.add(value.tags);
      // });
    }

    return Expanded(
        child: Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: InkWell(
        onTap: () async {
          await addEditQuestionDialog(context, oneQuestion, questions);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Q).",
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    // letterSpacing: .8,
                  ),
                ),
                const SizedBox(width: 5),
                Text(
                  "${oneQuestion.question} (${getQuestionType(oneQuestion.type)})",
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.w400,
                    fontSize: 15,
                    letterSpacing: .7,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 5),
            Row(
              children: [
                Text(
                  "NQ).",
                  style: GoogleFonts.livvic(
                    fontSize: 11, fontWeight: FontWeight.w500,
                    // fontSize: 15,
                    // letterSpacing: 1,
                  ),
                ),
                const SizedBox(width: 5),
                Text(
                  questions
                          .firstWhereOrNull(
                              (element) => element.id == oneQuestion.nextQuesId)
                          ?.question ??
                      "-",
                  style: GoogleFonts.livvic(
                    fontSize: 13, fontStyle: FontStyle.italic,
                    fontWeight: FontWeight.w500,
                    // fontSize: 15,
                    // letterSpacing: 1,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            // if (oneQuestion.type == QuestionTypes.textfield)
            //   const Text("user input"),
            if (oneQuestion.type != QuestionTypes.textfield)
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: options.length,
                itemBuilder: (context, index) {
                  final nextQuestionString = questions
                          .firstWhereOrNull((element) =>
                              element.id == optionNextQuesIds[index])
                          ?.question ??
                      "-";
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${(index + 1).toString()}).",
                        style: GoogleFonts.ptSans(),
                      ),
                      const SizedBox(width: 5),
                      Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              options[index],
                              style: GoogleFonts.ptSans(),
                            ),
                            if (getQuestionType(oneQuestion.type) ==
                                QuestionTypes.single)
                              Row(
                                children: [
                                  Text(
                                    "NQ). ",
                                    style: GoogleFonts.livvic(
                                      fontSize: 11,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  Text(
                                    nextQuestionString,
                                    style: GoogleFonts.livvic(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                          ]),
                    ],
                  );
                },
                separatorBuilder: (context, index) {
                  return const SizedBox(height: 10);
                },
              ),
            /* Wrap(
                alignment: WrapAlignment.start,
                runSpacing: 15,
                runAlignment: WrapAlignment.start,
                spacing: 15,
                children: [
                  ...List.generate(
                    options.length,
                    (index) {
                      final nextQuestionString = questions.firstWhereOrNull(
                          (element) => element.id == optionNextQuesIds[index]);
                      return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(options[index]),
                            Text("NQ) ${nextQuestionString ?? "-"}"),
                          ]);
                    },
                  )
                ],
              ), */
          ],
        ),
      ),
    ));
  }

  updateDefaultQuestion() async {
    List<Map<String, dynamic>> newQuestions = [];
    for (var element in questions) {
      newQuestions.add({
        'id': element.id,
        'question': element.question,
        'type': element.type,
        'nextQuesId': element.nextQuesId,
        'createdAt': element.createdAt,
        'defaultt': element.id == radioDefalutQuestion?.id,
        'answer': element.answer?.map((a) => a.toJson()).toList(),
        // element.answer?.map((key, value) => MapEntry(key, value.toJson())),
      });
    }
    await FBFireStore.questionaire
        .doc(questionaire?.docId)
        .update({'questions': newQuestions});
  }

  Future<dynamic> addEditQuestionDialog(
    BuildContext context,
    OneQuestionModel? editQuestion,
    List<OneQuestionModel> offeringallquestions,
  ) {
    String? selectedQuestionType;
    List<String> questionTypes = [
      QuestionTypes.single,
      QuestionTypes.multiple,
      QuestionTypes.textfield,
    ];
    List<TextEditingController> options = [];
    List<TextEditingController> tagsCtrl = [];
    List<String?> optionNextQuesIds = [];
    List<String?> optionImages = [];
    List<List<String>> optionsTags = [];
    String? questionNextQuesId;
    bool onSubmitLoad = false;
    final questionCtrl = TextEditingController();

    if (editQuestion != null) {
      questionCtrl.text = editQuestion.question;
      selectedQuestionType = editQuestion.type;

      if (editQuestion.answer != null) {
        for (var element in editQuestion.answer ?? <AnswerModel>[]) {
          options.add(TextEditingController(text: element.optionString));
          tagsCtrl.add(TextEditingController());
          optionImages.add(element.imageUrl);
          optionNextQuesIds.add(element.nextQuestionId);
          optionsTags.add(element.tags);
        }
        // editQuestion.answer!.forEach((key, value) {
        //   options.add((TextEditingController(text: key)));
        //   optionNextQuesIds.add(value.nextQuestionId);
        //   optionsTags.add(value.tags);
        // });
      }
      questionNextQuesId = editQuestion.nextQuesId;
    }
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return Dialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            clipBehavior: Clip.antiAlias,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            child: SingleChildScrollView(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                constraints: const BoxConstraints(maxWidth: 850),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Text(
                          editQuestion != null
                              ? "Edit Question"
                              : "New Question",
                          style: GoogleFonts.zcoolXiaoWei(fontSize: 25),
                        ),
                        const Spacer(),
                        InkWell(
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                            ),
                            child: const Icon(
                              CupertinoIcons.xmark,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 7),
                    const Text(
                      "NQ: Next Question",
                      style: TextStyle(fontSize: 12),
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      "Question",
                      style: TextStyle(fontSize: 16),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: questionCtrl,
                            decoration: inpDecor().copyWith(
                              contentPadding: const EdgeInsets.only(right: 10),
                              // prefix: Text("Q)."),
                              border: UnderlineInputBorder(
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade400)),
                              enabledBorder: UnderlineInputBorder(
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade400)),
                              focusedBorder: UnderlineInputBorder(
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade400)),
                              hoverColor: Colors.transparent,
                              // labelText: 'Question',
                              hintText: ' Write question',
                              hintStyle: GoogleFonts.livvic(
                                fontSize: 13,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        if ((selectedQuestionType != null) &&
                            (selectedQuestionType != questionTypes[2])) ...[
                          const SizedBox(width: 17),
                          ElevatedButton(
                            // icon: Icon(CupertinoIcons.add),
                            onPressed: () {
                              options.add(TextEditingController());
                              tagsCtrl.add(TextEditingController());
                              optionsTags.add([]);
                              optionImages.add(null);
                              optionNextQuesIds.add(null);
                              setState2(() {});
                            },
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(3)),
                              padding: const EdgeInsets.all(15),
                            ),
                            child: Row(
                              children: [
                                const Icon(CupertinoIcons.add, size: 20),
                                const SizedBox(width: 5),
                                Text(
                                  "Option",
                                  style: GoogleFonts.livvic(
                                      fontSize: 15, letterSpacing: 1),
                                ),
                              ],
                            ),
                          ),
                        ]
                      ],
                    ),
                    const SizedBox(height: 20),

                    // QUESTION NEXT QUESTION SELECTOR

                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text("NQ)."),
                        const SizedBox(width: 10),
                        if (questionNextQuesId != null) ...[
                          Expanded(
                              child: Text(questions
                                      .firstWhereOrNull((element) =>
                                          element.id == questionNextQuesId)
                                      ?.question ??
                                  "")),
                          const SizedBox(width: 10),
                        ],
                        InkWell(
                          // hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          // splashColor: Colors.transparent,
                          onTap: () async {
                            // QUESTION NEXT QUESTION SELECTOR DIALOG
                            List<OneQuestionModel> otherQuestions = [];
                            otherQuestions.clear();
                            otherQuestions.addAll(offeringallquestions);
                            otherQuestions.removeWhere(
                                (element) => element.id == editQuestion?.id);
                            if (otherQuestions.isNotEmpty) {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  String? selectedQuestionNextQuestion =
                                      questionNextQuesId;
                                  return StatefulBuilder(
                                      builder: (context, setState3) {
                                    return Dialog(
                                      backgroundColor: Colors.white,
                                      surfaceTintColor: Colors.white,
                                      shape: const RoundedRectangleBorder(),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 10, horizontal: 15),
                                        constraints: const BoxConstraints(
                                            maxWidth: 800, maxHeight: 800),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "Select Question",
                                              style: GoogleFonts.zcoolXiaoWei(
                                                fontSize: 22,
                                                // color: themeColor,
                                              ),
                                            ),
                                            const SizedBox(height: 20),
                                            ListView.separated(
                                              itemCount: otherQuestions.length,
                                              shrinkWrap: true,
                                              physics:
                                                  const ClampingScrollPhysics(),
                                              itemBuilder: (context, index) {
                                                return Row(
                                                  children: [
                                                    Radio(
                                                      value:
                                                          otherQuestions[index]
                                                              .id,
                                                      groupValue:
                                                          selectedQuestionNextQuestion,
                                                      onChanged: (value) {
                                                        selectedQuestionNextQuestion =
                                                            value;
                                                        setState3(() {});
                                                      },
                                                    ),
                                                    const SizedBox(width: 5),
                                                    Text(
                                                        "Q). ${otherQuestions[index].question}"),
                                                  ],
                                                );
                                              },
                                              separatorBuilder:
                                                  (context, index) {
                                                return const SizedBox(
                                                    height: 10);
                                              },
                                            ),
                                            const SizedBox(height: 20),
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: InkWell(
                                                    onTap: () {
                                                      if (selectedQuestionNextQuestion ==
                                                          null) {
                                                        showAppSnackBar(context,
                                                            'No Next Question Selected');
                                                        return;
                                                      }
                                                      Navigator.of(context)
                                                          .pop();
                                                      questionNextQuesId =
                                                          selectedQuestionNextQuestion;
                                                      setState2(() {});
                                                    },
                                                    child: Container(
                                                      // width: double.maxFinite,
                                                      padding:
                                                          const EdgeInsets.all(
                                                              10),
                                                      child: const Center(
                                                          child: Text("Done")),
                                                    ),
                                                  ),
                                                ),
                                                const SizedBox(width: 10),
                                                Expanded(
                                                  child: InkWell(
                                                    onTap: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                    child: Container(
                                                      // width: double.maxFinite,
                                                      padding:
                                                          const EdgeInsets.all(
                                                              10),
                                                      child: const Center(
                                                          child:
                                                              Text("Cancel")),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  });
                                },
                              );
                            } else {
                              showAppSnackBar(
                                  context, 'No questions available to select');
                            }
                          },
                          child: Container(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 5, horizontal: 22),
                              decoration: const BoxDecoration(
                                  border: Border(
                                      bottom: BorderSide(color: themeColor))),
                              child: Text(
                                "Choose NQ",
                                style: GoogleFonts.livvic(
                                    fontSize: 15, letterSpacing: 1),
                              )),
                        ),
                      ],
                    ),

                    const SizedBox(height: 15),
                    Wrap(
                      spacing: 8,
                      alignment: WrapAlignment.start,
                      runSpacing: 8,
                      runAlignment: WrapAlignment.start,
                      children: [
                        ...List.generate(
                          questionTypes.length,
                          (index) {
                            final selected =
                                selectedQuestionType == questionTypes[index];
                            return InkWell(
                              borderRadius: BorderRadius.circular(7),
                              onTap: () {
                                selectedQuestionType = questionTypes[index];
                                options.clear();
                                optionNextQuesIds.clear();
                                optionsTags.clear();
                                optionImages.clear();
                                setState2(() {});
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color: selected ? null : Colors.white,
                                  borderRadius: BorderRadius.circular(7),
                                  border: selected
                                      ? Border.all(color: Colors.black12)
                                      : null,
                                ),
                                padding: const EdgeInsets.symmetric(
                                    vertical: 4, horizontal: 10),
                                child: Text(
                                  questionTypes[index],
                                ),
                              ),
                            );
                          },
                        )
                      ],
                    ),
                    const SizedBox(height: 25),
                    ...List.generate(
                      options.length,
                      (index) {
                        // final optcontroller =
                        return Padding(
                          padding: EdgeInsets.only(
                              bottom: index == options.length - 1 ? 0 : 20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    'Option ${index + 1}',
                                    style: const TextStyle(fontSize: 15),
                                  ),
                                  const Spacer(),
                                  IconButton(
                                    onPressed: () {
                                      options.removeAt(index);
                                      if (selectedQuestionType !=
                                          questionTypes[2]) {
                                        optionNextQuesIds.removeAt(index);
                                        optionsTags.removeAt(index);
                                        optionImages.removeAt(index);
                                      }
                                      setState2(() {});
                                    },
                                    icon: const Icon(CupertinoIcons.delete),
                                  ),
                                ],
                              ),
                              // SizedBox(height: 10),
                              const Divider(
                                color: Colors.black,
                                thickness: .4,
                                height: 0,
                              ),
                              const SizedBox(height: 18),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      controller: options[index],
                                      decoration: inpDecor().copyWith(
                                        // labelText: 'Option ${index + 1}',
                                        border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(5),
                                            borderSide: const BorderSide(
                                                color: Color.fromARGB(
                                                    12, 0, 0, 0))),
                                        enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(5),
                                            borderSide: const BorderSide(
                                                color: Color.fromARGB(
                                                    12, 0, 0, 0))),
                                        focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(5),
                                            borderSide: const BorderSide(
                                                color: Color.fromARGB(
                                                    12, 0, 0, 0))),
                                        hintText: ' Write option',
                                        hintStyle: GoogleFonts.livvic(
                                            fontSize: 14, color: Colors.grey),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 13),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  InkWell(
                                    highlightColor: Colors.transparent,
                                    hoverColor: Colors.transparent,
                                    splashColor: Colors.transparent,
                                    onTap: () async {
                                      final optionselectedimage =
                                          await ImagePickerService()
                                              .pickImageNew(context,
                                                  useCompressor: true);
                                      if (optionselectedimage != null) {
                                        final imageurl = await uploadMiscFile(
                                            optionselectedimage);
                                        optionImages[index] = imageurl;
                                        setState2(() {});
                                      }
                                    },
                                    child: const Icon(
                                      CupertinoIcons.add_circled,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  const Text(
                                    "Image.",
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(width: 10),
                                  if (optionImages[index] != null)
                                    SizedBox(
                                      // color: Colors.black38,
                                      height: 90,
                                      width: 90,
                                      child: Stack(
                                        children: [
                                          Center(
                                            child: Container(
                                              height: 80,
                                              width: 80,
                                              clipBehavior: Clip.antiAlias,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(4)),
                                              child: Image.network(
                                                optionImages[index]!,
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          ),
                                          Align(
                                            alignment: Alignment.topRight,
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  right: 2.0, top: 2),
                                              child: InkWell(
                                                highlightColor:
                                                    Colors.transparent,
                                                hoverColor: Colors.transparent,
                                                splashColor: Colors.transparent,
                                                onTap: () {
                                                  optionImages[index] = null;
                                                  setState2(() {});
                                                },
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(1),
                                                  decoration:
                                                      const BoxDecoration(
                                                          color: Colors.white,
                                                          shape:
                                                              BoxShape.circle,
                                                          boxShadow: [
                                                        BoxShadow(
                                                            color:
                                                                Colors.black26,
                                                            blurRadius: 2,
                                                            // spreadRadius: 0,
                                                            spreadRadius: 1,
                                                            offset:
                                                                Offset(0, 2)),
                                                      ]),
                                                  child: const Icon(
                                                    CupertinoIcons.xmark,
                                                    size: 14,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                              if (selectedQuestionType == QuestionTypes.single)
                                const SizedBox(height: 10),

                              // OPTION NEXT QUESTION SELECTOR
                              if (selectedQuestionType == QuestionTypes.single)
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    InkWell(
                                      highlightColor: Colors.transparent,
                                      hoverColor: Colors.transparent,
                                      splashColor: Colors.transparent,
                                      onTap: () async {
                                        // OPTION NEXT QUESTION SELECTOR DIALOG
                                        List<OneQuestionModel> otherQuestions =
                                            [];
                                        otherQuestions.clear();
                                        otherQuestions
                                            .addAll(offeringallquestions);
                                        otherQuestions.removeWhere((element) =>
                                            element.id == editQuestion?.id);
                                        if (otherQuestions.isNotEmpty) {
                                          showDialog(
                                            context: context,
                                            builder: (context) {
                                              String?
                                                  selectedOptionNextQuestion =
                                                  optionNextQuesIds[index];
                                              return StatefulBuilder(builder:
                                                  (context, setState3) {
                                                List<OneQuestionModel>
                                                    otherQuestions = [];
                                                otherQuestions.clear();
                                                otherQuestions.addAll(
                                                    offeringallquestions);
                                                otherQuestions.removeWhere(
                                                    (element) =>
                                                        element.id ==
                                                        editQuestion?.id);

                                                return Dialog(
                                                  backgroundColor: Colors.white,
                                                  surfaceTintColor:
                                                      Colors.white,
                                                  shape:
                                                      const RoundedRectangleBorder(),
                                                  child: Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 10,
                                                        horizontal: 15),
                                                    constraints:
                                                        const BoxConstraints(
                                                            maxWidth: 800,
                                                            maxHeight: 800),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          "Select Question",
                                                          style: GoogleFonts
                                                              .zcoolXiaoWei(
                                                            fontSize: 22,
                                                            // color: themeColor,
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            height: 20),
                                                        ListView.separated(
                                                          itemCount:
                                                              otherQuestions
                                                                  .length,
                                                          shrinkWrap: true,
                                                          physics:
                                                              const ClampingScrollPhysics(),
                                                          itemBuilder: (context,
                                                              nextquestionIndex) {
                                                            return Row(
                                                              children: [
                                                                Radio(
                                                                  value: otherQuestions[
                                                                          nextquestionIndex]
                                                                      .id,
                                                                  groupValue:
                                                                      selectedOptionNextQuestion,
                                                                  onChanged:
                                                                      (value) {
                                                                    selectedOptionNextQuestion =
                                                                        value;
                                                                    setState3(
                                                                        () {});
                                                                  },
                                                                ),
                                                                const SizedBox(
                                                                    width: 5),
                                                                Text(
                                                                    "Q). ${otherQuestions[nextquestionIndex].question}"),
                                                              ],
                                                            );
                                                          },
                                                          separatorBuilder:
                                                              (context, index) {
                                                            return const SizedBox(
                                                                height: 10);
                                                          },
                                                        ),
                                                        const SizedBox(
                                                            height: 20),
                                                        Row(
                                                          children: [
                                                            Expanded(
                                                              child: InkWell(
                                                                onTap: () {
                                                                  if (selectedOptionNextQuestion ==
                                                                      null) {
                                                                    showAppSnackBar(
                                                                        context,
                                                                        'No Next Question Selected');
                                                                    return;
                                                                  }
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                  optionNextQuesIds[
                                                                          index] =
                                                                      selectedOptionNextQuestion;
                                                                  setState2(
                                                                      () {});
                                                                },
                                                                child:
                                                                    Container(
                                                                  // width: double.maxFinite,
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .all(
                                                                          10),
                                                                  child: const Center(
                                                                      child: Text(
                                                                          "Done")),
                                                                ),
                                                              ),
                                                            ),
                                                            const SizedBox(
                                                                width: 10),
                                                            Expanded(
                                                              child: InkWell(
                                                                onTap: () {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                },
                                                                child:
                                                                    Container(
                                                                  // width: double.maxFinite,
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .all(
                                                                          10),
                                                                  child: const Center(
                                                                      child: Text(
                                                                          "Cancel")),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              });
                                            },
                                          );
                                        } else {
                                          showAppSnackBar(context,
                                              'No questions available to select');
                                        }
                                      },
                                      child: const Icon(
                                        CupertinoIcons.add_circled,
                                        size: 20,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    const Text(
                                      "NQ).",
                                      style: TextStyle(fontSize: 14),
                                    ),
                                    const SizedBox(width: 10),
                                    if (optionNextQuesIds[index] != null) ...[
                                      Expanded(
                                        child: Text(questions
                                                .firstWhereOrNull((element) =>
                                                    element.id ==
                                                    optionNextQuesIds[index])
                                                ?.question ??
                                            "-"),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          optionNextQuesIds[index] = null;
                                          setState2(() {});
                                        },
                                        child: const Icon(CupertinoIcons.xmark),
                                      )
                                    ],
                                  ],
                                ),
                              const SizedBox(height: 10),
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () async {
                                      final offeringProductSnap =
                                          await FBFireStore.products
                                              .where('mainCatDocId',
                                                  isEqualTo: widget.offeringId)
                                              .get();
                                      final allTagsListofList =
                                          offeringProductSnap.docs
                                              .map((e) => e.data()['tags']
                                                  as List<String>)
                                              .toList();

                                      List<String> allTags = [];
                                      for (var tagsList in allTagsListofList) {
                                        allTags.addAll(tagsList
                                            .map((e) => e.toLowerCase().trim())
                                            .toList());
                                      }
                                      if (allTags.isEmpty) {
                                        showAppSnackBar(
                                            context, 'No tags added');
                                        return;
                                      }
                                      if (allTags.isNotEmpty) {
                                        showDialog(
                                          context: context,
                                          builder: (context) {
                                            List<String> selectedTags =
                                                optionsTags[index];
                                            return StatefulBuilder(
                                                builder: (context, setState3) {
                                              return Dialog(
                                                backgroundColor: Colors.white,
                                                surfaceTintColor: Colors.white,
                                                shape:
                                                    const RoundedRectangleBorder(),
                                                child: Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      vertical: 10,
                                                      horizontal: 15),
                                                  constraints:
                                                      const BoxConstraints(
                                                          maxWidth: 800,
                                                          maxHeight: 800),
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        "Select Tags",
                                                        style: GoogleFonts
                                                            .zcoolXiaoWei(
                                                          fontSize: 22,
                                                          // color: themeColor,
                                                        ),
                                                      ),
                                                      const SizedBox(
                                                          height: 20),
                                                      ListView.separated(
                                                        itemCount:
                                                            allTags.length,
                                                        shrinkWrap: true,
                                                        physics:
                                                            const ClampingScrollPhysics(),
                                                        itemBuilder: (context,
                                                            tagIndex) {
                                                          final tagSelected =
                                                              selectedTags.contains(
                                                                  allTags[
                                                                      tagIndex]);
                                                          return Row(
                                                            children: [
                                                              Checkbox(
                                                                value:
                                                                    tagSelected,
                                                                onChanged:
                                                                    (value) {
                                                                  if (value ==
                                                                      true) {
                                                                    selectedTags.add(
                                                                        allTags[
                                                                            tagIndex]);
                                                                  }
                                                                  if (value ==
                                                                      false) {
                                                                    selectedTags
                                                                        .removeWhere(
                                                                      (element) =>
                                                                          element ==
                                                                          allTags[
                                                                              tagIndex],
                                                                    );
                                                                  }
                                                                  setState3(
                                                                      () {});
                                                                },
                                                              ),
                                                              const SizedBox(
                                                                  width: 5),
                                                              Text(capilatlizeFirstLetter(
                                                                  allTags[
                                                                      tagIndex])),
                                                            ],
                                                          );
                                                        },
                                                        separatorBuilder:
                                                            (context, index) {
                                                          return const SizedBox(
                                                              height: 10);
                                                        },
                                                      ),
                                                      const SizedBox(
                                                          height: 20),
                                                      Row(
                                                        children: [
                                                          Expanded(
                                                            child: InkWell(
                                                              onTap: () {
                                                                if (selectedTags
                                                                    .isEmpty) {
                                                                  showAppSnackBar(
                                                                      context,
                                                                      'No Tags Selected');
                                                                  return;
                                                                }
                                                                Navigator.of(
                                                                        context)
                                                                    .pop();
                                                                optionsTags[
                                                                        index] =
                                                                    selectedTags;
                                                                setState2(
                                                                    () {});
                                                              },
                                                              child: Container(
                                                                // width: double.maxFinite,
                                                                padding:
                                                                    const EdgeInsets
                                                                        .all(
                                                                        10),
                                                                child: const Center(
                                                                    child: Text(
                                                                        "Done")),
                                                              ),
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                              width: 10),
                                                          Expanded(
                                                            child: InkWell(
                                                              onTap: () {
                                                                Navigator.of(
                                                                        context)
                                                                    .pop();
                                                              },
                                                              child: Container(
                                                                // width: double.maxFinite,
                                                                padding:
                                                                    const EdgeInsets
                                                                        .all(
                                                                        10),
                                                                child: const Center(
                                                                    child: Text(
                                                                        "Cancel")),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            });
                                          },
                                        );
                                      } else {
                                        showAppSnackBar(
                                            context, 'No tags added');
                                      }
                                    },
                                    child: const Icon(
                                      CupertinoIcons.add_circled,
                                      size: 20,
                                    ),
                                    // label: const Text("Tags"),
                                  ),
                                  const SizedBox(width: 10),
                                  const Text(
                                    "Tags",
                                    style: TextStyle(fontSize: 14),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 15),

                              TextFormField(
                                controller: tagsCtrl[index],
                                onFieldSubmitted: (value) async {
                                  if (value.trim().isEmpty) {
                                    return;
                                  }
                                  optionsTags[index]
                                      .add(value.toLowerCase().trim());
                                  tagsCtrl[index].clear();
                                  setState2(() {});
                                },
                                decoration: inpDecor().copyWith(
                                    labelText: 'Enter tag name*',
                                    hintText: ' Hit enter once written'),
                              ),
                              if (optionsTags[index].isNotEmpty) ...[
                                const SizedBox(height: 15),
                                Container(
                                  width: double.maxFinite,
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(7)),
                                  child: Wrap(
                                    spacing: 10,
                                    runSpacing: 10,
                                    runAlignment: WrapAlignment.start,
                                    children: [
                                      ...List.generate(
                                        optionsTags[index].length,
                                        (tagindex) {
                                          return Chip(
                                              onDeleted: () {
                                                optionsTags[index]
                                                    .removeAt(tagindex);
                                                setState2(() {});
                                              },
                                              label: Text(optionsTags[index]
                                                  [tagindex]));
                                        },
                                      )
                                    ],
                                  ),
                                )
                              ]
                            ],
                          ),
                        );
                      },
                      //   ),
                      // ],
                    ),
                    const SizedBox(height: 28),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              overlayColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(vertical: 17),
                              textStyle: const TextStyle(fontSize: 16),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5)),
                            ),
                            onPressed: () async {
                              if (questionCtrl.text.trim().isEmpty) {
                                showAppSnackBar(context, 'Question required');

                                return;
                              }
                              if (selectedQuestionType == null) {
                                showAppSnackBar(
                                    context, 'selected question type empty');

                                return;
                              }
                              if ((selectedQuestionType !=
                                      QuestionTypes.textfield) &&
                                  options.isEmpty) {
                                showAppSnackBar(context, 'options empty');
                                return;
                              }
                              if (onSubmitLoad) return;

                              try {
                                onSubmitLoad = true;
                                setState2(() {});

                                List<AnswerModel>? answersMap =
                                    selectedQuestionType ==
                                            QuestionTypes.textfield
                                        ? null
                                        : [];

                                // for (var i = 0; i < options.length; i++) {
                                //   final answerMap = AnswerModel(
                                //     optionString: ,
                                //           nextQuestionId: optionNextQuesIds[i],
                                //           tags: optionsTags[i])
                                //       .toJson();
                                //   answersMap!.addAll(
                                //       {options[i].text.trim(): answerMap});
                                // }
                                if (selectedQuestionType !=
                                    QuestionTypes.textfield) {
                                  for (var i = 0; i < options.length; i++) {
                                    answersMap?.add(AnswerModel(
                                        imageUrl: optionImages[i],
                                        nextQuestionId: optionNextQuesIds[i],
                                        optionString: options[i].text.trim(),
                                        tags: optionsTags[i]));
                                  }
                                }

                                final oneQuestionMap = <String, dynamic>{
                                  'id': editQuestion != null
                                      ? editQuestion.id
                                      : getRandomMix(6),
                                  'question': questionCtrl.text.trim(),
                                  'type': selectedQuestionType,
                                  'nextQuesId': questionNextQuesId,
                                  'defaultt': editQuestion != null
                                      ? editQuestion.defaultt
                                      : false,
                                  'answer': answersMap
                                      ?.map((e) => e.toJson())
                                      .toList(),
                                  'createdAt': editQuestion != null
                                      ? editQuestion.createdAt
                                      : Timestamp.now(),
                                };

                                final batch = FBFireStore.fb.batch();
                                List<OneQuestionModel> questionsList =
                                    <OneQuestionModel>[];

                                final questionsSnap = await FBFireStore
                                    .questionaire
                                    .where('offeringId',
                                        isEqualTo: widget.offeringId)
                                    .get();
                                if (questionsSnap.size > 0) {
                                  final questionaireDocRef = FBFireStore
                                      .questionaire
                                      .doc(questionsSnap.docs.first.id);
                                  final questionData =
                                      QuestionnaireModel.fromSnap(
                                          questionsSnap.docs.first);

                                  questionsList = questionData.questions;
                                  if (editQuestion != null) {
                                    questionsList.removeWhere((element) =>
                                        element.id == editQuestion.id);
                                  }
                                  questionsList.add(OneQuestionModel.fromJson(
                                      oneQuestionMap));

                                  batch.update(questionaireDocRef, {
                                    'questions': questionsList
                                        .map((e) => e.toJson())
                                        .toList(),
                                  });
                                } else {
                                  // if offering id is not created
                                  questionsList.add(OneQuestionModel.fromJson(
                                      oneQuestionMap));

                                  batch.set(FBFireStore.questionaire.doc(), {
                                    'offeringId': widget.offeringId,
                                    'questions': questionsList
                                        .map((e) => e.toJson())
                                        .toList(),
                                  });
                                }

                                await batch.commit();
                                onSubmitLoad = false;
                                setState2(() {});
                                if (context.mounted) {
                                  Navigator.of(context).pop();
                                }
                              } catch (e) {
                                debugPrint(e.toString());
                                onSubmitLoad = false;
                                setState2(() {});
                              }
                            },
                            child: onSubmitLoad
                                ? const Center(
                                    child: SizedBox(
                                      height: 23,
                                      width: 23,
                                      child: CircularProgressIndicator(
                                          // color: Colors.white,
                                          strokeWidth: 2.5),
                                    ),
                                  )
                                : Text(
                                    "Save",
                                    style: GoogleFonts.livvic(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 15.5,
                                        letterSpacing: .8),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }
}
