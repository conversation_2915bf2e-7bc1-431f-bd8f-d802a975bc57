// part of 'add_edit_blog_bloc.dart';

// class AddEditBlogState {
//   ApiResponse? errorData;
// }

// final class AddEditBlogInitial extends AddEditBlogState {}

// final class AddingBlogState extends AddEditBlogState {}

// final class BlogAddedState extends AddEditBlogState {}

// final class UploadingImagesState extends AddEditBlogState {
//   UploadingImagesState();
// }

// final class ImagesUploadedState extends AddEditBlogState {
//   ImagesUploadedState();
// }

// final class AddEditBlogErrorState extends AddEditBlogState {
//   AddEditBlogErrorState({required ApiResponse errorData}) {
//     super.errorData = errorData;
//   }
// }
