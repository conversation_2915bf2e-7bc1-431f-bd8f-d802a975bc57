import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:flutter/src/widgets/text.dart' as textwidget;
import 'package:markdown/markdown.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart';
import 'package:textfield_tags/textfield_tags.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/shared/theme.dart';
import 'package:wedding_super_admin/views/blogs/widgets/custom_text_field.dart';
import '../../../models/blogs.dart';
import '../../../services/image_picker.dart';
import '../../../shared/firebase.dart';
import '../../common/custom_textfeild.dart';

HtmlEditorController hcontroller = HtmlEditorController();

class BlogForm extends StatelessWidget {
  const BlogForm({super.key, this.blogId});
  final String? blogId;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (hctrl) {
      final blog =
          hctrl.blogs.firstWhereOrNull((element) => element.docId == blogId);
      return Padding(
        padding: const EdgeInsets.only(right: 20, left: 20.0, top: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BlogForm2(
              allBlogs: hctrl.blogs,
              blogModel: blog,
            ),
          ],
        ),
      );
    });
  }
}

class BlogForm2 extends StatefulWidget {
  const BlogForm2({super.key, this.blogModel, this.allBlogs});

  final BlogModel? blogModel;
  final List<BlogModel>? allBlogs;
  @override
  State<BlogForm2> createState() => _BlogForm2State();
}

class _BlogForm2State extends State<BlogForm2> {
  bool onLoadSubmit = false;
  late WebViewXController webviewController;
  final scrollController = ScrollController();
  TextEditingController metaTitleTextEditingController =
      TextEditingController();
  TextEditingController metaDescTextEditingController = TextEditingController();
  TextEditingController markdownTextEditingController = TextEditingController();
  TextEditingController permaLinkTextEditingController =
      TextEditingController();
  TextEditingController thumbnailTextEditingController =
      TextEditingController();
  ValueNotifier<String> markdownNotifier = ValueNotifier<String>("");
  List<String> uploadedImages = [];
  List<String> initialTags = [];
  final StringTagController tagsController = StringTagController();
  List<BlogModel> allBlogs = [];
  MultiSelectController otherBlogsSelectController = MultiSelectController();
  List<BlogModel> otherBlogs = [];
  DateTime publishedOn = DateTime.now();

  @override
  void initState() {
    super.initState();
    assignData();
  }

  assignData() async {
    if (widget.blogModel != null) {
      BlogModel blogsModel = widget.blogModel!;

      metaTitleTextEditingController.text = blogsModel.metaTitle;
      publishedOn = blogsModel.publishedOn.toDate();
      markdownNotifier.value =
          markdownTextEditingController.text = blogsModel.markDown;
      thumbnailTextEditingController.text = blogsModel.thumbnail;
      permaLinkTextEditingController.text = blogsModel.permalink;
      uploadedImages = blogsModel.imageLinks;
      if (blogsModel.tags.isNotEmpty) {
        initialTags.addAll(blogsModel.tags);
      }
      if (widget.blogModel?.otherBlogs.isNotEmpty ?? false) {
        final otherBlogSnap = await FBFireStore.blogs
            .where(FieldPath.documentId, whereIn: blogsModel.otherBlogs)
            .get();

        otherBlogs =
            otherBlogSnap.docs.map((e) => BlogModel.fromSnap(e)).toList();
      }

      allBlogs = widget.allBlogs ?? [];

      if (widget.allBlogs != null) {
        List<ValueItem> allValueItems = [];

        for (var element in widget.allBlogs!) {
          allValueItems.add(
            ValueItem(
                label:
                    "${element.metaTitle} ${element.tags.isEmpty ? "" : element.tags.map((e) => e)}",
                value: element.docId),
          );
        }
        otherBlogsSelectController.setOptions(allValueItems);

        for (ValueItem option in otherBlogsSelectController.options) {
          // option.value as BlogModel;
          if (otherBlogs.firstWhereOrNull(
                  (element) => element.docId == option.value) !=
              null) {
            otherBlogsSelectController.addSelectedOption(option);
          }
        }
      }
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                  onPressed: () {
                    context.pop();
                  },
                  icon: const Icon(
                    CupertinoIcons.arrow_left,
                    color: themeColor,
                  )),
              const SizedBox(width: 10),
              textwidget.Text('Blog',
                  style: GoogleFonts.zcoolXiaoWei(
                      fontSize: 28, color: themeColor)),
              Spacer(),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromARGB(255, 186, 127, 103),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10))),

                // titleStyle: const TextStyle(color: Colors.white),
                child: onLoadSubmit
                    ? const Center(
                        child: SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 3.5,
                              color: Colors.white,
                            )),
                      )
                    : Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15.0, vertical: 5),
                        child: textwidget.Text(
                          'Save Blog',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                onPressed: () async {
                  try {
                    if (metaTitleTextEditingController.text.isEmpty ||
                        metaDescTextEditingController.text.isEmpty ||
                        (tagsController.getTags?.isEmpty ?? true) ||
                        otherBlogs.isEmpty ||
                        uploadedImages.isEmpty ||
                        markdownTextEditingController.text.isEmpty ||
                        thumbnailTextEditingController.text.isEmpty) {
                      showAppSnackBar(
                          context, "Fill all the mandatory details first!!");

                      return;
                    }
                    setState(() {
                      onLoadSubmit = true;
                    });
                    final data = {
                      'createdAt': widget.blogModel != null
                          ? widget.blogModel?.createdAt
                          : Timestamp.now(),
                      'updatedAt': Timestamp.now(),
                      'publishedOn': Timestamp.fromDate(publishedOn),
                      'htmlString': markdownToHtml(markdownNotifier.value),
                      'imageLinks': uploadedImages,
                      'isVisible': widget.blogModel != null
                          ? widget.blogModel?.isVisible
                          : true,
                      'show': widget.blogModel != null
                          ? widget.blogModel?.isVisible
                          : false,
                      'lowerTitle': metaTitleTextEditingController.text
                          .trim()
                          .toLowerCase(),
                      'markDown': markdownNotifier.value,
                      'metaTitle': metaTitleTextEditingController.text.trim(),
                      'metaDesc': metaDescTextEditingController.text.trim(),
                      'otherBlogs': otherBlogs.map((e) => e.docId).toList(),
                      'permalink': permaLinkTextEditingController.text,
                      'tags': tagsController.getTags,
                      'thumbnail': thumbnailTextEditingController.text,
                    };
                    final blogRef = widget.blogModel != null
                        ? FBFireStore.blogs.doc(widget.blogModel?.docId)
                        : FBFireStore.blogs.doc();
                    await blogRef.set(data);
                    setState(() {
                      onLoadSubmit = true;
                    });
                    if (context.mounted) {
                      context.pop();
                    }
                  } on Exception catch (e) {
                    setState(() {
                      onLoadSubmit = true;
                    });
                    debugPrint(e.toString());
                  }
                },

                // state is AddingBlogState
                //     ? () {}
                //     : () {
                //         addEditBlogBloc.add(
                //           AddBlogEvent(
                //             blogModel: BlogModel(
                //               id: widget.blogModel?.id,
                //               metaTitle: addEditBlogBloc
                //                   .metaTitleTextEditingController.text,
                //               htmlString: markdownToHtml(
                //                   addEditBlogBloc.markdownNotifier.value),
                //               markdown:
                //                   addEditBlogBloc.markdownNotifier.value,
                //               permalink: addEditBlogBloc
                //                   .permaLinkTextEditingController.text,
                //               tags:
                //                   addEditBlogBloc.tagsController.getTags,
                //               thumbnail: addEditBlogBloc
                //                   .thumbnailTextEditingController.text,
                //               isVisible: true,
                //             ),
                //           ),
                //         );
                //       },
                // child: state is AddingBlogState
                //     ? const CircularProgressIndicator(color: Colors.white)
                //     : null,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        // flex: 1,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            const SizedBox(height: 20),
                            PrimaryButton(
                              height: 30,
                              boxDecoration: BoxDecoration(
                                color: const Color.fromARGB(255, 186, 127, 103),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              padding: const EdgeInsets.all(10),
                              // backgroundColor: Color.fromARGB(255, 186, 127, 103),
                              titleStyle: const TextStyle(color: Colors.white),
                              title: "Upload Images",
                              onTap: () async {
                                List<SelectedImage> pickedImages =
                                    await ImagePickerService().pickImageAndCrop(
                                        context,
                                        useCompressor: true);
                                final res = await uploadBlogFile(pickedImages);
                                uploadedImages.addAll(res);
                                setState(() {});
                              },
                              // onTap: state is UploadingImagesState
                              //     ? () {}
                              //     : () async {
                              //         List<SelectedImage> pickedImages =
                              //             await addEditBlogBloc.imagePickerService
                              //                 .pickImageAndCompress(useCompressor: true);

                              //         addEditBlogBloc.add(
                              //           UploadImagesEvent(imagesToUpload: pickedImages),
                              //         );
                              //       },
                              // child: state is UploadingImagesState
                              //     ? const CircularProgressIndicator(
                              //         color: Colors.white,
                              //       )
                              //     : null,
                            ),
                            const SizedBox(height: 20),
                            uploadedImages.isEmpty
                                ? const Center(
                                    child:
                                        textwidget.Text("No Images Uploaded"),
                                  )
                                : ListView.separated(
                                    shrinkWrap: true,
                                    itemCount: uploadedImages.length,
                                    separatorBuilder:
                                        (BuildContext context, int index) {
                                      return const SizedBox(height: 20);
                                    },
                                    itemBuilder: (context, index) {
                                      return UploadedImageCard(
                                        imageUrl: uploadedImages[index],
                                      );
                                    },
                                  ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        flex: 4,
                        child: Container(
                          decoration: BoxDecoration(
                            // color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: Colors.grey),
                          ),
                          // padding: EdgeInsets.symmetric(horizontal: 5.sp),
                          child: Column(
                            children: [
                              Scrollbar(
                                thumbVisibility: true,
                                controller: scrollController,
                                child: SingleChildScrollView(
                                  controller: scrollController,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10),
                                    child: Column(
                                      children: [
                                        const SizedBox(height: 20),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              flex: 4,
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.end,
                                                    children: [
                                                      ElevatedButton(
                                                          style: OutlinedButton.styleFrom(
                                                              shape: RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              5))),
                                                          onPressed: () async {
                                                            publishedOn = await showDatePicker(
                                                                    context:
                                                                        context,
                                                                    firstDate: DateTime
                                                                            .now()
                                                                        .subtract(Duration(
                                                                            days:
                                                                                1800)),
                                                                    lastDate:
                                                                        DateTime
                                                                            .now()) ??
                                                                publishedOn;
                                                            setState(() {});
                                                          },
                                                          child: textwidget
                                                              .Text(publishedOn
                                                                  .goodDayDate()
                                                                  .toString()))
                                                    ],
                                                  ),
                                                  const SizedBox(height: 20),
                                                  CustomTextField(
                                                    label: "Meta Title",
                                                    controller:
                                                        metaTitleTextEditingController,
                                                  ),

                                                  const SizedBox(height: 20),
                                                  CustomTextField(
                                                    label: "Meta Description",
                                                    controller:
                                                        metaDescTextEditingController,
                                                  ),

                                                  const SizedBox(height: 20),
                                                  CustomTextField(
                                                    enabled: true,
                                                    label: "Thumbnail Image",
                                                    controller:
                                                        thumbnailTextEditingController,
                                                  ),
                                                  const SizedBox(height: 25),
                                                  TextFieldTags<String>(
                                                    initialTags: initialTags,
                                                    textfieldTagsController:
                                                        tagsController,
                                                    textSeparators: const [
                                                      // ' ',
                                                      ',',
                                                      '\n'
                                                    ],
                                                    letterCase:
                                                        LetterCase.normal,
                                                    validator: (String tag) {
                                                      if (tagsController
                                                          .getTags!
                                                          .contains(tag)) {
                                                        return 'You\'ve already entered that';
                                                      }
                                                      return null;
                                                    },
                                                    inputFieldBuilder: (context,
                                                        inputFieldValues) {
                                                      return CustomTextField(
                                                        label: "Tags",
                                                        onTap: () {
                                                          tagsController
                                                              .getFocusNode
                                                              ?.requestFocus();
                                                        },
                                                        controller: inputFieldValues
                                                            .textEditingController,
                                                        focusNode:
                                                            inputFieldValues
                                                                .focusNode,
                                                        prefix: inputFieldValues
                                                                .tags.isNotEmpty
                                                            ? SingleChildScrollView(
                                                                controller:
                                                                    inputFieldValues
                                                                        .tagScrollController,
                                                                scrollDirection:
                                                                    Axis.vertical,
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .only(
                                                                    top: 8,
                                                                    bottom: 8,
                                                                    left: 8,
                                                                  ),
                                                                  child: Wrap(
                                                                      runSpacing:
                                                                          4.0,
                                                                      spacing:
                                                                          4.0,
                                                                      children: inputFieldValues
                                                                          .tags
                                                                          .map((String
                                                                              tag) {
                                                                        return Container(
                                                                          decoration: const BoxDecoration(
                                                                              borderRadius: BorderRadius.all(
                                                                                Radius.circular(20.0),
                                                                              ),
                                                                              color: Color(0xff138AE1)),
                                                                          margin: const EdgeInsets
                                                                              .symmetric(
                                                                              horizontal: 5.0),
                                                                          padding: const EdgeInsets
                                                                              .symmetric(
                                                                              horizontal: 10.0,
                                                                              vertical: 5.0),
                                                                          child:
                                                                              Row(
                                                                            mainAxisAlignment:
                                                                                MainAxisAlignment.start,
                                                                            mainAxisSize:
                                                                                MainAxisSize.min,
                                                                            children: [
                                                                              InkWell(
                                                                                child: textwidget.Text(
                                                                                  tag,
                                                                                  style: const TextStyle(color: Colors.white),
                                                                                ),
                                                                                onTap: () {
                                                                                  //print("$tag selected");
                                                                                },
                                                                              ),
                                                                              const SizedBox(width: 4.0),
                                                                              InkWell(
                                                                                child: const Icon(
                                                                                  Icons.cancel,
                                                                                  size: 14.0,
                                                                                  color: Color(0xfff7f7f7),
                                                                                ),
                                                                                onTap: () {
                                                                                  inputFieldValues.onTagRemoved(tag);
                                                                                },
                                                                              )
                                                                            ],
                                                                          ),
                                                                        );
                                                                      }).toList()),
                                                                ),
                                                              )
                                                            : null,
                                                        onChange:
                                                            inputFieldValues
                                                                .onTagChanged,
                                                        onSubmit:
                                                            inputFieldValues
                                                                .onTagSubmitted,
                                                      );
                                                    },
                                                  ),
                                                  const SizedBox(height: 20),
                                                  CustomTextField(
                                                    mandatory: false,
                                                    label: "Permalink",
                                                    controller:
                                                        permaLinkTextEditingController,
                                                  ),
                                                  // SizedBox(20.h),
                                                  const SizedBox(height: 20),
                                                  Row(
                                                    children: [
                                                      const textwidget.Text(
                                                          "Related Blogs"),
                                                      const textwidget.Text(
                                                        '*',
                                                        style: TextStyle(
                                                            color: Colors.red),
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 20),
                                                  MultiSelectDropDown(
                                                    borderRadius: 5,
                                                    hintStyle: const TextStyle(
                                                        color: Colors.black),
                                                    borderColor: Colors.black,
                                                    borderWidth: 1,
                                                    fieldBackgroundColor:
                                                        Colors.transparent,
                                                    hint:
                                                        "Select related blogs",
                                                    // padding: EdgeInsets.symmetric(
                                                    //     vertical: 1.sp,
                                                    //     horizontal: 1.sp),
                                                    controller:
                                                        otherBlogsSelectController,
                                                    onOptionSelected:
                                                        (options) {
                                                      // otherBlogs.clear();
                                                      for (var ele in options) {
                                                        final addedBlog = widget
                                                            .allBlogs!
                                                            .firstWhere(
                                                                (blog) =>
                                                                    ele.value ==
                                                                    blog.docId);

                                                        bool isContain = otherBlogs
                                                                .firstWhereOrNull(
                                                                    (element) =>
                                                                        ele.value ==
                                                                        element
                                                                            .docId) !=
                                                            null;
                                                        otherBlogs.addIf(
                                                            !isContain,
                                                            addedBlog);
                                                      }
                                                      debugPrint(
                                                          options.toString());
                                                    },
                                                    onOptionRemoved:
                                                        (index, option) {
                                                      BlogModel?
                                                          removedBlogsModel =
                                                          otherBlogs
                                                              .firstWhereOrNull(
                                                                  (element) =>
                                                                      element
                                                                          .docId ==
                                                                      option
                                                                          .value);
                                                      otherBlogs.removeWhere(
                                                        (element) =>
                                                            element.docId ==
                                                            removedBlogsModel
                                                                ?.docId,
                                                      );
                                                    },

                                                    options: widget.allBlogs !=
                                                            null
                                                        ? widget.allBlogs!.map(
                                                            (BlogModel
                                                                blogModel) {
                                                            return ValueItem(
                                                              label: blogModel
                                                                  .metaTitle,
                                                              value: blogModel
                                                                  .docId,
                                                            );
                                                          }).toList()
                                                        : [],
                                                    selectionType:
                                                        SelectionType.multi,
                                                    chipConfig:
                                                        const ChipConfig(
                                                      wrapType: WrapType.wrap,
                                                    ),
                                                    optionSeparator:
                                                        const SizedBox(
                                                            height: 2),
                                                    dropdownHeight: 300,
                                                    dropdownBorderRadius: 10,
                                                    optionBuilder:
                                                        (ctx, item, selected) {
                                                      return Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                          horizontal: 5,
                                                          vertical: 10,
                                                        ),
                                                        child: textwidget.Text(
                                                            item.label),
                                                      );
                                                    },
                                                    selectedOptionIcon:
                                                        const Icon(
                                                            Icons.check_circle),
                                                  ),
                                                  const SizedBox(height: 20),
                                                  const Row(
                                                    children: [
                                                      Row(
                                                        children: [
                                                          textwidget.Text(
                                                              "Content"),
                                                          textwidget.Text(
                                                            '*',
                                                            style: TextStyle(
                                                                color:
                                                                    Colors.red),
                                                          )
                                                        ],
                                                      ),
                                                      SizedBox(width: 20),
                                                      // InkWell(
                                                      //   onTap: () {
                                                      //     showDialog(
                                                      //       context: context,
                                                      //       builder: (BuildContext
                                                      //           context) {
                                                      //         return const MarkdownTips();
                                                      //       },
                                                      //     );
                                                      //   },
                                                      //   child: const Icon(
                                                      //       Icons.info_rounded),
                                                      // ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 10),
                                                  Container(
                                                    margin: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 5.0),
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 10.0,
                                                        vertical: 5.0),
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              7),
                                                      border: Border.all(
                                                          color: Colors.black),
                                                    ),
                                                    // color: Colors.black,
                                                    width: 1000,
                                                    child: Row(
                                                      children: [
                                                        Expanded(
                                                          child:
                                                              SingleChildScrollView(
                                                            child: HtmlEditor(
                                                              htmlToolbarOptions: const HtmlToolbarOptions(
                                                                  toolbarType:
                                                                      ToolbarType
                                                                          .nativeGrid,
                                                                  renderSeparatorWidget:
                                                                      true,
                                                                  renderBorder:
                                                                      false,
                                                                  initiallyExpanded:
                                                                      false),
                                                              controller:
                                                                  hcontroller,

                                                              htmlEditorOptions:
                                                                  HtmlEditorOptions(
                                                                initialText: widget
                                                                    .blogModel
                                                                    ?.htmlString,
                                                                androidUseHybridComposition:
                                                                    true,
                                                                shouldEnsureVisible:
                                                                    true,
                                                                spellCheck:
                                                                    true,
                                                                autoAdjustHeight:
                                                                    true,
                                                                adjustHeightForKeyboard:
                                                                    true,
                                                                hint:
                                                                    "Draft your content here...",
                                                              ),
                                                              // htmlToolbarOptions:
                                                              //     const HtmlToolbarOptions(
                                                              //   toolbarPosition:
                                                              //       ToolbarPosition
                                                              //           .aboveEditor,
                                                              // ),
                                                              otherOptions:
                                                                  const OtherOptions(
                                                                height: 400,
                                                              ),
                                                              callbacks:
                                                                  Callbacks(
                                                                onChangeContent:
                                                                    (p0) {
                                                                  webviewController.loadContent(
                                                                      p0 ?? '',
                                                                      sourceType:
                                                                          SourceType
                                                                              .html);

                                                                  markdownTextEditingController
                                                                          .text =
                                                                      p0 ?? "";
                                                                  markdownNotifier
                                                                          .value =
                                                                      p0 ?? "";
                                                                },
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        // ElevatedButton(
                                                        //     onPressed: () async {
                                                        //       print(
                                                        //           await hcontroller
                                                        //               .getText());
                                                        //     },
                                                        //     child: const HintText(
                                                        //         hintText:
                                                        //             "Print"))
                                                      ],
                                                    ),
                                                  ),
                                                  // CustomTextField(
                                                  //   maxLine: 20,
                                                  //   controller: addEditBlogBloc
                                                  //       .markdownTextEditingController,
                                                  //   onChange: (value) {
                                                  //     addEditBlogBloc
                                                  //         .markdownNotifier
                                                  //         .value = value;
                                                  //   },
                                                  // ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 20),
                              // const SizedBox(height: 20),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      SizedBox(
                        width: 500,
                        // flex: 3,
                        child: Column(
                          children: [
                            ValueListenableBuilder(
                              valueListenable: markdownNotifier,
                              builder: (context, value, _) {
                                return WebViewX(
                                  initialContent: "No Content",
                                  initialSourceType: SourceType.html,
                                  onWebViewCreated: (controller) =>
                                      webviewController = controller,
                                  width: 500,
                                  height: MediaQuery.sizeOf(context).height,
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class UploadedImageCard extends StatefulWidget {
  const UploadedImageCard({
    super.key,
    required this.imageUrl,
  });

  final String imageUrl;

  @override
  State<UploadedImageCard> createState() => _UploadedImageCardState();
}

class _UploadedImageCardState extends State<UploadedImageCard> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        await Clipboard.setData(
          ClipboardData(text: widget.imageUrl),
        );
      },
      child: MouseRegion(
        onEnter: (event) {
          setState(() {
            isHovered = true;
          });
        },
        onExit: (event) {
          setState(() {
            isHovered = false;
          });
        },
        child: AspectRatio(
          aspectRatio: 1,
          child: Stack(
            fit: StackFit.expand,
            children: [
              SizedBox(
                child: Image.network(
                  widget.imageUrl,
                  fit: BoxFit.cover,
                ),
              ),
              AnimatedOpacity(
                opacity: isHovered ? 1 : 0,
                duration: Duration(milliseconds: isHovered ? 250 : 0),
                child: Container(
                  color: Colors.black38,
                  child: const Icon(
                    CupertinoIcons.link,
                    color: Colors.white,
                  ),
                ),
              )
            ],
          ),
        ),
        // child: Column(
        //   children: [
        //     SizedBox(5.h),
        //     Image.network(addEditBlogBloc
        //         .uploadedImages[index]),
        //     SizedBox(10.h),
        //     ElevatedButton(
        //       onPressed: () async {
        //         await Clipboard.setData(
        //           ClipboardData(
        //               text: addEditBlogBloc
        //                       .uploadedImages[
        //                   index]),
        //         );
        //       },
        //       child: const TextWidget.Text(
        //           "Copy Link"),
        //     ),
        //     SizedBox(5.h),
        //   ],
        // ),
      ),
    );
  }
}
