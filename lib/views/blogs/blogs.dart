import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/shared/router.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';

import '../../controller/home_ctrl.dart';
import '../../shared/firebase.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';

class BlogsPage extends StatefulWidget {
  const BlogsPage({super.key});

  @override
  State<BlogsPage> createState() => _BlogsPageState();
}

class _BlogsPageState extends State<BlogsPage> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
            pageTile: 'Blog Management', pagesubTile: 'Manage blog\'s'),
        Expanded(
          child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
              child: Column(children: [
                PageHeaderWithTrailingAndBack(
                  title: 'Blogs',
                  showTrailing: true,
                  trailing: ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeColor,
                      elevation: 0,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4)),
                      // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                    ),
                    onPressed: () {
                      context.push("${Routes.blog}/${null}");
                    },
                    icon: Icon(
                      CupertinoIcons.add,
                      size: 20,
                    ),
                    label: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text('Add',
                          style: GoogleFonts.livvic(
                              letterSpacing: 1.3,
                              fontSize: 14,
                              fontWeight: FontWeight.w500)),
                    ),
                  ),
                ),
                // PageHeaderWithButton(
                //   title: "Blogs",
                //   button: true,
                //   onPressed: () {
                //     // _imagePicker();
                //     context.push("${Routes.blog}/${null}");
                //   },
                //   buttonName: 'Add',
                //   icon: CupertinoIcons.add,
                // ),
                const SizedBox(height: 20),
                GetBuilder<HomeCtrl>(
                  builder: (hctrl) {
                    return StaggeredGrid.extent(
                      maxCrossAxisExtent: 300,
                      mainAxisSpacing: 15,
                      crossAxisSpacing: 15,
                      children: [
                        ...List.generate(
                          hctrl.blogs.length,
                          (index) {
                            final blog = hctrl.blogs[index];
                            return InkWell(
                              onTap: () {
                                context.push("${Routes.blog}/${blog.docId}");
                              },
                              onLongPress: () async {
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    bool loading = false;
                                    return StatefulBuilder(
                                        builder: (context, setState2) {
                                      return AlertDialog(
                                        backgroundColor: Colors.white,
                                        surfaceTintColor: Colors.white,
                                        title: const Text("Delete"),
                                        content: const Text(
                                            "Are you sure you want to delete this blog?"),
                                        actions: loading
                                            ? [
                                                const SizedBox(
                                                  height: 28,
                                                  width: 28,
                                                  child: Center(
                                                      child:
                                                          CircularProgressIndicator(
                                                    strokeWidth: 2.5,
                                                    color: themeColor,
                                                  )),
                                                )
                                              ]
                                            : [
                                                TextButton(
                                                    onPressed: () async {
                                                      if (loading) return;

                                                      try {
                                                        setState2(() {
                                                          loading = true;
                                                        });
                                                        await FBFireStore.blogs
                                                            .doc(blog.docId)
                                                            .delete();
                                                        setState2(() {
                                                          loading = false;
                                                        });
                                                        if (context.mounted) {
                                                          showAppSnackBar(
                                                              context,
                                                              'Blog deleted successfully');
                                                          Navigator.of(context)
                                                              .pop();
                                                        }
                                                      } on Exception catch (e) {
                                                        showErrorAppSnackBar(
                                                            context,
                                                            e.toString());
                                                        debugPrint(
                                                            e.toString());
                                                      }
                                                    },
                                                    child: const Text("Yes")),
                                                TextButton(
                                                    onPressed: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                    child: const Text("No"))
                                              ],
                                      );
                                    });
                                  },
                                );
                              },
                              child: Stack(
                                children: [
                                  InkWell(
                                    // onTap: () {
                                    //   context
                                    //       .push("${Routes.blog}/${blog.docId}");
                                    // },
                                    child: AspectRatio(
                                      aspectRatio: 1,
                                      child: Container(
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: CachedNetworkImage(
                                          imageUrl: blog.thumbnail,
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) {
                                            return Container(
                                              decoration: BoxDecoration(
                                                color: const Color.fromARGB(
                                                    6, 0, 0, 0),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                  AspectRatio(
                                    aspectRatio: 1,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        gradient: LinearGradient(
                                          end: Alignment.topCenter,
                                          begin: Alignment.bottomCenter,
                                          colors: [
                                            Colors.black.withOpacity(0.3),
                                            Colors.black.withOpacity(0.0)
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  AspectRatio(
                                    aspectRatio: 1,
                                    child: Align(
                                      alignment: Alignment.bottomLeft,
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                            left: 12.0, bottom: 8, right: 8),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                blog.metaTitle,
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                                style: const TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 18),
                                              ),
                                            ),
                                            const SizedBox(width: 5),
                                            // Spacer(),
                                            InkWell(
                                              onTap: () async {
                                                await FBFireStore.blogs
                                                    .doc(blog.docId)
                                                    .update({
                                                  'show': !blog.show,
                                                });
                                              },
                                              child: Icon(
                                                blog.show
                                                    ? CupertinoIcons.star_fill
                                                    : CupertinoIcons.star,
                                                // CupertinoIcons.xmark,
                                                size: 25,
                                                color: blog.show
                                                    ? Colors.amber
                                                    : Colors.white30,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  if (!blog.isVisible)
                                    AspectRatio(
                                      aspectRatio: 1,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          color: Colors.black.withOpacity(0.3),
                                        ),
                                      ),
                                    ),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          right: 8.0, top: 8),
                                      child: InkWell(
                                        onTap: () async {
                                          await FBFireStore.blogs
                                              .doc(blog.docId)
                                              .update({
                                            'isVisible': !blog.isVisible,
                                          });
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(3),
                                          decoration: const BoxDecoration(
                                              color: Colors.white,
                                              shape: BoxShape.circle,
                                              boxShadow: [
                                                BoxShadow(
                                                    color: Colors.black26,
                                                    blurRadius: 2,
                                                    // spreadRadius: 0,
                                                    spreadRadius: 1,
                                                    offset: Offset(0, 2)),
                                              ]),
                                          child: Icon(
                                            blog.isVisible == true
                                                ? Icons.visibility_rounded
                                                : Icons.visibility_off_rounded,
                                            // CupertinoIcons.xmark,
                                            size: 18,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        )
                      ],
                    );
                  },
                )
              ])),
        ),
      ],
    );
  }
}
