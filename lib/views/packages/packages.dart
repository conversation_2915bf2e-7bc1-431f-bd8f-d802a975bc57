import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/package_model.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/router.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';
import 'package:wedding_super_admin/views/packages/widgets/package_card.dart';

import '../../shared/methods.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';

class Packages extends StatefulWidget {
  const Packages({super.key});

  @override
  State<Packages> createState() => _PackagesState();
}

class _PackagesState extends State<Packages> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
          pageTile: 'Package Management',
          pagesubTile: 'Create and edit service packages',
        ),
        Expanded(
          child: GetBuilder<HomeCtrl>(builder: (hCtrl) {
            return SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  PageHeaderWithButton(
                    title: "Packages",
                    button: true,
                    onPressed: () async {
                      addEditPackages(
                          context, null, hCtrl.settings?.services ?? []);
                    },
                    buttonName: 'Package',
                    showTrailing: true,
                    trailing: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                            padding: EdgeInsets.all(15),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5)),
                            side: BorderSide(color: Color(0xffE68B7B)),
                            backgroundColor: Colors.white),
                        // icon: Icon(CupertinoIcons.pen),
                        onPressed: () {
                          context.push(Routes.packageOrders);
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.edit,
                                size: 18, color: Color(0xffE68B7B)),
                            SizedBox(width: 10),
                            Text(
                              'Orders',
                              style: TextStyle(color: Color(0xffE68B7B)),
                            ),
                          ],
                        )),
                    icon: CupertinoIcons.money_dollar,
                  ),
                  const SizedBox(height: 20),
                  if (hCtrl.allPackages.isNotEmpty) ...[
                    // Text(
                    //   "Standard Packages",
                    //   style: GoogleFonts.zcoolXiaoWei(
                    //       fontSize: 25, color: const Color(0xff3E3E3E)),
                    // ),
                    // const SizedBox(height: 15),
                    StaggeredGrid.extent(
                      maxCrossAxisExtent: 350,
                      mainAxisSpacing: 15,
                      crossAxisSpacing: 15,
                      children: [
                        ...List.generate(
                          hCtrl.allPackages.length,
                          (index) {
                            return PackageCard(
                                packageModel: hCtrl.allPackages[index]);
                          },
                        )
                      ],
                    ),
                  ],
                ],
              ),
            );
          }),
        ),
      ],
    );
  }
}

addEditPackages(
  BuildContext context,
  PackageModel? package,
  List<PackageServiceModel> allServices,
) {
  final packageNameCtrl = TextEditingController();
  final packageDescCtrl = TextEditingController();
  final packagePriceCtrl = TextEditingController();
  final packageValidityCtrl = TextEditingController();
  Color pickedColor = Colors.black;
  // bool isStandard = true;
  List<PointsModel> packagePointsInputList = [];
  bool onSubmitLoad = false;
  if (package != null) {
    packageNameCtrl.text = package.packageName;
    packageDescCtrl.text = package.description;
    packagePriceCtrl.text = package.price.toString();
    packagePointsInputList.clear();
    packagePointsInputList.addAll(package.points);
    packageValidityCtrl.text = package.validity.toString();
    pickedColor = package.packageColor.isEmpty
        ? Colors.black
        : hexToColor(package.packageColor);
    // isStandard = package.isStandard;
  }

  showDialog(
    context: context,
    builder: (context) {
      return Dialog(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        child: StatefulBuilder(builder: (context, setState2) {
          return Container(
            constraints: const BoxConstraints(maxWidth: 650, maxHeight: 700),
            padding: const EdgeInsets.all(25),
            decoration: const BoxDecoration(),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    package != null ? 'Edit Package' : "Add Packages",
                    style: GoogleFonts.zcoolXiaoWei(
                        fontSize: 25, color: themeColor),
                  ),
                  const SizedBox(height: 20),

                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: packageNameCtrl,
                          cursorHeight: 20,
                          decoration: inpDecor().copyWith(labelText: 'Name*'),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: TextFormField(
                          controller: packagePriceCtrl,
                          cursorHeight: 20,
                          decoration: inpDecor().copyWith(labelText: 'Price*'),
                        ),
                      ),
                      /*  Expanded(
                        child: Container(
                          clipBehavior: Clip.antiAlias,
                          height: 48,
                          padding: const EdgeInsets.only(left: 3),
                          decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(7),
                              // color: const Color.fromARGB(9, 0, 0, 0),
                              color: Colors.transparent),
                          child: Row(
                            children: [
                              Checkbox(
                                side: const BorderSide(color: Colors.grey),
                                checkColor: Colors.white,
                                activeColor: themeColor,
                                hoverColor: Colors.transparent,
                                // focusColor: Colors.transparent,
                                overlayColor: const WidgetStatePropertyAll(
                                    Colors.transparent),
                                value: isStandard,
                                onChanged: (value) async {
                                  isStandard = value!;
                                  setState2(() {});
                                },
                              ),
                              const SizedBox(width: 5),
                              const Text(
                                "Standard Package",
                                style: TextStyle(color: Colors.black),
                              ),
                            ],
                          ),
                        ),
                      ),
                     */
                    ],
                  ),
                  const SizedBox(height: 18),

                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: packageValidityCtrl,
                          cursorHeight: 20,
                          decoration: inpDecor().copyWith(
                              labelText: 'Validity*', hintText: 'In Days'),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: InkWell(
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () async {
                            showDialog(
                              context: context,
                              builder: (context) {
                                Color currentColor = pickedColor;
                                return AlertDialog(
                                  title: const Text('Pick a color!'),
                                  content: SingleChildScrollView(
                                    child: ColorPicker(
                                      pickerColor: pickedColor,
                                      onColorChanged: (value) {
                                        currentColor = value;
                                      },
                                    ),
                                  ),
                                  actions: <Widget>[
                                    ElevatedButton(
                                      child: const Text('Got it'),
                                      onPressed: () {
                                        setState2(() {
                                          pickedColor = currentColor;
                                        });
                                        Navigator.of(context).pop();
                                      },
                                    ),
                                  ],
                                );
                              },
                            );
                          },
                          child: Container(
                            height: 48,
                            width: double.maxFinite,
                            padding: EdgeInsets.symmetric(horizontal: 10),
                            decoration: BoxDecoration(
                              border: Border.all(
                                  color:
                                      const Color.fromARGB(255, 184, 184, 184)),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: Row(
                              children: [
                                Text("Color"),
                                Spacer(),
                                Container(
                                  height: 38,
                                  width: 38,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: pickedColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(height: 18),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: packageDescCtrl,
                          cursorHeight: 20,
                          minLines: 1,
                          maxLines: 3,
                          decoration:
                              inpDecor().copyWith(labelText: 'Description*'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 18),
                  Row(
                    children: [
                      Text(
                        "Points",
                        style: GoogleFonts.livvic(
                            fontSize: 18,
                            color: const Color.fromARGB(255, 228, 139, 110)),
                      ),
                      const SizedBox(width: 10),
                      InkWell(
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          packagePointsInputList.add(
                            PointsModel(
                              serviceUnit: null,
                              serviceId: null,
                              serviceName: '',
                              serviceQty: 0,
                            ),
                          );
                          setState2(() {});
                        },
                        child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                                shape: BoxShape.circle, color: Colors.white),
                            child: const Icon(
                              CupertinoIcons.add,
                              size: 18,
                              color: Color.fromARGB(255, 115, 115, 115),
                            )),
                      ),
                    ],
                  ),
                  ...[
                    const SizedBox(height: 20),
                    ...List.generate(
                      packagePointsInputList.length,
                      (index) {
                        return Padding(
                          padding: EdgeInsets.only(top: index == 0 ? 0 : 15.0),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: DropdownButtonFormField(
                                  decoration: inpDecor()
                                      .copyWith(labelText: 'Select Service*'),
                                  value:
                                      packagePointsInputList[index].serviceId,
                                  items: [
                                    ...List.generate(
                                      allServices.length,
                                      (index) {
                                        return DropdownMenuItem(
                                          value: allServices[index].id,
                                          child: Text(allServices[index].name),
                                        );
                                      },
                                    )
                                  ],
                                  onChanged: (value) {
                                    if (value != null) {
                                      packagePointsInputList[index].serviceId =
                                          value;
                                      packagePointsInputList[index]
                                          .serviceName = allServices
                                              .firstWhereOrNull((element) =>
                                                  element.id == value)
                                              ?.name ??
                                          '';
                                      setState2(() {});
                                    }
                                  },
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                flex: 1,
                                child: DropdownButtonFormField(
                                  decoration: inpDecor()
                                      .copyWith(labelText: 'Select Type*'),
                                  value:
                                      packagePointsInputList[index].serviceUnit,
                                  items: [
                                    ...List.generate(
                                      packageServiceType.length,
                                      (index) {
                                        return DropdownMenuItem(
                                          value: packageServiceType[index],
                                          child:
                                              Text(packageServiceType[index]),
                                        );
                                      },
                                    )
                                  ],
                                  onChanged: (value) {
                                    if (value != null) {
                                      packagePointsInputList[index]
                                          .serviceUnit = value;

                                      setState2(() {});
                                    }
                                  },
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: TextFormField(
                                  cursorHeight: 20,
                                  decoration: inpDecor()
                                      .copyWith(labelText: 'Service Qty*'),
                                  controller: TextEditingController(
                                      text: packagePointsInputList[index]
                                          .serviceQty
                                          .toString()),
                                  onChanged: (value) {
                                    if (num.tryParse(value) == null) {
                                      showErrorAppSnackBar(
                                          context, 'Error in Quantity');
                                      return;
                                    }
                                    packagePointsInputList[index].serviceQty =
                                        num.tryParse(value) ?? 0;
                                    // setState2(() {});
                                  },
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                  // Spacer(),
                  const SizedBox(height: 25),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: onSubmitLoad
                        ? [
                            const SizedBox(
                              height: 23,
                              width: 23,
                              child: Center(
                                  child: CircularProgressIndicator(
                                strokeWidth: 3.5,
                              )),
                            )
                          ]
                        : [
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: const Text("Cancel"),
                            ),
                            const SizedBox(width: 10),
                            ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: themeColor,
                                elevation: 0,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4)),
                                // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                              ),
                              onPressed: () async {
                                if (onSubmitLoad) return;

                                try {
                                  onSubmitLoad = true;
                                  setState2(() {});
                                  final data = <String, dynamic>{
                                    'packageName': packageNameCtrl.text.trim(),
                                    'price': num.tryParse(
                                        packagePriceCtrl.text.trim()),
                                    'description': packageDescCtrl.text.trim(),
                                    'packageColor': pickedColor.toHexString(),
                                    'validity': int.tryParse(
                                        packageValidityCtrl.text.trim()),
                                    'createdAt': package != null
                                        ? package
                                            .createdAt.millisecondsSinceEpoch
                                        : DateTime.now().millisecondsSinceEpoch,
                                    'updatedAt':
                                        DateTime.now().millisecondsSinceEpoch,
                                    'points': packagePointsInputList
                                        .map((e) => e.toJson())
                                        .toList(),
                                    // 'isStandard': isStandard,
                                    'isActive': package != null
                                        ? package.isActive
                                        : true,
                                  };
                                  final packageRef = package != null
                                      ? FBFireStore.packages.doc(package.docId)
                                      : FBFireStore.packages.doc();
                                  await packageRef.set(data);
                                  if (context.mounted) {
                                    onSubmitLoad = false;
                                    setState2(() {});
                                    Navigator.of(context).pop();
                                  }
                                } catch (e) {
                                  onSubmitLoad = false;
                                  setState2(() {});
                                  debugPrint(e.toString());
                                }
                              },
                              child: Text(
                                "Save",
                                style: GoogleFonts.livvic(
                                    letterSpacing: 1.3,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500),
                              ),
                            ),
                          ],
                  ),
                ],
              ),
            ),
          );
        }),
      );
    },
  );
}
