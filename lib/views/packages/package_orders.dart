import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/views/common/header_search_feild.dart';
import 'package:wedding_super_admin/views/packages/widgets/package_table_header.dart';

import '../../models/package_order_model.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';
import '../users/widgets/pagination_button.dart';
import 'widgets/package_order_tile.dart';

class PackageOrdersPage extends StatefulWidget {
  const PackageOrdersPage({super.key});

  @override
  State<PackageOrdersPage> createState() => _PackageOrdersPageState();
}

class _PackageOrdersPageState extends State<PackageOrdersPage> {
  List<PackageOrderModel> filteredOrderList = [];
  TextEditingController searchController = TextEditingController();
  int totalPages = 0;
  int totalItems = 0;
  String? selPackageType;
  List<List<PackageOrderModel>> paginatedData = [];
  int currentPage = 1;
  int perPageLimit = 1;
  String searchText = '';
  bool isLastPage = false;
  bool dataloaded = false;
  DocumentSnapshot? lastFetchedDoc;
  Timer? debounce;
  List<String> allPackagesName = [];
  @override
  void initState() {
    super.initState();
    allPackagesName
        .addAll(Get.find<HomeCtrl>().allPackages.map((e) => e.packageName));
    initializePagination();
  }

  void resetPagination() {
    paginatedData.clear();
    lastFetchedDoc = null;
    currentPage = 1;
  }

  // NORMAL DATA
  Future<void> initializePagination() async {
    currentPage = 1;
    final userCountSnap = await FBFireStore.packageOrders.count().get();
    totalItems = userCountSnap.count ?? 0;
    totalPages = (totalItems / perPageLimit).ceil();
    await fetchPage(1); // fetch first page (1-based)
    dataloaded = true;
    setState(() {});
  }

  // SEARCHED DATA
  getSearchData(String str) async {
    final packageCountSnap = await FBFireStore.packageOrders
        .where('username', isGreaterThanOrEqualTo: str)
        .where('username', isLessThanOrEqualTo: "$str\uf7ff")
        .count()
        .get();
    totalItems = packageCountSnap.count ?? 0;
    totalPages = (totalItems / perPageLimit).ceil();
    if (debounce?.isActive ?? false) debounce?.cancel();
    debounce = Timer(const Duration(milliseconds: 200), () async {
      await fetchPage(1, forSearch: true);
      setState(() {});
    }); // fetch first page (1-based)
    dataloaded = true;
    setState(() {});
  }

  getPackageTypeData() async {
    final packageCountSnap = await FBFireStore.packageOrders
        .where('packageModel.packageName', isEqualTo: selPackageType)
        .count()
        .get();
    totalItems = packageCountSnap.count ?? 0;
    totalPages = (totalItems / perPageLimit).ceil();
    await fetchPage(1, byPackageType: true);

    dataloaded = true;
    setState(() {});
  }

  Future<void> fetchPage(int pageIndex,
      {bool forSearch = false, bool byPackageType = false}) async {
    int pageZeroIndex = pageIndex - 1;
    final str = searchController.text.toLowerCase().trim();
    if (pageZeroIndex < paginatedData.length &&
        paginatedData[pageZeroIndex].isNotEmpty) {
      return;
    }
    Query query = forSearch
        ? FBFireStore.packageOrders
            .where('username', isGreaterThanOrEqualTo: str)
            .where('username', isLessThanOrEqualTo: "$str\uf7ff")
            .orderBy('createdAt', descending: true)
            .limit(perPageLimit)
        : byPackageType
            ? FBFireStore.packageOrders
                .where('packageModel.packageName',
                    isEqualTo: selPackageType?.trim())
                .orderBy('createdAt', descending: true)
                .limit(perPageLimit)
            : FBFireStore.packageOrders
                .orderBy('createdAt', descending: true)
                .limit(perPageLimit);

    if (lastFetchedDoc != null) {
      query = query.startAfterDocument(lastFetchedDoc!);
    }

    final snap = await query.get();
    if (snap.docs.isNotEmpty) {
      final users =
          snap.docs.map((doc) => PackageOrderModel.forNextSnap(doc)).toList();
      lastFetchedDoc = snap.docs.last;

      while (paginatedData.length <= pageZeroIndex) {
        paginatedData.add([]);
      }
      paginatedData[pageZeroIndex] = users;
    }
  }

  void handlePageChange(int newPage) async {
    dataloaded = false;
    setState(() {});
    await fetchPage(newPage,
        forSearch: searchController.text.trim().isNotEmpty,
        byPackageType: selPackageType != null);
    setState(() {
      dataloaded = true;
      currentPage = newPage;
    });
  }

/*   getFirstPageData() async {
    currentPageIndex = 0;
    isLastPage = false;
    packageOrderList.clear();

    final orderSnap = await FBFireStore.packageOrders
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .get();

    if (orderSnap.docs.isNotEmpty) {
      packageOrderList.add(
          orderSnap.docs.map((e) => PackageOrderModel.fromSnap(e)).toList());
    }

    isLastPage = orderSnap.docs.length < limit;
    setState(() {});
  }

  getNextPage() async {
    currentPageIndex++;
    isLastPage = false;

    final orderSnap = await FBFireStore.packageOrders
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .get();

    if (orderSnap.docs.isNotEmpty) {
      packageOrderList.add(
          orderSnap.docs.map((e) => PackageOrderModel.fromSnap(e)).toList());
    }

    isLastPage = orderSnap.docs.length < limit;
    setState(() {});
  }
 */
  @override
  Widget build(BuildContext context) {
    filteredOrderList = (currentPage - 1) < paginatedData.length
        ? paginatedData[currentPage - 1]
        : [];

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithTrailingAndBack(
            title: "Package Orders",
            showTrailing: false,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: SearchField(
                  searchController: searchController,
                  onChanged: (value) {
                    if (debounce?.isActive ?? false) {
                      debounce?.cancel();
                    }
                    debounce =
                        Timer(const Duration(milliseconds: 500), () async {
                      resetPagination();
                      if (value.trim().isNotEmpty) {
                        await getSearchData(value.toLowerCase().trim());
                      } else {
                        initializePagination();
                      }
                    });
                  },
                ),
              ),
              SizedBox(width: 15),
              Expanded(
                child: DropdownButtonHideUnderline(
                  child: DropdownButtonFormField(
                    decoration: inpDecor().copyWith(
                      border: OutlineInputBorder(
                          borderSide:
                              const BorderSide(color: Color(0xffede2de)),
                          borderRadius: BorderRadius.circular(7)),
                      enabledBorder: OutlineInputBorder(
                          borderSide:
                              const BorderSide(color: Color(0xffede2de)),
                          borderRadius: BorderRadius.circular(7)),
                      focusedBorder: OutlineInputBorder(
                          borderSide:
                              const BorderSide(color: Color(0xffede2de)),
                          borderRadius: BorderRadius.circular(7)),
                      hintText: 'Package Type',
                      hintStyle: TextStyle(
                          color: Color(0xff737373),
                          fontSize: 14.5,
                          fontWeight: FontWeight.w500),
                    ),
                    value: selPackageType,
                    items: [
                      ...List.generate(
                        allPackagesName.length,
                        (index) {
                          return DropdownMenuItem(
                            value: allPackagesName[index],
                            child: Text(
                              allPackagesName[index],
                            ),
                          );
                        },
                      )
                    ],
                    onChanged: (value) {
                      selPackageType = value;
                      resetPagination();
                      getPackageTypeData();
                    },
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 20),
          PackageOrderTableHeader(),
          !dataloaded
              ? Container(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  decoration: const BoxDecoration(
                      border: Border(
                          bottom: BorderSide(color: dividerColor),
                          left: BorderSide(color: dividerColor),
                          right: BorderSide(color: dividerColor)),
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(8),
                          bottomRight: Radius.circular(8))),
                  child: Center(
                    child: Column(
                      children: [
                        SizedBox(height: 15),
                        SizedBox(
                          height: 26,
                          width: 26,
                          child: Center(
                            child: CircularProgressIndicator(strokeWidth: 3),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : filteredOrderList.isEmpty
                  ? Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: const BoxDecoration(
                          border: Border(
                              bottom: BorderSide(color: dividerColor),
                              left: BorderSide(color: dividerColor),
                              right: BorderSide(color: dividerColor)),
                          borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(8))),
                      child: Center(
                          child: Text(
                        'No Data Available',
                        style: TextStyle(
                            color: Color(0xff737373),
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500),
                      )),
                    )
                  : Column(
                      children: [
                        ...List.generate(
                          filteredOrderList.length,
                          (index) {
                            return PackageOrderTile(
                                isLast: index == (filteredOrderList.length - 1),
                                packageOrderModel: filteredOrderList[index],
                                index: index);
                          },
                        )
                      ],
                    ),

          if (totalPages > 1)
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: DynamicPagination(
                currentPage: currentPage,
                totalPages: totalPages,
                onPageChanged: handlePageChange,
              ),
            ),
          // if (paginatedData.length > 1)
          // Padding(
          //   padding: const EdgeInsets.all(12.0),
          //   child: Row(
          //     children: [
          //       if (currentPage > 1)
          //         IconButton(
          //           onPressed: () {
          //             handlePageChange(currentPage - 1);
          //           },
          //           icon: Icon(CupertinoIcons.chevron_back),
          //         ),
          //       Spacer(),
          //       if (isLastPage)
          //         IconButton(
          //           onPressed: () {
          //             handlePageChange(currentPage + 1);
          //           },
          //           icon: Icon(CupertinoIcons.chevron_forward),
          //         ),
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }
}
