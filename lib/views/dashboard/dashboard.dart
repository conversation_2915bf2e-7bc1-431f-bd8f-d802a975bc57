import 'package:flutter/material.dart';

import '../common/page_header.dart';
import '../common/top_sections.dart';

class DashBoardPage extends StatefulWidget {
  const DashBoardPage({super.key});

  @override
  State<DashBoardPage> createState() => _DashBoardPageState();
}

class _DashBoardPageState extends State<DashBoardPage> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
          pageTile: 'Dashboard',
          pagesubTile: 'Welcome to your Wedding Ease Admin Portal',
        ),
        SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              PageHeaderWithButton(
                title: "Dashboard",
                button: false,
                onPressed: () {},
              ),
              const SizedBox(height: 20),
            ])),
      ],
    );
  }
}
