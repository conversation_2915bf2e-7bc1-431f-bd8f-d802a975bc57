// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/product.dart';
import 'package:wedding_super_admin/models/subcategories.dart';
import 'package:wedding_super_admin/models/variants.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/shared/theme.dart';
import 'package:wedding_super_admin/views/Api/data_import.dart';

class ProdCatData {
  final dynamic subcatData;
  final List<dynamic> prodData;
  ProdCatData({required this.subcatData, required this.prodData});
}

class WooCommerce extends StatefulWidget {
  const WooCommerce({super.key, required this.vendor});
  final VendorModel vendor;
  @override
  State<WooCommerce> createState() => _WooCommerceState();
}

class _WooCommerceState extends State<WooCommerce> {
  List<ProdCatData> apiFetchedProductData = [];
  // List<dynamic> apiFetchedCatData = [];
  bool fetchingDataCat = false;
  bool fetchingDataProd = false;
  bool convertingCat = false;
  bool convertingProd = false;
  bool allCatFetched = false;
  num catPageCount = 1;
  bool allCatProdFetched = false;
  num prodPageCount = 1;
  bool allProductsFetched = false;
  bool catConverted = false;
  bool prodConverted = false;
  String? currentStatus;
  int currentCatIndex = 0;
  SubCategory? selectedSubCat;
  bool createNew = true;
  bool inProcess = false;
  bool breakFunct = false;
  TextEditingController perPageCountctrlCat =
      TextEditingController(text: perPageCat);
  TextEditingController perPageCountctrlProd =
      TextEditingController(text: perPageProduct);
  TextEditingController consumerscretctrl = TextEditingController();
  // text: 'cs_c4dfe8b5611e82e29b3e0d1745b9510903e42fa3');
  TextEditingController consumerctrl = TextEditingController();
  // text: 'ck_4f9c97222da3ba2936af8bdf324747ab96087c1b');
  // TextEditingController apiKeyctrl = TextEditingController();
  // text:
  // '********************************************************************************************************************************************************************');
  // TextEditingController prodConsumerscretctrl = TextEditingController();
  // text: 'cs_c4dfe8b5611e82e29b3e0d1745b9510903e42fa3');
  // TextEditingController prodConsumerctrl = TextEditingController();
  // text: 'ck_4f9c97222da3ba2936af8bdf324747ab96087c1b');
  // TextEditingController prodApiKeyctrl = TextEditingController();
  num categoryCompletionCount = 0;
  num productCompletionCount = 0;
  // text:
  // '********************************************************************************************************************************************************************');
  int i = 0;
  int j = 0;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.vendor.apiRequiredData.isNotEmpty) {
      if (widget.vendor.apiRequiredData.containsKey('consumerSecretKey')) {
        consumerscretctrl.text =
            widget.vendor.apiRequiredData['consumerSecretKey'];
      }
      if (widget.vendor.apiRequiredData.containsKey('consumerKey')) {
        consumerctrl.text = widget.vendor.apiRequiredData['consumerKey'];
      }

      if (widget.vendor.apiRequiredData.containsKey('selectedCat')) {
        selectedSubCat = Get.find<HomeCtrl>().subcategories.firstWhereOrNull(
            (element) =>
                element.docId == widget.vendor.apiRequiredData['selectedCat']);
      }
      if (widget.vendor.apiRequiredData.containsKey('newSubCat')) {
        createNew = widget.vendor.apiRequiredData['newSubCat'];
      }
    }
    if (mounted) {
      setState(() {});
    }
  }

  fetcgCategories() async {
    currentStatus = "Fetching Categories";
    setState(() {});

    while (!allCatFetched) {
      if (breakFunct) {
        break;
      }
      await woocommerceGetCat(
          consumerctrl.text, consumerscretctrl.text, perPageCountctrlCat.text);

      // getData(selectedVendor ?? "");
      catPageCount++;
      if (catPageCount >= catpageLimit) {
        break;
      }
    }

    setState(() {});
  }

  fetchProducts() async {
    currentStatus = "Fetching Products";
    setState(() {});

    while (!allProductsFetched) {
      if (breakFunct) {
        break;
      }
      await woocommerceGetProd(
          consumerctrl.text,
          consumerscretctrl.text,
          perPageCountctrlProd.text,
          (apiFetchedProductData[currentCatIndex].subcatData as Map)['id']
              .toString());
      prodPageCount++;
      if (prodPageCount >= productPageLimit) {
        break;
      }
    }

    setState(() {});
  }

  convertCategories() async {
    currentStatus = "Converting Categories";
    setState(() {});

    await convertWoocommerceCat().timeout(Duration(minutes: 5));
  }

  convertProducts() async {
    currentStatus = "Converting Products";
    setState(() {});

    await convertWoocommerceProducts().timeout(Duration(hours: 1));
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        SizedBox(
          height: 30,
        ),
        Divider(),
        SizedBox(
          height: 30,
        ),
        Row(
          children: [
            Text("Details",
                style: GoogleFonts.zcoolXiaoWei(
                  fontSize: 24,
                  color: themeColor,
                )),
            SizedBox(
              height: 20,
            ),
            Spacer(),
            Text(currentStatus == null ? "" : "${currentStatus}..."),
            SizedBox(
              width: 10,
            ),
            Row(
              children: [
                ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5))),
                    onPressed: inProcess
                        ? () {}
                        : () async {
                            if (consumerctrl.text.isEmpty &&
                                consumerscretctrl.text.isEmpty &&
                                perPageCountctrlCat.text.isEmpty &&
                                perPageCountctrlProd.text.isEmpty) {
                              showAppSnackBar(context,
                                  "Please enter all the required data!!");
                              return;
                            }
                            inProcess = true;
                            setState(() {});
                            try {
                              await fetcgCategories();
                              await convertCategories();
                              await fetchProducts();
                              await convertProducts();
                            } catch (e) {
                              // TODO
                              debugPrint(e.toString());
                              currentStatus = "Error ${e.toString()}!!";
                            }
                            inProcess = false;
                            setState(() {});
                          },
                    child: inProcess
                        ? CircularProgressIndicator()
                        : Text("Initiate Process")),
                if (inProcess)
                  IconButton(
                      onPressed: () {
                        breakFunct = true;
                      },
                      icon: Icon(
                        Icons.block,
                        color: Colors.red,
                      ))
              ],
            )
          ],
        ),
        SizedBox(
          height: 30,
        ),
        StaggeredGrid.extent(
          maxCrossAxisExtent: 600,
          crossAxisSpacing: 20,
          mainAxisSpacing: 20,
          children: [
            TextFormField(
              onChanged: (value) async {
                await FBFireStore.vendors
                    .doc(widget.vendor.docId)
                    .update({'apiRequiredData.consumerKey': value});
              },
              controller: consumerctrl,
              decoration: inpDecor().copyWith(labelText: 'Enter consumer key'),
            ),
            TextFormField(
              onChanged: (value) async {
                await FBFireStore.vendors
                    .doc(widget.vendor.docId)
                    .update({'apiRequiredData.consumerSecretKey': value});
              },
              controller: consumerscretctrl,
              decoration:
                  inpDecor().copyWith(labelText: 'Enter consumer secret key'),
            ),
            // TextFormField(
            //   controller: apiKeyctrl,
            //   onChanged: (value) async {
            //     await FBFireStore.vendors
            //         .doc(widget.vendor.docId)
            //         .update({'apiRequiredData.apiKey': value});
            //   },
            //   decoration: inpDecor().copyWith(labelText: 'Enter api key'),
            // ),
          ],
        ),
        SizedBox(
          height: 30,
        ),
        Divider(),
        SizedBox(
          height: 30,
        ),
        Text("Default Category",
            style: GoogleFonts.zcoolXiaoWei(
              fontSize: 24,
              color: themeColor,
            )),
        SizedBox(
          height: 20,
        ),
        Container(
            constraints: BoxConstraints(maxWidth: 500),
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField(
                    value: selectedSubCat,
                    hint: Text("Select Default Sub-Category"),
                    decoration: InputDecoration(border: OutlineInputBorder()),
                    items: List.generate(
                      ctrl.subcategories.length,
                      (index) {
                        return DropdownMenuItem(
                            value: ctrl.subcategories[index],
                            child: Text(ctrl.subcategories[index].name));
                      },
                    ),
                    onChanged: (value) async {
                      selectedSubCat = value;
                      await FBFireStore.vendors
                          .doc(widget.vendor.docId)
                          .update({
                        'apiRequiredData.selectedCat': selectedSubCat?.docId
                      });
                      setState(() {});
                    },
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                IconButton(
                    onPressed: () async {
                      await FBFireStore.vendors
                          .doc(widget.vendor.docId)
                          .update({
                        'apiRequiredData.selectedCat': null,
                        'apiRequiredData.newSubCat': true
                      });

                      setState(() {
                        selectedSubCat = null;
                        createNew = true;
                      });
                    },
                    icon: Icon(CupertinoIcons.xmark))
              ],
            )),
        if (selectedSubCat != null) ...[
          SizedBox(
            height: 20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Checkbox(
                value: createNew,
                onChanged: (value) async {
                  await FBFireStore.vendors
                      .doc(widget.vendor.docId)
                      .update({'apiRequiredData.newSubCat': value});
                  createNew = value ?? false;
                  setState(() {});
                },
              ),
              // SizedBox(
              //   width: 5,
              // ),
              Text("Create New Sub Cat ")
            ],
          ),
          SizedBox(
            height: 20,
          )
        ],
        SizedBox(
          height: 30,
        ),
        Divider(),
        SizedBox(
          height: 30,
        ),
        Row(
          children: [
            Text("Category",
                style: GoogleFonts.zcoolXiaoWei(
                  fontSize: 24,
                  color: themeColor,
                )),
            SizedBox(
              width: 15,
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5))),
              onPressed: fetchingDataCat || allCatFetched
                  ? () {}
                  : true
                      ? () {}
                      : fetcgCategories,
              child: fetchingDataCat
                  ? SizedBox(
                      height: 40,
                      width: 40,
                      child: Padding(
                        padding: const EdgeInsets.all(5.0),
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                        ),
                      ),
                    )
                  : Text(allCatFetched
                      ? "Data Fetched"
                      : catPageCount != 1
                          ? "Fetching next ${perPageCountctrlCat.text} Categories"
                          : 'Fetch Categories'),
            ),
          ],
        ),
        SizedBox(height: 35),
        StaggeredGrid.extent(
          maxCrossAxisExtent: 600,
          crossAxisSpacing: 20,
          mainAxisSpacing: 20,
          children: [
            TextFormField(
              controller: perPageCountctrlCat,
              decoration: inpDecor().copyWith(labelText: 'Per Page Limit'),
            ),
          ],
        ),

        // if (apiFetchedProductData.map((e) => e.subcatData).isNotEmpty) ...[
        if (apiFetchedProductData.map((e) => e.subcatData).isNotEmpty) ...[
          if (selectedSubCat == null || createNew) ...[
            SizedBox(
              height: 30,
            ),
            Divider(),
            SizedBox(
              height: 30,
            ),
            Row(
              children: [
                Text("Convert Category",
                    style: GoogleFonts.zcoolXiaoWei(
                      fontSize: 24,
                      color: themeColor,
                    )),
                SizedBox(
                  width: 10,
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5))),
                  onPressed: convertingCat ||
                          (categoryCompletionCount ==
                              (apiFetchedProductData
                                  .map((e) => e.subcatData)
                                  .length))
                      ? () {}
                      : true
                          ? () {}
                          : convertCategories,
                  child: (categoryCompletionCount ==
                          (apiFetchedProductData
                              .map((e) => e.subcatData)
                              .length))
                      ? Text(
                          "${apiFetchedProductData.map((e) => e.subcatData).length} - Converted")
                      : convertingCat
                          ? Padding(
                              padding: const EdgeInsets.all(5.0),
                              child: Row(
                                children: [
                                  Text(
                                      "$categoryCompletionCount/${apiFetchedProductData.map((e) => e.subcatData).length}"),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  CircularProgressIndicator(),
                                ],
                              ),
                            )
                          : Text(
                              catConverted ? "Converted" : 'Convert Category'),
                ),
              ],
            ),
          ],
          // SizedBox(
          //   height: 25,
          // ),
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.start,
          //   children: [
          //     ConstrainedBox(
          //       constraints: BoxConstraints(maxWidth: 600),
          //       child: TextFormField(
          //         controller: catApiKeyctrl,
          //         decoration:
          //             inpDecor().copyWith(labelText: 'Enter api key'),
          //       ),
          //     ),
          //   ],
          // ),

          SizedBox(
            height: 30,
          ),

          // Tex
          Text(
              "Result: ${apiFetchedProductData.map((e) => e.subcatData).length} Categories Found!!",
              style: GoogleFonts.zcoolXiaoWei(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: themeColor,
              )),
        ],

        SizedBox(
          height: 30,
        ),
        Divider(),
        SizedBox(
          height: 30,
        ),
        Row(
          children: [
            Text("Products",
                style: GoogleFonts.zcoolXiaoWei(
                  fontSize: 24,
                  color: themeColor,
                )),
            SizedBox(
              width: 10,
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5))),
              onPressed: fetchingDataProd || allProductsFetched
                  ? () {}
                  : true
                      ? () {}
                      : fetchProducts,
              child: fetchingDataProd
                  ? Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: CircularProgressIndicator(),
                    )
                  : Text(allProductsFetched
                      ? "Data Fetched"
                      : allCatProdFetched
                          ? "Fetch next category products"
                          : prodPageCount != 1
                              ? "Fetch next ${perPageCountctrlProd.text} Products"
                              : 'Fetch Products'),
            ),
          ],
        ),
        SizedBox(
          height: 25,
        ),
        StaggeredGrid.extent(
          maxCrossAxisExtent: 600,
          mainAxisSpacing: 20,
          crossAxisSpacing: 20,
          children: [
            TextFormField(
              controller: perPageCountctrlProd,
              decoration: InputDecoration(
                  labelText: "Per Page Limit",
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5))),
            ),
          ],
        ),

        // SizedBox(
        //   height: 30,
        // ),

        // Tex

        if (apiFetchedProductData
            .any((subList) => subList.prodData.isNotEmpty)) ...[
          SizedBox(
            height: 30,
          ),
          Divider(),
          SizedBox(
            height: 30,
          ),
          Row(
            children: [
              Text("Convert Products",
                  style: GoogleFonts.zcoolXiaoWei(
                    fontSize: 24,
                    color: themeColor,
                  )),
              SizedBox(
                width: 10,
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5))),
                onPressed: convertingProd ||
                        (productCompletionCount ==
                            apiFetchedProductData
                                .map((e) => e.prodData)
                                .map((e) => e.length)
                                .reduce((value, element) => value + element))
                    ? () {}
                    : true
                        ? () {}
                        : convertProducts,
                child: (productCompletionCount ==
                        apiFetchedProductData
                            .map((e) => e.prodData)
                            .map((e) => e.length)
                            .reduce((value, element) => value + element))
                    ? Text(
                        "${apiFetchedProductData.map((e) => e.prodData).map((e) => e.length).reduce((value, element) => value + element)} - Converted")
                    : convertingProd
                        ? Padding(
                            padding: const EdgeInsets.all(5.0),
                            child: Row(
                              children: [
                                Text(
                                    "$productCompletionCount/${apiFetchedProductData.map((e) => e.prodData).map((e) => e.length).reduce((value, element) => value + element)}"),
                                SizedBox(
                                  width: 10,
                                ),
                                CircularProgressIndicator(),
                              ],
                            ),
                          )
                        : Text(
                            prodConverted ? "Converted" : 'Convert Products'),
              ),
            ],
          ),

          SizedBox(
            height: 30,
          ),
          // Tex
          Text(
              "Result: ${apiFetchedProductData.map((e) => e.prodData).map((e) => e.length).reduce((value, element) => value + element)} Products Found!!",
              style: GoogleFonts.zcoolXiaoWei(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: themeColor,
              )),
        ]
      ]
          // ],
          );
    });
  }

  Future convertWoocommerceCat() async {
    convertingCat = true;
    setState(() {});

    try {
      for (var item in apiFetchedProductData) {
        // i++;
        // if (i == 3) {
        //   break;
        // }
        if (breakFunct) {
          break;
        }
        final data = await convertVendorCategoryToCategory(
                [(item.subcatData as Map<String, dynamic>)],
                // apiFetchedProductData
                //     .map((e) => e.subcatData as Map<String, dynamic>)
                //     .toList(),
                gptApipiKey)
            .timeout(Duration(minutes: 5));

        for (var category in data) {
          final categoryData = {
            'name': category['name'],
            'image': category['image'],
            'isActive': category['isActive'],
            'combinationNames': category['combinationNames'],
            'offeringId': '',
            'minPrice': category['minPrice'],
            'maxPrice': category['maxPrice'],
            'tags': category['tags'],
            'detailTypes': [],
            'marginPercentage': category['marginPercentage'],
            'allData': {}
          };
          categoryData['combinationNames'] =
              generateCombinations(categoryData['name']);
          print(categoryData);
          final productData = await getSingleProduct(
              (item.subcatData as Map)['id'].toString(),
              consumerctrl.text,
              consumerscretctrl.text);
          final offeringId = await matchedDatawithOffering(
                  Get.find<HomeCtrl>()
                      .subcategories
                      .map((e) => {'name': e.name, 'offeringId': e.offeringId})
                      .toList(),
                  Get.find<HomeCtrl>()
                      .offerings
                      .map((e) => {'name': e.name, 'offeringId': e.docId})
                      .toList(),
                  {
                    'categoryName': (category['name']),
                    'productName': productData[0],
                    "ProductDescription": productData[1]
                  },
                  gptApipiKey)
              .timeout(Duration(minutes: 5));
          categoryData['offeringId'] = offeringId.first['offeringId'];
          String? catId = Get.find<HomeCtrl>()
              .vendors
              .firstWhereOrNull(
                  (element) => element.docId == widget.vendor.docId)
              ?.apiSubCatIds
              .firstWhereOrNull((e) =>
                  e.subCatApiId == (item.subcatData as Map)['id'].toString())
              ?.subCatdocId;

          // String? catId = temp.docs.isNotEmpty ? temp.docs.first.id : null;

          final catData =
              catId ?? (await FBFireStore.subCategories.add(categoryData)).id;
          // final catData = await FBFireStore.subCategories.add(categoryData);
          if (catId == null) {
            List<SubCatMap>? subcatMapList = Get.find<HomeCtrl>()
                .vendors
                .firstWhereOrNull(
                    (element) => element.docId == widget.vendor.docId)
                ?.apiSubCatIds;
            List<String>? subcatids = Get.find<HomeCtrl>()
                .vendors
                .firstWhereOrNull(
                    (element) => element.docId == widget.vendor.docId)
                ?.subcatids;

            subcatMapList?.add(SubCatMap(
                subCatApiId: apiFetchedProductData
                        .firstWhereOrNull((element) =>
                            element.subcatData['name'] == category['name'])
                        ?.subcatData['id']
                        .toString() ??
                    "",
                subCatdocId: catData));
            subcatids?.addIf(!(subcatids.contains(catData)), catData);
            print(subcatMapList?.map((e) => e.toJson()));
            await FBFireStore.vendors.doc(widget.vendor.docId).update({
              'apiSubCatIds': subcatMapList?.map((e) => e.toJson()).toList(),
              'subcatids': subcatids
            });
          }
        }
        categoryCompletionCount++;
        setState(() {});
      }
      catConverted = true;
    } catch (e) {
      showAppSnackBar(context, e.toString());
      debugPrint(e.toString());
    }

    convertingCat = false;
    setState(() {});
  }

  Future convertWoocommerceProducts() async {
    convertingProd = true;
    setState(() {});
    print("Eneterd");
    try {
      for (var subCatProd in apiFetchedProductData) {
        // j++;
        // if (j == 3) {
        //   break;
        // }
        if (breakFunct) {
          break;
        }
        String catId = "";
        for (var prod in subCatProd.prodData) {
          if (breakFunct) {
            break;
          }
          // List<Map<String, dynamic>> data =
          //     await convertVendorToProductswithRetry(
          //         [prod as Map<String, dynamic>], gptApipiKey);

          final data = await convertVendorToProducts(
                  [prod as Map<String, dynamic>], gptApipiKey)
              .timeout(Duration(hours: 1));
          print("fetched Data --------------------------------------");
          List<String> combNames = [];
          for (var element in (data.first['combinationNames'] as List)) {
            combNames.addAll(generateCombinations(element.toString()));
          }

          Map<String, dynamic> productData = {
            'priorityNo': data.first['priorityNo'],
            'rating': data.first['rating'],
            'name': data.first['name'],
            'lowerName': data.first['lowerName'],
            'combinationNames': combNames,
            'mainCatDocId': null,
            'subCatDocId': null,
            'subcatIds': <String>[],
            'vendorDocId': widget.vendor.docId,
            'show': data.first['show'],
            'sku': data.first['sku'],
            'description': data.first['description'],
            'userType': 'Vendor',
            'defaultImage': data.first['defaultImage'],
            'createdAt': Timestamp.now(),
            'topSelling': data.first['topSelling'],
            'minPrice': data.first['minPrice'],
            'maxPrice': data.first['maxPrice'],
            'quantitySold': data.first['quantitySold'],
            'totalSalesAmount': data.first['totalSalesAmount'],
            'tags': data.first['tags'],
            'detailsList': data.first['detailsList'] ?? <String>[],
            // 'docId': '',
          };
          print("------1");

          if (selectedSubCat == null) {
            print('selectedSubCat is null, processing subcatIds from data...');
            for (var apiId in data.first['subcatIds']) {
              print('Processing apiId: $apiId');
              String? subCatDocId = Get.find<HomeCtrl>()
                  .vendors
                  .firstWhereOrNull(
                      (element) => element.docId == widget.vendor.docId)
                  ?.apiSubCatIds
                  .firstWhereOrNull((el) => el.subCatApiId == apiId.toString())
                  ?.subCatdocId;

              print('Mapped apiId $apiId to subCatDocId: $subCatDocId');

              if (subCatDocId != null) {
                final subCat = Get.find<HomeCtrl>()
                    .subcategories
                    .firstWhereOrNull(
                        (element) => element.docId == subCatDocId);

                print('Fetched subCat for docId $subCatDocId: $subCat');

                if (subCat != null) {
                  (productData['subcatIds'] as List<String>).add(subCat.docId);
                  print(
                      'Added subCat.docId to productData[subcatIds]: ${subCat.docId}');
                  print(
                      'Current productData[subcatIds]: ${productData['subcatIds']}');

                  if (productData['subCatDocId'] == null &&
                      subCat.name == subCatProd.subcatData['name']) {
                    catId = subCat.docId;
                    productData['subCatDocId'] = subCat.docId;
                    productData['mainCatDocId'] = subCat.offeringId;
                    print('Set productData[subCatDocId]: ${subCat.docId}');
                    print(
                        'Set productData[mainCatDocId]: ${subCat.offeringId}');
                  }
                }
              }
            }
          } else {
            print('selectedSubCat is not null: $selectedSubCat');
            (productData['subcatIds'] as List<String>)
                .add(selectedSubCat?.docId ?? "");
            print(
                'Added selectedSubCat.docId to productData[subcatIds]: ${selectedSubCat?.docId}');
            print(
                'Current productData[subcatIds]: ${productData['subcatIds']}');

            if (productData['subCatDocId'] == null) {
              productData['subCatDocId'] = selectedSubCat?.docId ?? "";
              productData['mainCatDocId'] = selectedSubCat?.offeringId ?? "";
              print('Set productData[subCatDocId]: ${selectedSubCat?.docId}');
              print(
                  'Set productData[mainCatDocId]: ${selectedSubCat?.offeringId}');
            }
          }
          print('Final productData: $productData');

          if (selectedSubCat != null && createNew) {
            for (var apiId in data.first['subcatIds']) {
              String? subCatDocId = Get.find<HomeCtrl>()
                  .vendors
                  .firstWhereOrNull(
                      (element) => element.docId == widget.vendor.docId)
                  ?.apiSubCatIds
                  .firstWhereOrNull((el) => el.subCatApiId == apiId.toString())
                  ?.subCatdocId;
              if (subCatDocId != null) {
                final subCat = Get.find<HomeCtrl>()
                    .subcategories
                    .firstWhereOrNull(
                        (element) => element.docId == subCatDocId);
                if (subCat != null) {
                  (productData['subcatIds'] as List<String>).add(subCat.docId);
                }
              }
            }
          }
          print("---${productData}");

          final product = ProductModel.fromJson(productData);

          // print("------${product.toJson()}");
          final fbData = await FBFireStore.products
              .where('sku', isEqualTo: product.sku)
              .get();

          String? existingId;
          if (fbData.docs.isNotEmpty) {
            existingId = fbData.docs.first.id;
          }
          if (existingId != null) {
            await FBFireStore.products.doc(existingId).update(productData);
            List<NewVariantModel> existingVariants = [];
            final variantData = await FBFireStore.variants
                .where('productId', isEqualTo: existingId)
                .get();
            existingVariants = variantData.docs
                .map((e) => NewVariantModel.fromDocSnap(e))
                .toList();
            Map<String, List<String>> allDataProduct = {};
            for (var element in (data.first['variants'] as List)) {
              String? vardocId = existingVariants
                  .firstWhereOrNull((e) => e.aqpiId == element['id'])
                  ?.docId;
              if (vardocId == null) {
                await FBFireStore.variants.add({
                  'id': getRandomId(6),
                  // 'newImages': [],
                  'images': element['images'],
                  'description': element['description'],
                  'defaultt': element['defaultt'],
                  'show': element['show'],
                  'lowerName': data.first['lowerName'],
                  'fixedprice': element['fixedprice'],
                  'priceType': element['priceType'],
                  'priceRange': element['priceRange'],
                  'detailTypes': element['detailTypes'],
                  'productId': existingId,
                  'subCatId': product.defaultSubCatDocId,
                  'mainCatId': product.mainCatDocId,
                  'apiId': element['id'],
                });
              } else {
                await FBFireStore.variants.doc(vardocId).update({
                  // 'id': getRandomId(6),
                  // 'newImages': [],
                  'images': element['images'],
                  'description': element['description'],
                  'defaultt': element['defaultt'],
                  'show': element['show'],
                  'lowerName': data.first['lowerName'],
                  'fixedprice': element['fixedprice'],
                  'priceType': element['priceType'],
                  'priceRange': element['priceRange'],
                  'detailTypes': element['detailTypes'],
                  'productId': existingId,
                  'subCatId': product.defaultSubCatDocId,
                  'mainCatId': product.mainCatDocId,
                  // 'apiId': element['id'],
                });
              }
              (element['detailTypes'] as Map).forEach((key, value) {
                print('Processing key: $key, value: $value');

                if (allDataProduct.containsKey(key as String)) {
                  List listData2 = allDataProduct[key] ?? [];
                  print('Existing listData2 for key "$key": $listData2');

                  final listData =
                      listData2.map((e) => e.toString()).toSet().toList();
                  print('Mapped listData (toString): $listData');

                  listData.addAll([value as String]);
                  print('After adding element[\'detailTypes\']: $listData');

                  // Note: toSet() and toList() are not assigned back, so ineffective here.
                  // Assign back to remove duplicates:
                  final uniqueList = listData.toSet().toList();
                  print('Unique listData after toSet and toList: $uniqueList');

                  allDataProduct[key] = uniqueList;
                  print('Updated allDataProduct[$key]: ${allDataProduct[key]}');
                } else {
                  print(
                      'Key "$key" not found in allDataProduct. Adding new entry.');
                  allDataProduct.addEntries({
                    (key.toString()): [value as String]
                    // (value as List).map((e) => e.toString()).toList()
                  }.entries);
                  print(
                      'allDataProduct after adding new entry: $allDataProduct');
                }
              });

              final subCatData = await FBFireStore.subCategories
                  .doc(product.defaultSubCatDocId)
                  .get();
              print('Fetched subCategory data: ${subCatData.data()}');

              final subCat = SubCategory.fromJson(subCatData.data() ?? {});
              print('Parsed subCategory: $subCat');

              if (subCat.allData.isEmpty) {
                print('subCat.allData is empty. Adding allDataProduct.');
                subCat.allData.addAll(allDataProduct);
                print('subCat.allData after adding: ${subCat.allData}');
              } else {
                print('subCat.allData is not empty. Merging data.');
                subCat.allData.forEach((key, value) {
                  print('Processing subCat.allData key: $key, value: $value');

                  if (allDataProduct.containsKey(key)) {
                    List listData2 = allDataProduct[key] ?? [];
                    print('Existing allDataProduct[$key]: $listData2');

                    final listData =
                        listData2.map((e) => e.toString()).toSet().toList();
                    print('Mapped listData (toString): $listData');
                    print(value);
                    listData.addAll(value);
                    print('After adding subCat.allData value: $listData');

                    // Remove duplicates by assigning back
                    final uniqueList = listData.toSet().toList();
                    print(
                        'Unique listData after toSet and toList: $uniqueList');

                    allDataProduct[key] = uniqueList;
                    print(
                        'Updated allDataProduct[$key]: ${allDataProduct[key]}');
                  } else {
                    print(
                        'Key "$key" not found in allDataProduct. Adding new entry.');
                    allDataProduct.addEntries({
                      (key.toString()):
                          (value as List).map((e) => e.toString()).toList()
                    }.entries);
                    print(
                        'allDataProduct after adding new entry: $allDataProduct');
                  }
                });
              }
            }
            await FBFireStore.subCategories
                .doc(product.defaultSubCatDocId)
                .update({'allData': allDataProduct});
          } else {
            final id = await FBFireStore.products.add(productData);
            Map<String, List<String>> allDataProduct = {};
            for (var element in (data.first['variants'] as List)) {
              await FBFireStore.variants.add({
                'id': getRandomId(6),
                // 'newImages': [],
                'images': element['images'],
                'description': element['description'],
                'defaultt': element['defaultt'],
                'show': element['show'],
                'lowerName': data.first['lowerName'],
                'fixedprice': element['fixedprice'],
                'priceType': element['priceType'],
                'priceRange': element['priceRange'],
                'detailTypes': element['detailTypes'],
                'productId': id.id,
                'subCatId': product.defaultSubCatDocId,
                'mainCatId': product.mainCatDocId
              });
              (element['detailTypes'] as Map).forEach((key, value) {
                if (allDataProduct.containsKey(key as String)) {
                  List listData2 = allDataProduct[key] ?? [];
                  final listData =
                      listData2.map((e) => e.toString()).toSet().toList();
                  listData.addAll([value as String]);
                  // Remove duplicates
                  final uniqueList = listData.toSet().toList();
                  allDataProduct[key] = uniqueList;
                  print('Updated allDataProduct[$key]: $uniqueList');
                } else {
                  allDataProduct.addEntries({
                    (key.toString()): [(value as String)]
                  }.entries);
                  print('Added new key to allDataProduct: $key -> ${[
                    value as String
                  ]}');
                }
              });

              print(
                  'allDataProduct after detailTypes processing: $allDataProduct');

              final subCatData = await FBFireStore.subCategories
                  .doc(product.defaultSubCatDocId)
                  .get();
              final subCat = SubCategory.fromJson(subCatData.data() ?? {});

              print('Loaded subCat.allData: ${subCat.allData}');

              if (subCat.allData.isEmpty) {
                subCat.allData.addAll(allDataProduct);
                print(
                    'subCat.allData was empty, added allDataProduct: ${subCat.allData}');
              } else {
                subCat.allData.forEach(
                  (key, value) {
                    if (allDataProduct.containsKey(key)) {
                      List listData2 = allDataProduct[key] ?? [];
                      final listData =
                          listData2.map((e) => e.toString()).toSet().toList();
                      listData.addAll(value);
                      // Remove duplicates
                      final uniqueList = listData.toSet().toList();
                      allDataProduct[key] = uniqueList;
                      print('Merged existing key $key: $uniqueList');
                    } else {
                      allDataProduct.addEntries({
                        (key.toString()):
                            value.map((e) => e.toString()).toSet().toList()
                      }.entries);
                      print(
                          'Added missing key from subCat.allData: $key -> ${value.map((e) => e.toString()).toList()}');
                    }
                  },
                );
              }

              print('Final allDataProduct: $allDataProduct');
            }
            await FBFireStore.subCategories
                .doc(product.defaultSubCatDocId)
                .update({'allData': allDataProduct});
          }

          print(productData);

          productCompletionCount++;
          setState(() {});
        }
        String? subCatDocId = Get.find<HomeCtrl>()
            .vendors
            .firstWhereOrNull((element) => element.docId == widget.vendor.docId)
            ?.apiSubCatIds
            .firstWhereOrNull((el) => el.subCatApiId == subCatProd.toString())
            ?.subCatdocId;

        final maxPriceProdData = await FBFireStore.products
            .where('defaultSubCatDocId', isEqualTo: subCatDocId)
            .orderBy('maxPrice', descending: true)
            .limit(1)
            .get();

        final minPriceProdData = await FBFireStore.products
            .where('defaultSubCatDocId', isEqualTo: subCatDocId)
            .orderBy('minPrice', descending: false)
            .limit(1)
            .get();

        final maxPriceProd =
            ProductModel.fromDocSnap(maxPriceProdData.docs.first);

        final minPriceProd =
            ProductModel.fromDocSnap(minPriceProdData.docs.first);

        if (subCatDocId != null) {
          await FBFireStore.subCategories.doc(subCatDocId).update({
            'maxPrice': maxPriceProd.maxPrice,
            "minPrice": minPriceProd.minPrice
          });
        }
      } //   bre9
      prodConverted = true;
    } catch (e) {
      showAppSnackBar(context, "---${e.toString()}");
      debugPrint(e.toString());
    }
    convertingProd = false;
    setState(() {});
  }

  woocommerceGetProd(String consumerKey, String consumerSecretKey,
      String perPageCount, String catId) async {
    fetchingDataProd = true;
    setState(() {});
    // String pageNum = '1';
    print("-------${catId}");
    List<Map> data = [];
    try {
      // do {
      var headers = {
        'Cookie':
            'PHPSESSID=f5676c3eb3491ec53b785f4b9d786080; wfwaf-authcookie-ac650b89681ecea921a2d14a6fe8b501=1%7Cadministrator%7Cmanage_options%2Cunfiltered_html%2Cedit_others_posts%2Cupload_files%2Cpublish_posts%2Cedit_posts%2Cread%7C6686fa9be733eb2d2b39fb8fab22003f98488fec54a92ac3150c49d1f2efd052'
      };
      var request = http.Request(
          'GET',
          Uri.parse(
              'https://www.thehub.in/wp-json/wc/v3/products?page=${prodPageCount.toString()}&per_page=$perPageCount&category=$catId&consumer_key=$consumerKey&consumer_secret=$consumerSecretKey&oauth_consumer_key=ck_4f9c97222da3ba2936af8bdf324747ab96087c1b&oauth_signature_method=HMAC-SHA1&oauth_timestamp=1745988759&oauth_nonce=LwETC5sKZ4S&oauth_version=1.0&oauth_signature=IhILwe7y4WUOBUhnFp2tPEUf%2F2E%3D'));
      // var headers = {
      //   'Cookie':
      //       'PHPSESSID=f2065a28936fd34c98edc85f3fb03b9a; wfwaf-authcookie-ac650b89681ecea921a2d14a6fe8b501=1%7Cadministrator%7Cmanage_options%2Cunfiltered_html%2Cedit_others_posts%2Cupload_files%2Cpublish_posts%2Cedit_posts%2Cread%7C61d3f16e5667ae3f84caa23446c2b97d4a8e65b0bc6d5f283a6a9fe5d9f8a6e9'
      // };
      // var request = http.Request(
      //     'GET',
      //     Uri.parse(
      //         'https://www.thehub.in/wp-json/wc/v3/products?consumer_key=$consumerKey&consumer_secret=$consumerSecretKey&oauth_consumer_key=ck_4f9c97222da3ba2936af8bdf324747ab96087c1b&oauth_signature_method=HMAC-SHA1&oauth_timestamp=1745988759&oauth_nonce=LwETC5sKZ4S&oauth_version=1.0&oauth_signature=IhILwe7y4WUOBUhnFp2tPEUf%2F2E%3D'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      print("prod----${response.statusCode}");
      print("prod----${response.reasonPhrase}");

      if (response.statusCode == 200) {
        String apiData = (await response.stream.bytesToString());
        List<dynamic> temp = await json.decode(apiData);
        for (var element in temp) {
          if (element.containsKey('yoast_head_json') &&
              element['yoast_head_json'] is Map) {
            // (element['yoast_head_json'] as Map).remove('robots');
            (element['yoast_head_json'] as Map).remove('schema');
          }
          List<String> imagesData = [];
          for (var img in element['images']) {
            imagesData.add(img['src']);
          }
          element['images'] = imagesData;
          element.remove('meta_data');
          element.remove('_links');
          element.remove('yoast_head');
          data.add(element);
        }
        apiFetchedProductData
            .firstWhereOrNull(
                (element) => element.subcatData['id'].toString() == catId)
            ?.prodData
            .addAll(data);
        print(data.length);
        // print(apiData);
        // for (var element in apiFetchedProductData) {
        //   print(element.prodData.length);
        // }
        // print(apiFetchedProductData);
        // pageNum = ((int.tryParse(pageNum) ?? 0) + 1).toString();
      } else {
        // break;
      }

      // print("${data.length}---${(int.tryParse(perPageCount) ?? 0)}");
      if (data.length != (int.tryParse(perPageCount) ?? 0)) {
        allCatProdFetched = true;
        if (currentCatIndex + 1 == apiFetchedProductData.length) {
          allProductsFetched = true;
        } else {
          currentCatIndex++;
        }
      } else {
        allCatProdFetched = false;
      }
      // } while (data.length == (int.tryParse(perPageCount) ?? 0));
    } catch (e) {
      print(e.toString());
    }
    fetchingDataProd = false;
    setState(() {});
  }

  woocommerceGetCat(
      String consumerKey, String consumerSecretKey, String perPageCount) async {
    fetchingDataCat = true;
    setState(() {});
    // String pageNumber = '1';

    List<dynamic> data = [];
    try {
      // do {
      var headers = {
        'Cookie':
            'PHPSESSID=f5676c3eb3491ec53b785f4b9d786080; wfwaf-authcookie-ac650b89681ecea921a2d14a6fe8b501=1%7Cadministrator%7Cmanage_options%2Cunfiltered_html%2Cedit_others_posts%2Cupload_files%2Cpublish_posts%2Cedit_posts%2Cread%7C6686fa9be733eb2d2b39fb8fab22003f98488fec54a92ac3150c49d1f2efd052'
      };
      var request = http.Request(
          'GET',
          Uri.parse(
              'https://www.thehub.in/wp-json/wc/v3/products/categories?consumer_key=$consumerKey&consumer_secret=$consumerSecretKey&page=${catPageCount.toString()}&per_page=$perPageCount&oauth_consumer_key=ck_4f9c97222da3ba2936af8bdf324747ab96087c1b&oauth_signature_method=HMAC-SHA1&oauth_timestamp=1746016533&oauth_nonce=7Lb4Tb0OlTg&oauth_version=1.0&oauth_signature=6%2FR%2Bzl1Sisk1T6u6PKfxqAsc%2BCM%3D'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();
      print("Cat----${response.statusCode}");
      print("Cat----${response.reasonPhrase}");

      if (response.statusCode == 200) {
        String apiData = (await response.stream.bytesToString());
        data = await json.decode(apiData);

        for (var element in data) {
          if (element.containsKey('yoast_head_json') &&
              element['yoast_head_json'] is Map) {
            (element['yoast_head_json'] as Map).remove('robots');
            (element['yoast_head_json'] as Map).remove('schema');
          }
          element.remove('yoast_head');
          element.remove('_links');

          apiFetchedProductData
              .add(ProdCatData(subcatData: element, prodData: []));
        }

        // print(apiData);
        print(apiFetchedProductData.length);
      } else {
        print(response.reasonPhrase);

        // break;
      }
      if (data.length != (int.tryParse(perPageCount) ?? 0)) {
        allCatFetched = true;
      }
      // } while (data.length == (int.tryParse(perPageCount) ?? 0));
    } catch (e) {
      print(e.toString());
    }
    fetchingDataCat = false;
    setState(() {});
  }
}

Future<List<String>> getSingleProduct(
    String catId, String consumerKey, String consumerSecretKey) async {
  try {
    // do {
    var headers = {
      'Cookie':
          'PHPSESSID=f5676c3eb3491ec53b785f4b9d786080; wfwaf-authcookie-ac650b89681ecea921a2d14a6fe8b501=1%7Cadministrator%7Cmanage_options%2Cunfiltered_html%2Cedit_others_posts%2Cupload_files%2Cpublish_posts%2Cedit_posts%2Cread%7C6686fa9be733eb2d2b39fb8fab22003f98488fec54a92ac3150c49d1f2efd052'
    };
    var request = http.Request(
        'GET',
        Uri.parse(
            'https://www.thehub.in/wp-json/wc/v3/products?per_page=1&category=$catId&consumer_key=$consumerKey&consumer_secret=$consumerSecretKey&oauth_consumer_key=ck_4f9c97222da3ba2936af8bdf324747ab96087c1b&oauth_signature_method=HMAC-SHA1&oauth_timestamp=1745988759&oauth_nonce=LwETC5sKZ4S&oauth_version=1.0&oauth_signature=IhILwe7y4WUOBUhnFp2tPEUf%2F2E%3D'));

    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();

    print("prod----${response.statusCode}");
    print("prod----${response.reasonPhrase}");

    if (response.statusCode == 200) {
      String apiData = (await response.stream.bytesToString());
      List<dynamic> data = await json.decode(apiData);

      print(data.length);
      if (data.isNotEmpty) {
        return [
          (data.first as Map)['name'],
          (data.first as Map)['description']
        ];
      } else {
        return ['', ''];
      }
    } else {
      return ['', ''];
    }

    // } while (data.length == (int.tryParse(perPageCount) ?? 0));
  } catch (e) {
    print(e.toString());
    return ['', ''];
  }
}
