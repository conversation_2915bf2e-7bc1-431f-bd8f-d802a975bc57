import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import 'package:wedding_super_admin/shared/firebase.dart';

import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/views/Api/widgets/shopify.dart';
import 'package:wedding_super_admin/views/Api/widgets/woocommerce.dart';

import '../../shared/theme.dart';

class PlatformApiPage extends StatefulWidget {
  const PlatformApiPage({super.key, required this.vendor});

  final VendorModel vendor;
  @override
  State<PlatformApiPage> createState() => _PlatformApiPageState();
}

class _PlatformApiPageState extends State<PlatformApiPage> {
  List<String> platforms = [];
  String? selectedPlatform;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.vendor.apiRequiredData.isNotEmpty) {
      if (widget.vendor.apiRequiredData.containsKey('selectedApi')) {
        selectedPlatform = widget.vendor.apiRequiredData['selectedApi'];
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: GetBuilder<HomeCtrl>(builder: (ctrl) {
        if (platforms.isEmpty) {
          platforms = ctrl.settings?.apiPlatform ?? [];
        }
        return platforms.isEmpty
            ? Center(child: CircularProgressIndicator())
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      IconButton(
                          onPressed: () {
                            context.pop();
                          },
                          icon: const Icon(
                            CupertinoIcons.arrow_left,
                            color: themeColor,
                          )),
                      const SizedBox(width: 10),
                      Text('Import Data',
                          style: GoogleFonts.zcoolXiaoWei(
                            fontSize: 35,
                            color: themeColor,
                          )),
                    ],
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Divider(),
                  SizedBox(
                    height: 20,
                  ),
                  Text("Platform Selection",
                      style: GoogleFonts.zcoolXiaoWei(
                        fontSize: 24,
                        color: themeColor,
                      )),
                  SizedBox(height: 20),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 300),
                    child: DropdownButtonFormField(
                      value: selectedPlatform,
                      // borderRadius: BorderRadius.all(Radius.circular(
                      //   5,
                      // )
                      // ),
                      decoration:
                          inpDecor().copyWith(labelText: 'Select Platform'),
                      items: List.generate(
                        platforms.length,
                        (index) {
                          return DropdownMenuItem(
                            value: platforms[index],
                            child: Text(platforms[index]),
                          );
                        },
                      ),
                      onChanged: (value) async {
                        await FBFireStore.vendors
                            .doc(widget.vendor.docId)
                            .update({'apiRequiredData.selectedApi': value});
                        setState(() {
                          selectedPlatform = value;
                        });
                      },
                    ),
                  ),
                  if (selectedPlatform != null) ...[
                    getSelectedVendorUI(selectedPlatform!, widget.vendor)
                  ]
                ],
              );
      }),
    );
  }

  // getData(String selectedVendor) {
  //   switch (selectedVendor) {
  //     case 'woocommerce':
  //       return woocommerceGet();
  //     case 'shopify':
  //       return {};

  //     default:
  //       return {showAppSnackBar(context, "No Matching Api")};
  //   }
  // }
}

Widget getSelectedVendorUI(String selectedPlatform, VendorModel vendor) {
  switch (selectedPlatform.toLowerCase()) {
    case 'woocommerce':
      return WooCommerce(vendor: vendor);

    case 'shopify':
      return Shopify(vendor: vendor);
    default:
      return SizedBox();
  }
}
