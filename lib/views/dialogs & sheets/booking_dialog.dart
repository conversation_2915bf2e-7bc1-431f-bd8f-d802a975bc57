import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/session_model.dart';
import 'package:wedding_super_admin/models/user_model.dart';

import '../../models/bookings_model.dart';
import '../../shared/booking_methods.dart';
import '../../shared/const.dart';
import '../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../../shared/theme.dart';

bookingDialog({
  required BuildContext context,
  required String userDocId,
  required String sessionId,
  required String? bookingId,
  required Function? refresh,
  // required String messageId,
}) async {
  return showDialog(
    context: context,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState) {
          return Dialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            shape: const RoundedRectangleBorder(),
            child: BookingDialogWid(
              refresh: refresh,
              bookingId: bookingId,
              // messageId: messageId,
              sessionId: sessionId,
              userDocId: userDocId,
            ),
          );
        },
      );
    },
  );
}

class BookingDialogWid extends StatefulWidget {
  const BookingDialogWid({
    super.key,
    required this.userDocId,
    required this.sessionId,
    required this.refresh,
    this.bookingId,
    // required this.messageId,
  });
  final String userDocId;
  final String sessionId;
  final Function? refresh;
  final String? bookingId;
  // final String messageId;
  @override
  State<BookingDialogWid> createState() => _BookingDialogWidState();
}

class _BookingDialogWidState extends State<BookingDialogWid> {
  @override
  void initState() {
    super.initState();
    pageIndex = 0;
  }

  // List<String> loginWith = ['Email', 'Mobile'];
  // String selectedLoginType = 'Email';
  bool firstTime = true;
  bool onSubmitLoad = false;
  List<String> slotsList = [];
  int? slotGap;
  DateTime selectedDate = DateTime.now();
  String? selectedSlot;
  UserModel? currentUser;
  TextEditingController fnameCtrl = TextEditingController();
  TextEditingController lnameCtrl = TextEditingController();
  TextEditingController emailCtrl = TextEditingController();
  TextEditingController mobileCtrl = TextEditingController();
  // TextEditingController authEmailCtrl = TextEditingController();
  // TextEditingController authPhoneCtrl = TextEditingController();
  PageController pageCtrl = PageController();
  PageController loginPageCtrl = PageController();
  String? selectedCountryCode;
  int? pageIndex;
  BookingsModel? bookingModel;
  setBookingForm() async {
    final hctrl = Get.find<HomeCtrl>();

    final settings = hctrl.settings;
    if (settings != null) {
      DateTime bookingStartTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        settings.startHour,
        settings.startMin,
      );

      final bookingEndTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        settings.endHour,
        settings.endMin,
      );

      slotGap = settings.slotGap;

      slotsList = generateTimeSlotsWithBlocked(bookingStartTime, bookingEndTime,
          settings.slotGap, hctrl.blockedSlotModelList);
    }
    if (firstTime) {
      if (widget.bookingId != null) {
        final bookingSnap =
            await FBFireStore.bookings.doc(widget.bookingId).get();
        bookingModel = BookingsModel.fromDocSnap(bookingSnap);
        selectedDate = bookingModel?.bookingDate ?? selectedDate;
      }
      final userTemp = await FBFireStore.users.doc(widget.userDocId).get();
      currentUser = UserModel.fromDocSnap(userTemp);
      emailCtrl.text = currentUser?.email ?? "";
      mobileCtrl.text = currentUser?.phone ?? "";
      fnameCtrl.text = (currentUser?.name?.split(" ")[0]) ?? '';
      lnameCtrl.text = (currentUser?.name?.split(" ")[1]) ?? '';
      setState(() {});
      firstTime = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    setBookingForm();
    return Container(
      color: Colors.white,
      constraints: const BoxConstraints(
        maxHeight: 500,
        maxWidth: 800,
      ),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(opacity: animation, child: child);
        },
        child: [
          // COLLECT BOOKING DETAILS PAGE
          Row(
            children: [
              commonBookingDetailsWid(),
              Expanded(
                flex: 2,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 25, vertical: 25),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Align(
                          alignment: Alignment.topRight,
                          child: dialogCloseButton(context)),
                      Text(
                        'Select Date & Time',
                        style: GoogleFonts.zcoolXiaoWei(
                            fontWeight: FontWeight.w500,
                            color: const Color(0xff1C1C1C),
                            fontSize: 28),
                      ),
                      const SizedBox(height: 20),
                      Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              flex: 3,
                              child: LimitedBox(
                                child: CalendarDatePicker(
                                  initialDate: DateTime.now(),
                                  firstDate: DateTime.now(),
                                  lastDate: DateTime(3000),
                                  onDateChanged: (value) {
                                    // showDialog(
                                    //   context: context,
                                    //   builder: (context) => AlertDialog(),
                                    // ).then(
                                    //   (value) => showDialog(
                                    //     context: context,
                                    //     builder: (context) {},
                                    //   ),
                                    // );
                                    selectedDate = value;
                                    setState(() {});
                                  },
                                ),
                              ),
                            ),
                            const SizedBox(width: 15),
                            Expanded(
                              child: slotsList.isEmpty
                                  ? const Center(
                                      child: Text('Slots unavailable'))
                                  : SingleChildScrollView(
                                      padding: const EdgeInsets.only(right: 25),
                                      child: Wrap(
                                        runSpacing: 10,
                                        // crossAxisCount: 1,
                                        // mainAxisSpacing: 10,
                                        children: [
                                          ...List.generate(
                                            slotsList.length,
                                            (index) {
                                              final selected = selectedSlot ==
                                                  slotsList[index];
                                              return InkWell(
                                                onTap: () {
                                                  selectedSlot =
                                                      slotsList[index];
                                                  setState(() {});
                                                },
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(7),
                                                  decoration: BoxDecoration(
                                                      color: selected
                                                          ? themeColor
                                                          : null,
                                                      border: Border.all(
                                                          color: selected
                                                              ? themeColor
                                                              : const Color(
                                                                  0xff6C6C6C))),
                                                  child: Center(
                                                    child: Text(
                                                      slotsList[index]
                                                          .split(" - ")[0],
                                                      style: GoogleFonts.livvic(
                                                        color: selected
                                                            ? Colors.white
                                                            : null,
                                                        fontSize: 12,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          )
                                        ],
                                      ),
                                    ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            shape: const RoundedRectangleBorder(),
                            backgroundColor: const Color(0xffD88F77),
                            fixedSize: const Size(double.maxFinite, 40)),
                        onPressed: () {
                          if (selectedSlot == null) {
                            showErrorAppSnackBar(context, 'Please select slot');
                            return;
                          }
                          pageIndex = 1;
                          setState(() {});
                          // pageCtrl.animateToPage(1,
                          //     duration: const Duration(milliseconds: 300),
                          //     curve: Curves.linear);
                        },
                        child: Text(
                          "Next",
                          style: GoogleFonts.livvic(
                            letterSpacing: 1,
                            color: const Color(0xff1C1C1C),
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // COLLECTING USER DETAILS

          Row(
            children: [
              commonBookingDetailsWid(),
              Expanded(
                flex: 2,
                child: SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 25, vertical: 25),
                  // padding: const EdgeInsets.only(right: 15),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          IconButton(
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            splashColor: Colors.transparent,
                            onPressed: () {
                              pageIndex = 0;
                              setState(() {});
                              // pageCtrl.animateTo(
                              //   0,
                              //   duration:
                              //       const Duration(milliseconds: 300),
                              //   curve: Curves.linear,
                              // );
                            },
                            icon: const Icon(CupertinoIcons.back),
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Your Details',
                                  style: GoogleFonts.zcoolXiaoWei(
                                    color: const Color(0xff1C1C1C),
                                    fontSize: 27,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                textField('First Name', fnameCtrl, null),
                                const SizedBox(height: 10),
                                textField('Last Name', lnameCtrl, null),
                                const SizedBox(height: 10),
                                textField('Email', emailCtrl, null),
                                const SizedBox(height: 10),
                                textField('Mobile', mobileCtrl, (value) {
                                  if (value.isEmpty) {
                                    mobileCtrl.clear();
                                    selectedCountryCode = null;
                                  }
                                  if ((selectedCountryCode == null ||
                                          selectedCountryCode != value) &&
                                      value.substring(0, 1).contains('+') &&
                                      value.length >= 2) {
                                    final countryCodes = countryCodeFlagsList
                                        .map((e) => e['dial_code'])
                                        .toList();
                                    countryCodes.sort((a, b) =>
                                        a!.length.compareTo(b!.length));
                                    selectedCountryCode =
                                        countryCodes.firstWhereOrNull((e) =>
                                                e!.contains(value) &&
                                                e.length <= value.length) ??
                                            selectedCountryCode;
                                  }
                                  setState(() {});
                                }, true),
                                const SizedBox(height: 20),
                                onSubmitLoad
                                    ? const SizedBox(
                                        height: 25,
                                        width: 25,
                                        child: Center(
                                          child: CircularProgressIndicator(
                                            strokeWidth: 3.5,
                                          ),
                                        ),
                                      )
                                    : ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                            shape:
                                                const RoundedRectangleBorder(),
                                            backgroundColor:
                                                const Color(0xffD88F77),
                                            fixedSize: const Size(200, 40)),
                                        onPressed: () async {
                                          if (selectedSlot == null) {
                                            return;
                                          }

                                          if (emailCtrl.text.trim().isEmpty) {
                                            showErrorAppSnackBar(
                                                context, 'Email Required');
                                            return;
                                          }
                                          if (selectedCountryCode == null) {
                                            showErrorAppSnackBar(context,
                                                'Country Code Required');
                                            return;
                                          }

                                          if (mobileCtrl.text.trim().isEmpty) {
                                            showErrorAppSnackBar(
                                                context, 'Mobile Required');
                                            return;
                                          }

                                          if (onSubmitLoad) return;

                                          try {
                                            setState(() {
                                              onSubmitLoad = true;
                                            });
                                            createBookingDocumentFunction();
                                            setState(() {
                                              onSubmitLoad = false;
                                            });

                                            if (widget.refresh != null) {
                                              widget.refresh!();
                                            }
                                          } on Exception catch (e) {
                                            setState(() {
                                              onSubmitLoad = false;
                                            });
                                            debugPrint(e.toString());
                                          }
                                        },
                                        child: Text(
                                          "Schedule Meeting",
                                          style: GoogleFonts.livvic(
                                            letterSpacing: 1,
                                            color: const Color(0xff1C1C1C),
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                const SizedBox(height: 10),
                              ],
                            ),
                          ),
                          const SizedBox(width: 7),
                          dialogCloseButton(context),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ][pageIndex ?? 0],
      ),
    );
  }

  Expanded commonBookingDetailsWid() {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 50, horizontal: 30),
        color: const Color(0xffFFF7F5),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              "assets/logo.png",
            ),
            const SizedBox(height: 50),
            Text(
              '$slotGap Mins Call',
              style: GoogleFonts.zcoolXiaoWei(
                color: const Color(0xff1C1C1C),
                fontSize: 25,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 50),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  CupertinoIcons.stopwatch,
                  size: 18,
                ),
                const SizedBox(width: 6),
                Text(
                  '$slotGap min',
                  style: GoogleFonts.leagueSpartan(
                    color: const Color(0xff1C1C1C),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 13),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  CupertinoIcons.calendar,
                  size: 18,
                ),
                const SizedBox(width: 6),
                Text(
                  selectedDate.goodDayDate(),
                  style: GoogleFonts.leagueSpartan(
                    color: const Color(0xff1C1C1C),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            if (selectedSlot != null) ...[
              const SizedBox(height: 13),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(
                    CupertinoIcons.clock,
                    size: 18,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    selectedSlot!,
                    style: GoogleFonts.leagueSpartan(
                      color: const Color(0xff1C1C1C),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ]
          ],
        ),
      ),
    );
  }

  createBookingDocumentFunction() async {
    String bookingComment = '';
    // widget.mapQuestion?.entries
    //         .map((e) => '${e.key}: ${e.value.join(", ")}')
    //         .join("\n") ??
    //     "";

    final convertedSlot = stringToDateTime(selectedSlot!);

    final bookingDate = selectedDate;

    final selectedSlotstartTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        convertedSlot['startTime']!.hour,
        convertedSlot['startTime']!.minute);
    final selectedSlotendTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        convertedSlot['endTime']!.hour,
        convertedSlot['endTime']!.minute);

    final batch = FBFireStore.fb.batch();
    final bookingRef = widget.bookingId != null
        ? FBFireStore.bookings.doc(widget.bookingId)
        : FBFireStore.bookings.doc();
    final messageRef = bookingModel != null
        ? FBFireStore.sessions
            .doc(widget.sessionId)
            .collection('messages')
            .doc(bookingModel?.messageId)
        : FBFireStore.sessions
            .doc(widget.sessionId)
            .collection('messages')
            .doc();
    final infoRef =
        FBFireStore.sessions.doc(widget.sessionId).collection('messages').doc();
    final bookingData = {
      'uId': widget.userDocId,
      'createdAt': bookingModel != null
          ? bookingModel?.createdAt.millisecondsSinceEpoch
          : DateTime.now().millisecondsSinceEpoch,
      'createdByDocId': FBAuth.auth.currentUser?.uid,
      'createdBy': Get.find<HomeCtrl>().currentUserType,
      'teamMemberId': bookingModel?.teamMemberId,
      'stylerId': bookingModel?.stylerId,
      'sessionId': widget.sessionId,
      'messageId': messageRef.id,
      'bookingDate': bookingDate.millisecondsSinceEpoch,
      'bookingstartTime': selectedSlotstartTime.millisecondsSinceEpoch,
      'bookingendTime': selectedSlotendTime.millisecondsSinceEpoch,
      'slotgap': slotGap,
      'teamsLink': bookingModel?.teamsLink ?? '',
      'productIds': bookingModel?.productIds ?? [],
      'teamMemberComment': bookingModel?.teamMemberComment ?? '',
      'bookingComment': bookingModel?.bookingComment ?? bookingComment,
      'isCompleted': bookingModel?.isCompleted ?? false,
      'completedAt': bookingModel?.completedAt?.millisecondsSinceEpoch,
      'cancelledAt': bookingModel?.cancelledAt?.millisecondsSinceEpoch,
      'isCancelled': bookingModel?.isCancelled ?? false,
      'emailAtBooking': emailCtrl.text.trim().toLowerCase(),
      'fullnameAtBooking':
          '${fnameCtrl.text.trim().toLowerCase()} ${lnameCtrl.text.trim().toLowerCase()}',
      'mobileAtBooking':
          '$selectedCountryCode${mobileCtrl.text.contains(selectedCountryCode!) ? mobileCtrl.text.trim().substring(selectedCountryCode!.length) : mobileCtrl.text.trim()}',
      'cancelledReason': bookingModel?.cancelledReason,
    };

    final messageData = {
      'type': MessageTypes.booking,
      'sessionId': widget.sessionId,
      'loadingText': 'Booking details',
      'data': {
        'date': selectedDate.millisecondsSinceEpoch,
        'bookingstartTime': selectedSlotstartTime.millisecondsSinceEpoch,
        'bookingendTime': selectedSlotendTime.millisecondsSinceEpoch,
        'slotgap': slotGap,
        'bookingId': bookingRef.id,
      },
      'senderId': FBAuth.auth.currentUser?.uid,
      'sendAt': bookingModel != null
          ? bookingModel?.createdAt.millisecondsSinceEpoch
          : DateTime.now().millisecondsSinceEpoch,
    };

    final infoData = {
      'type': MessageTypes.info,
      'sessionId': widget.sessionId,
      'loadingText': 'info',
      'data': {
        'text': bookingModel != null ? 'Booking Updated' : 'Booking Confirmed'
      },
      'senderId': FBAuth.auth.currentUser?.uid,
      'sendAt': DateTime.now().millisecondsSinceEpoch,
    };
    // onLongPress: () async {
    //       final updatedDate = DateTime(2025, 3, 19).millisecondsSinceEpoch;
    //       final batch = FBFireStore.fb.batch();
    //       final bookingRef = FBFireStore.bookings.doc(widget.bookingsModel.docID);
    //       batch.update(bookingRef, {'bookingDate': updatedDate});
    //       if (widget.bookingsModel.sessionId != null) {
    //         final messageRef = FBFireStore.sessions
    //             .doc(widget.bookingsModel.sessionId)
    //             .collection('messages')
    //             .doc(widget.bookingsModel.messageId);
    //         batch.update(messageRef, {
    //           'data.date': updatedDate,
    //         });
    //       }
    //       await batch.commit();
    //     },

    try {
      if (bookingModel != null) {
        final messageHistoryRef = FBFireStore.sessions
            .doc(widget.sessionId)
            .collection('messages')
            .doc(messageRef.id)
            .collection('history')
            .doc();
        final previousMessageSnap = await FBFireStore.sessions
            .doc(widget.sessionId)
            .collection('messages')
            .doc(bookingModel?.messageId)
            .get();

        Map<String, dynamic> historyData = previousMessageSnap.data() ?? {};
        batch.set(messageHistoryRef, historyData);
      }

      batch.set(bookingRef, bookingData);
      batch.set(messageRef, messageData);
      batch.set(infoRef, infoData);
      batch.update(FBFireStore.sessions.doc(widget.sessionId),
          {'lastMessage': messageData});
      await batch.commit();
      Navigator.of(context).pop(true);

      await FBFunctions.ff
          .httpsCallable('sendBookingDetails')
          .call(<String, String>{
        'bookingDate': bookingDate.convertToDDMMYY(),
        'bookingstartTime': selectedSlotstartTime.goodTime(),
        'bookingendTime': selectedSlotendTime.goodTime(),
        'emailAtBooking': emailCtrl.text.trim().toLowerCase(),
        'fullnameAtBooking': capilatlizeFirstLetter(
            '${fnameCtrl.text.trim().toLowerCase()} ${lnameCtrl.text.trim().toLowerCase()}'),
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
      showErrorAppSnackBar(context, 'Someting went wrong');
    }
  }

  Widget buildCountryCodePicker(
      BuildContext context, void Function(void Function()) refresh) {
    // const countryCodes = ['+1', '+44', '+91', '+81', '+61'];
    return InkWell(
      onTap: () {
        showDialog(
          context: context,
          builder: (context) {
            final searchCtrl = TextEditingController();

            List<Map<String, String>> filteredList = [];
            getSearchedList() {
              if (searchCtrl.text.isEmpty) {
                filteredList = countryCodeFlagsList;
              } else {
                final temp = countryCodeFlagsList.where((e) {
                  return e['name']!
                      .toLowerCase()
                      .contains(searchCtrl.text.trim().toLowerCase());
                }).toList();
                filteredList = temp;
              }
            }

            return StatefulBuilder(builder: (context, setState3) {
              getSearchedList();
              return Dialog(
                backgroundColor: Colors.white,
                surfaceTintColor: Colors.white,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  constraints:
                      const BoxConstraints(maxWidth: 500, maxHeight: 450),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: searchCtrl,
                              onChanged: (value) {
                                setState3(() {});
                              },
                              decoration: const InputDecoration(
                                  labelText: 'Search by name or dial code'),
                            ),
                          ),
                        ],
                      ),
                      Expanded(
                        child: ListView.separated(
                          shrinkWrap: true,
                          itemCount: filteredList.length,
                          itemBuilder: (context, index) {
                            return ListTile(
                              onTap: () {
                                selectedCountryCode =
                                    filteredList[index]['dial_code']!;

                                Navigator.of(context).pop();
                                refresh(() {});
                              },
                              // {"name": "Afghanistan", "flag": "🇦🇫", "code": "AF", "dial_code": "+93"},
                              title: Text(filteredList[index]['name'] ?? ""),
                              subtitle:
                                  Text(filteredList[index]['dial_code'] ?? ""),
                              leading: CircleAvatar(
                                child: Text(filteredList[index]['flag'] ?? ""),
                              ),
                            );
                          },
                          separatorBuilder: (context, index) {
                            return const SizedBox(height: 10);
                          },
                        ),
                      )
                    ],
                  ),
                ),
              );
            });
          },
        );
      },
      child: IgnorePointer(
        ignoring: true,
        child: DropdownButton<String>(
          value: selectedCountryCode,
          hint: const Text("Country code"),
          underline: const SizedBox(), // Remove underline
          onChanged: (value) {},
          items: [
            ...List.generate(
              countryCodeFlagsList.length,
              (index) {
                final countryCodeFlag = countryCodeFlagsList[index];
                return DropdownMenuItem<String>(
                  value: countryCodeFlag['dial_code'],
                  child: Row(
                    children: [
                      Text(
                        // getFlagEmoji(countryCodeFlag['code']!),
                        countryCodeFlag['flag']!,
                        style: const TextStyle(fontSize: 24),
                      ),
                      const SizedBox(width: 3),
                      Text(
                        countryCodeFlag['dial_code']!,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<dynamic> confirmationPopoup(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          title: const Text('Booking Confirm'),
          content: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Text(
                'Your booking for date ${selectedDate.goodDayDate()} in slot $selectedSlot has been confirmed. You will recive an email for same on given email within 24 hours.'),
          ),
          actions: [
            TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Done'))
          ],
        );
      },
    );
  }

  Widget textField(String labelText, TextEditingController controller,
      Function(String)? onChanged,
      [bool wantprefix = false]) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      // mainAxisSize: MainAxisSize.min,
      children: [
        Text(labelText),
        const SizedBox(height: 5),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                onChanged: onChanged,
                controller: controller,
                decoration: InputDecoration(
                  prefixIcon: wantprefix
                      ? Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: buildCountryCodePicker(context, setState))
                      : null,
                  hintText: ' $labelText',
                  hintStyle: GoogleFonts.mulish(
                    color: const Color(0XffCBCBCB),
                    fontSize: 14,
                  ),
                  border: const OutlineInputBorder(
                    borderSide: BorderSide(color: Color(0xffCBCBCB)),
                  ),
                  enabledBorder: const OutlineInputBorder(
                    borderSide: BorderSide(color: Color(0xffCBCBCB)),
                  ),
                  focusedBorder: const OutlineInputBorder(
                    borderSide: BorderSide(color: Color(0xffCBCBCB)),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget dialogCloseButton(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop(false);
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Color(0xffFFF7F5),
        ),
        child: const Icon(
          CupertinoIcons.xmark,
          size: 18,
        ),
      ),
    );
  }
}

Future<dynamic> bookingConfirm(BuildContext context) {
  return showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: const Center(
          child: Icon(
            CupertinoIcons.checkmark_alt_circle,
            color: Colors.green,
            size: 70,
          ),
        ),
        content: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            child: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Booking Confirm',
                  style: TextStyle(
                    color: Color(0xff3E3E3E),
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 5),
                Text(
                  'Email sent to user with booking details.',
                  style: TextStyle(
                      color: Color.fromARGB(255, 90, 89, 89), fontSize: 15),
                ),
              ],
            )
            // 'Your booking for date ${selectedDate.goodDayDate()} in slot $selectedSlot has been confirmed. You will recive an email for same on given email within 24 hours.'),
            ),
        actions: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(2)),
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('OK')),
              ),
            ],
          )
        ],
      );
    },
  );
}
