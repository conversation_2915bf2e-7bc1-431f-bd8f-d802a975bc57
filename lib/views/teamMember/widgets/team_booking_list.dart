import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:wedding_super_admin/models/bookings_model.dart';

import '../../../controller/home_ctrl.dart';
import '../../../models/teammodel.dart';
import '../../bookings/widget/booking_tile_card.dart';
import '../../common/page_header.dart';

class TeamBookingList extends StatefulWidget {
  const TeamBookingList({super.key, required this.teamMemberId});
  final String teamMemberId;
  @override
  State<TeamBookingList> createState() => _TeamBookingListState();
}

class _TeamBookingListState extends State<TeamBookingList> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (hCtrl) {
        final teamMember = hCtrl.teamMembers.firstWhereOrNull(
            (element) => element.docId == widget.teamMemberId);
        // final teamMemeberSessions = hCtrl.activeSessionsList.where((element) {
        //   List<String> teamDocIdList = [];
        //   teamDocIdList.add(element.consultantId);
        //   teamDocIdList.add(element.stylerId);
        //   return teamDocIdList.contains(teamMember?.docId);
        // }).toList();
        final teamMemeberBookings = hCtrl.bookingList.where((element) {
          List<String> teamDocIdList = [];
          teamDocIdList.addIf(element.stylerId != null, element.stylerId ?? '');
          teamDocIdList.addIf(
              element.teamMemberId != null, element.teamMemberId ?? '');
          return teamDocIdList.contains(teamMember?.docId);
        }).toList();
        return TeamMemberBookings(
          teamMemberBookings: teamMemeberBookings,
          teamMember: teamMember,
        );
      },
    );
  }
}

class TeamMemberBookings extends StatefulWidget {
  const TeamMemberBookings(
      {super.key, this.teamMember, required this.teamMemberBookings});
  final TeamModel? teamMember;
  final List<BookingsModel> teamMemberBookings;
  @override
  State<TeamMemberBookings> createState() => _TeamMemberBookingsState();
}

class _TeamMemberBookingsState extends State<TeamMemberBookings> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithTrailingAndBack(
            title: 'Bookings',
            showTrailing: false,
          ),
          const SizedBox(height: 25),
          widget.teamMemberBookings.isNotEmpty
              ? StaggeredGrid.extent(
                  maxCrossAxisExtent: 377,
                  crossAxisSpacing: 25,
                  mainAxisSpacing: 25,
                  children: [
                    ...List.generate(
                      widget.teamMemberBookings.length,
                      (index) {
                        final bookingData = widget.teamMemberBookings[index];
                        return BookingCard(bookingsModel: bookingData);
                      },
                    )
                  ],
                )
              : const Center(
                  child: Text('No Bookings Currently'),
                )
        ],
      ),
    );
  }
}
