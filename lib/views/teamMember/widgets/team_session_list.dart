import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wedding_super_admin/models/session_model.dart';

import '../../../controller/home_ctrl.dart';
import '../../../models/teammodel.dart';
import '../../../shared/theme.dart';
import '../../common/page_header.dart';
import '../../sessions/widgets/session_table_header.dart';
import '../../sessions/widgets/session_tile.dart';

class TeamSessionList extends StatefulWidget {
  const TeamSessionList({super.key, required this.teamMemberId});
  final String teamMemberId;
  @override
  State<TeamSessionList> createState() => _TeamSessionListState();
}

class _TeamSessionListState extends State<TeamSessionList> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (hCtrl) {
        final teamMember = hCtrl.teamMembers.firstWhereOrNull(
            (element) => element.docId == widget.teamMemberId);
        final teamMemberSessions = hCtrl.activeSessionsList.where((element) {
          List<String> teamDocIdList = [];
          teamDocIdList.add(element.consultantId);
          teamDocIdList.add(element.stylerId);
          return teamDocIdList.contains(teamMember?.docId);
        }).toList();

        return TeamMemberSessions(
          teamMemberSessions: teamMemberSessions,
          teamMember: teamMember,
        );
      },
    );
  }
}

class TeamMemberSessions extends StatefulWidget {
  const TeamMemberSessions(
      {super.key, this.teamMember, required this.teamMemberSessions});
  final TeamModel? teamMember;
  final List<SessionModel> teamMemberSessions;
  @override
  State<TeamMemberSessions> createState() => _TeamMemberSessionsState();
}

class _TeamMemberSessionsState extends State<TeamMemberSessions> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          PageHeaderWithTrailingAndBack(
            title: 'Member Inquiries',
            showTrailing: false,
          ),
          // const SizedBox(height: 20),
          // SearchField(
          //   searchController: searchController,
          //   onChanged: (p0) async {
          //     setState(() {});
          //   },
          // ),
          const SizedBox(height: 25),
          const SessionTableHeader(),
          widget.teamMemberSessions.isEmpty
              ? Container(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  decoration: const BoxDecoration(
                      border: Border(
                          bottom: BorderSide(color: dividerColor),
                          left: BorderSide(color: dividerColor),
                          right: BorderSide(color: dividerColor)),
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(8),
                          bottomRight: Radius.circular(8))),
                  child: Center(
                      child: Text(
                    'No Data Available',
                    style: TextStyle(
                        color: Color(0xff737373),
                        fontSize: 14.5,
                        fontWeight: FontWeight.w500),
                  )),
                )
              : Column(
                  children: [
                    ...List.generate(
                      widget.teamMemberSessions.length,
                      (index) {
                        return SessionTile(
                            isLast:
                                index == (widget.teamMemberSessions.length - 1),
                            sessionModel: widget.teamMemberSessions[index],
                            index: index);
                      },
                    )
                  ],
                ),
        ]));
  }
}
