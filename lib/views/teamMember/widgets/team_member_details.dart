import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/teammodel.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/shared/router.dart';
import 'package:wedding_super_admin/shared/theme.dart';

import '../../../shared/const.dart';
import '../../../shared/firebase.dart';
import '../../common/page_header.dart';

class TeamMemberDetails extends StatefulWidget {
  const TeamMemberDetails({super.key, required this.teamMemeberId});
  final String teamMemeberId;
  @override
  State<TeamMemberDetails> createState() => _TeamMemberDetailsState();
}

class _TeamMemberDetailsState extends State<TeamMemberDetails> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (hCtrl) {
        final teamMember = hCtrl.teamMembers.firstWhereOrNull(
            (element) => element.docId == widget.teamMemeberId);
        final teamMemeberSessions = hCtrl.activeSessionsList.where((element) {
          List<String> teamDocIdList = [];
          teamDocIdList.add(element.consultantId);
          teamDocIdList.add(element.stylerId);
          return teamDocIdList.contains(teamMember?.docId);
        }).toList();
        final teamMemeberBookings = hCtrl.bookingList.where((element) {
          List<String> teamDocIdList = [];
          teamDocIdList.addIf(element.stylerId != null, element.stylerId ?? '');
          teamDocIdList.addIf(
              element.teamMemberId != null, element.teamMemberId ?? '');
          return teamDocIdList.contains(teamMember?.docId);
        }).toList();
        return TeamMemberDetailsScreen(
            teamMemberBookings: teamMemeberBookings.length,
            teamMemberSessions: teamMemeberSessions.length,
            teamMember: teamMember);
      },
    );
  }
}

class TeamMemberDetailsScreen extends StatefulWidget {
  const TeamMemberDetailsScreen(
      {super.key,
      required this.teamMember,
      required this.teamMemberSessions,
      required this.teamMemberBookings});
  final TeamModel? teamMember;
  final int teamMemberSessions;
  final int teamMemberBookings;
  @override
  State<TeamMemberDetailsScreen> createState() =>
      _TeamMemberDetailsScreenState();
}

class _TeamMemberDetailsScreenState extends State<TeamMemberDetailsScreen> {
  bool loading = false;
  final nameCtrl = TextEditingController();
  final emailCtrl = TextEditingController();
  final phoneCtrl = TextEditingController();
  final addressCtrl = TextEditingController();
  final specialityCtrl = TextEditingController();
  String? selectedUserType;
  List<String> userTypeList = [
    UserTypes.consultants,
    UserTypes.manager,
    UserTypes.styler
  ];
  getRequiredData() {
    nameCtrl.text = widget.teamMember?.name ?? '';
    emailCtrl.text = widget.teamMember?.email ?? '';
    phoneCtrl.text = widget.teamMember?.phone ?? '';
    addressCtrl.text = widget.teamMember?.address ?? '';
    specialityCtrl.text = widget.teamMember?.speciality ?? '';
    selectedUserType = widget.teamMember?.userType;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    getRequiredData();
  }

  @override
  Widget build(BuildContext context) {
    return widget.teamMember != null
        ? SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PageHeaderWithTrailingAndBack(
                  title: 'Team Member Details',
                  showTrailing: true,
                  trailing: loading
                      ? const Center(
                          child: SizedBox(
                            height: 25,
                            width: 25,
                            child: CircularProgressIndicator(
                              strokeWidth: 2.5,
                              color: themeColor,
                            ),
                          ),
                        )
                      : ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: themeColor,
                            elevation: 0,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4)),
                            // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                          ),
                          onPressed: () async {
                            if (loading) return;

                            try {
                              if (nameCtrl.text.isEmpty) {
                                showErrorAppSnackBar(context, 'Name Required');
                                return;
                              }
                              if (phoneCtrl.text.isEmpty) {
                                showErrorAppSnackBar(
                                    context, 'Number Required');
                                return;
                              }
                              if (emailCtrl.text.isEmpty) {
                                showErrorAppSnackBar(context, 'Email Required');
                                return;
                              }
                              if (selectedUserType == null) {
                                showErrorAppSnackBar(
                                    context, 'Select User Type');
                                return;
                              }
                              if (selectedUserType == UserTypes.styler &&
                                  specialityCtrl.text.trim().isEmpty) {
                                showErrorAppSnackBar(
                                    context, 'Speciality Required');
                                return;
                              }

                              final teamMemberSnap = await FBFireStore
                                  .teamMember
                                  .where('email',
                                      isEqualTo:
                                          emailCtrl.text.toLowerCase().trim())
                                  .where(FieldPath.documentId,
                                      isNotEqualTo: widget.teamMember?.docId)
                                  .get();

                              if (teamMemberSnap.size != 0) {
                                showErrorAppSnackBar(
                                    context, 'Email Already exist');
                                return;
                              }

                              setState(() {
                                loading = true;
                              });

                              String? speciality =
                                  selectedUserType == UserTypes.styler
                                      ? specialityCtrl.text.trim()
                                      : null;

                              final phone =
                                  phoneCtrl.text.trim().substring(0, 3) == '+91'
                                      ? phoneCtrl.text.trim()
                                      : '+91${phoneCtrl.text.trim()}';
                              final data = {
                                'name': nameCtrl.text.toLowerCase().trim(),
                                'phone': phone,
                                'address': addressCtrl.text.trim(),
                                'email': emailCtrl.text.toLowerCase().trim(),
                                'password': widget.teamMember != null
                                    ? widget.teamMember?.password
                                    : getRandomMix(8),
                                'subcatids': widget.teamMember != null
                                    ? widget.teamMember?.subcatids
                                    : [],
                                'userType': selectedUserType,
                                'speciality': speciality,
                                'createdAt': widget.teamMember?.createdAt,
                                'isActive': widget.teamMember != null
                                    ? widget.teamMember?.isActive
                                    : true,
                              };
                              if (widget.teamMember != null) {
                                try {
                                  bool res = await FBFireStore.teamMember
                                      .doc(widget.teamMember?.docId)
                                      .update(data)
                                      .then((value) => true);
                                  if (res) {
                                    showAppSnackBar(
                                        context, 'Member Details Updated');
                                    context.pop();
                                  } else {
                                    showAppSnackBar(
                                        context, 'Error Updating Member');
                                  }
                                } on Exception catch (e) {
                                  debugPrint(e.toString());
                                }
                              }
                              // teamMember != null
                              //     ? await FBFireStore.teamMember
                              //         .doc(teamMember.docId)
                              //         .update(data)
                              //     : await FBFunctions.ff
                              //         .httpsCallable('createTeamMember')
                              //         .call(data);
                              setState(() {
                                loading = false;
                              });
                              // const snackBar = SnackBar(
                              //   content: Text("Vendor Added"),
                              //   duration: Duration(seconds: 2),
                              // );
                            } catch (e) {
                              debugPrint(e.toString());
                              showAppSnackBar(context, e.toString());
                            }
                          },
                          icon: Icon(
                            CupertinoIcons.checkmark_alt,
                            size: 20,
                          ),
                          label: Text("Add",
                              style: GoogleFonts.livvic(
                                  letterSpacing: 1.3,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500)),
                        ),
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: nameCtrl,
                        decoration: inpDecor().copyWith(labelText: 'Name*'),
                      ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: TextFormField(
                        controller: emailCtrl,
                        decoration: inpDecor()
                            .copyWith(labelText: 'Email*', enabled: false),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 15),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: phoneCtrl,
                        decoration: inpDecor().copyWith(labelText: 'Phone*'),
                      ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: TextFormField(
                        controller: addressCtrl,
                        decoration: inpDecor().copyWith(labelText: 'Address*'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 15),
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 45,
                        child: DropdownButtonHideUnderline(
                          child: DropdownButtonFormField(
                            value: selectedUserType,
                            decoration: inpDecor()
                                .copyWith(labelText: 'Select User Type*'),
                            items: [
                              ...List.generate(
                                userTypeList.length,
                                (index) {
                                  return DropdownMenuItem(
                                      value: userTypeList[index],
                                      child: Text(userTypeList[index]));
                                },
                              )
                            ],
                            onChanged: (value) {
                              selectedUserType = value;
                              setState(() {});
                            },
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    selectedUserType == UserTypes.styler
                        ? Expanded(
                            child: TextFormField(
                              controller: specialityCtrl,
                              // cursorHeight: 20,
                              decoration:
                                  inpDecor().copyWith(labelText: 'Speciality*'),
                            ),
                          )
                        : Expanded(child: SizedBox())
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                        child: InkWell(
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      onTap: () {
                        context.push(
                            '${Routes.teamSessions}/${widget.teamMember?.docId}');
                      },
                      child: Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: dividerColor),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              padding: EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: Color(0xfffef7f6),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                CupertinoIcons.clock,
                                size: 25,
                                color: Color(0xfff5b7a3),
                              ),
                            ),
                            SizedBox(width: 15),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Sessions',
                                  style: TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xff333333)),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  widget.teamMemberSessions.toString(),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff737373),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    )),
                    SizedBox(width: 15),
                    Expanded(
                      child: InkWell(
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          context.push(
                              '${Routes.teamBookings}/${widget.teamMember?.docId}');
                        },
                        child: Container(
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: dividerColor),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                padding: EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: Color(0xfffef7f6),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  CupertinoIcons.calendar,
                                  size: 25,
                                  color: Color(0xfff5b7a3),
                                ),
                              ),
                              SizedBox(width: 15),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Bookings',
                                    style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xff333333)),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    widget.teamMemberBookings.toString(),
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Color(0xff737373),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 15),
                    Spacer(flex: 2),
                    SizedBox(width: 15),
                    Spacer(flex: 2),
                  ],
                )
              ],
            ),
          )
        : const Center(child: Text('No Order Found'));
  }
}
