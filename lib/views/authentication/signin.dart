import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../shared/router.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final providers = [EmailAuthProvider()];
    return Scaffold(
      // backgroundColor: Colors.white,
      body: SignInScreen(
        providers: providers,
        showAuthActionSwitch: false,
        email: '<EMAIL>',
        /*  sideBuilder: (context, constraints) {
          return Container(
              height: double.maxFinite,
              color: Color.fromARGB(255, 254, 241, 224),
              child: Center(
                child: Image.asset(
                  'assets/sudarshan_logo.png',
                  fit: BoxFit.cover,
                ),
              ));
        }, */
        actions: [
          AuthStateChangeAction<SignedIn>((context, state) {
            if (state.user != null) context.go(homeRoute);
          }),
        ],
      ),
    );
  }
}
