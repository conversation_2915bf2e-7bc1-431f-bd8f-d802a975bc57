import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:wedding_super_admin/models/user_model.dart';
import 'package:wedding_super_admin/shared/methods.dart';

import '../models/display_order_details_model.dart';
import '../models/order_model.dart';

Future createOrderPdfAndPrint(
    OrderModel orderData,
    List<DisplayProductsDetails> allOrderedProductDetails,
    UserModel userModel,
    List<ChargeModel> charges,
    [bool download = false]) async {
  // const columnWid = 64.0;
  // print("object1=====");
  // final ByteData bytes = await rootBundle.load('assets/logo.png');
  // print("object2=====");
  // final Uint8List imageBytes = bytes.buffer.asUint8List();
  // print("object3=====");

  // pdf.addPage(MultiPage(
  //   margin: const EdgeInsets.symmetric(horizontal: 30, vertical: 30),
  //   build: (context) {
  //     for (var element in printBunchDataList) {
  //       final data = element;
  //     }

  // // Save the PDF document
  // final Uint8List pdfBytes = await pdf.save();
  // // TODO: Save or display the PDF as needed
  // // if (!true) {

  // if (download) {
  //   await Printing.sharePdf(
  //       bytes: await pdf.save(),
  //       filename: '${DateTime.now().microsecondsSinceEpoch}.pdf');
  //   return;
  //   // await Printing.layoutPdf(
  //   //     onLayout: (format) async => await pdf.save(),
  //   //     name: DateTime.now().microsecondsSinceEpoch.toString());
  // } else {
  //   await Printing.layoutPdf(
  //       onLayout: (PdfPageFormat format) async => pdfBytes);
  // }
  // // }
  // return pdfBytes;

  //   },
  // ));
  final img = await rootBundle.load('assets/logo_small.png');
  final imageBytes = img.buffer.asUint8List();

  final pdf = pw.Document();
  List<pw.ImageProvider> images = [];

  for (var element in allOrderedProductDetails) {
    String imageUrl =
        (element.orderData.orderProductExtraData?.variantImage.isEmpty ?? false)
            ? element.orderData.orderProductExtraData!.variantImage
            : element.variant.images.first;
    images.add(await networkImage(imageUrl));
  }

  pdf.addPage(
    pw.MultiPage(
      margin: const pw.EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      header: (context) {
        return pw.Row(
          children: [
            pw.SizedBox(
              height: 200,
              width: 200,
              child: pw.Image(pw.MemoryImage(imageBytes)),
            ),
            pw.Spacer(),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  'Tax Invoice',
                  style: pw.TextStyle(
                    letterSpacing: .6,
                    fontSize: 15,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.RichText(
                    text: pw.TextSpan(children: [
                  pw.TextSpan(
                    text: 'Order No: ',
                    style: pw.TextStyle(
                      letterSpacing: .8,
                      fontSize: 12,
                      // fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.TextSpan(
                    text: orderData.orderId.toUpperCase(),
                    style: pw.TextStyle(
                      letterSpacing: .8,
                      fontSize: 12,
                      // fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ])),
                pw.SizedBox(height: 2),
                pw.RichText(
                    text: pw.TextSpan(children: [
                  pw.TextSpan(
                    text: 'Order Date: ',
                    style: pw.TextStyle(
                      letterSpacing: .8,
                      fontSize: 12,
                      // fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.TextSpan(
                    text: orderData.createdAt.goodDayDate(),
                    style: pw.TextStyle(
                      letterSpacing: .8,
                      fontSize: 12,
                      // fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ])),
              ],
            ),
          ],
        );
      },
      build: (pw.Context context) => [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.SizedBox(height: 5),
            pw.Center(
              child: pw.Text(
                "Admin Copy",
                style: pw.TextStyle(
                  letterSpacing: .6,
                  fontSize: 15,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            pw.SizedBox(height: 15),
            pw.Divider(color: PdfColors.black, height: 0, thickness: 1),
            pw.SizedBox(height: 15),
            userDetails(userModel, orderData),
            pw.SizedBox(height: 20),
            itemDetails(allOrderedProductDetails, orderData, images),
            pw.SizedBox(height: 20),
            chargesTotalDetails(
                allOrderedProductDetails
                    .map((e) => (e.orderData.qty * e.orderData.sellingPrice))
                    .toList()
                    .fold(
                        0, (previousValue, element) => previousValue + element),
                orderData.charges),
          ],
        ),
      ],
    ),
  );

  // return;

  // Save the PDF document
  final Uint8List pdfBytes = await pdf.save();
  // TODO: Save or display the PDF as needed
  // if (!true) {

  if (download) {
    await Printing.sharePdf(
        bytes: await pdf.save(),
        filename: '${DateTime.now().microsecondsSinceEpoch}.pdf');
    return;
    // await Printing.layoutPdf(
    //     onLayout: (format) async => await pdf.save(),
    //     name: DateTime.now().microsecondsSinceEpoch.toString());
  } else {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfBytes,
    );
  }
  // }
  return pdfBytes;
}

pw.Widget chargesTotalDetails(num subTotal, List<ChargeModel> charges) {
  num chargesTotal = charges
      .map((e) => num.tryParse(e.price.trim().isEmpty ? '0' : e.price.trim()))
      .fold(0, (previousValue, element) => previousValue + element!);
  num totalAmount = chargesTotal + subTotal;
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.start,
    children: [
      pw.Text(
        'Payment Summary',
        style: pw.TextStyle(
          letterSpacing: .5,
          fontSize: 15,
          color: PdfColors.black,
          fontWeight: pw.FontWeight.bold,
        ),
      ),
      pw.SizedBox(height: 10),
      pw.Container(
        padding: pw.EdgeInsets.symmetric(vertical: 5),
        decoration:
            pw.BoxDecoration(border: pw.Border.all(color: PdfColors.black)),
        child: pw.Column(
          children: [
            pw.Row(
              children: [
                pw.SizedBox(width: 10),
                pw.Expanded(
                    flex: 5,
                    child: pw.Text(
                      "Subtotal",
                    )),
                pw.SizedBox(width: 10),
                pw.Align(
                    alignment: pw.Alignment.centerRight,
                    child: pw.Expanded(
                        child: pw.Text(subTotal.toStringAsFixed(2)))),
                pw.SizedBox(width: 10),
              ],
            ),
            pw.Divider(color: PdfColors.black),
            ...List.generate(
              charges.length,
              (index) {
                final charge = charges[index];
                return pw.Column(children: [
                  pw.Row(
                    children: [
                      pw.SizedBox(width: 10),
                      pw.Expanded(
                          flex: 5,
                          child: pw.Text(
                            charge.chargeName,
                          )),
                      pw.SizedBox(width: 10),
                      pw.Align(
                          alignment: pw.Alignment.centerRight,
                          child: pw.Expanded(
                              child: pw.Text(charge.price.trim().isEmpty
                                  ? '0.00'
                                  : (num.tryParse(charge.price) ?? 0)
                                      .toStringAsFixed(2)))),
                      pw.SizedBox(width: 10),
                    ],
                  ),
                  if ((charges.length - 1) != index)
                    pw.Divider(color: PdfColors.black),
                ]);
              },
            ),
            if (charges.isNotEmpty) pw.Divider(color: PdfColors.black),
            pw.Row(
              children: [
                pw.SizedBox(width: 10),
                pw.Expanded(
                    flex: 5,
                    child: pw.Text("Total Amount",
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                        ))),
                pw.SizedBox(width: 10),
                pw.Align(
                  alignment: pw.Alignment.centerRight,
                  child: pw.Expanded(
                      child: pw.Text(totalAmount.toStringAsFixed(2))),
                ),
                pw.SizedBox(width: 10),
              ],
            ),
            pw.SizedBox(height: 5),
          ],
        ),
      ),
    ],
  );
}

pw.Widget itemDetails(List<DisplayProductsDetails> allOrderedProductDetails,
    OrderModel orderData, List<pw.ImageProvider> images) {
  num subTotal = allOrderedProductDetails
      .map((e) => (e.orderData.qty * e.orderData.sellingPrice))
      .toList()
      .fold(0, (previousValue, element) => previousValue + element);
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.start,
    children: [
      pw.Text(
        'Item Details',
        style: pw.TextStyle(
          letterSpacing: .5,
          fontSize: 15,
          color: PdfColors.black,
          fontWeight: pw.FontWeight.bold,
        ),
      ),
      pw.SizedBox(height: 10),
      pw.Container(
        padding: pw.EdgeInsets.symmetric(vertical: 5),
        decoration:
            pw.BoxDecoration(border: pw.Border.all(color: PdfColors.black)),
        child: pw.Column(
          children: [
            pw.Row(
              children: [
                pw.SizedBox(width: 10),
                pw.Container(width: 35, child: pw.Text("Sr No")),
                pw.SizedBox(width: 10),
                pw.Expanded(child: pw.Text("Image")),
                pw.SizedBox(width: 10),
                pw.Expanded(flex: 2, child: pw.Text("Name")),
                pw.SizedBox(width: 10),
                pw.Expanded(child: pw.Text("Qty")),
                // pw.SizedBox(width: 10),
                // pw.Expanded(child: pw.Text("Price")),
                pw.SizedBox(width: 10),
                pw.Expanded(child: pw.Text("Amount")),
                pw.SizedBox(width: 10),
                pw.Expanded(child: pw.Text("Total Amount")),
                pw.SizedBox(width: 10),
              ],
            ),
            pw.Divider(color: PdfColors.black),
            ...List.generate(
              allOrderedProductDetails.length,
              (index) {
                final productData = allOrderedProductDetails[index];
                final image = images[index];
                return pw.Column(children: [
                  pw.Row(
                    children: [
                      pw.SizedBox(width: 10),
                      pw.Container(
                        width: 35,
                        child: pw.Text("${index + 1}",
                            textAlign: pw.TextAlign.center),
                      ),
                      pw.SizedBox(width: 10),
                      pw.Expanded(
                        child: pw.Container(
                          height: 70,
                          width: double.maxFinite,
                          child: pw.Image(image, fit: pw.BoxFit.cover),
                        ),
                      ),
                      pw.SizedBox(width: 10),
                      pw.Expanded(
                        flex: 2,
                        child: pw.Text(
                          '${capilatlizeFirstLetter(productData.product.name)} (${productData.variant.detailTypes.values.join(', ')})',
                        ),
                      ),
                      pw.SizedBox(width: 10),
                      pw.Expanded(
                          child: pw.Text(productData.orderData.qty.toString())),
                      pw.SizedBox(width: 10),
                      // pw.Expanded(
                      //     child: pw.Text(
                      //         productData.orderData.purchasePrice.toString())),
                      // pw.SizedBox(width: 10),
                      pw.Expanded(
                          child: pw.Text(
                              productData.orderData.sellingPrice.toString())),
                      pw.SizedBox(width: 10),
                      pw.Expanded(
                          child: pw.Text((productData.orderData.qty *
                                  productData.orderData.sellingPrice)
                              .toStringAsFixed(2))),
                      pw.SizedBox(width: 10),
                    ],
                  ),
                  if (allOrderedProductDetails.length - 1 != index)
                    pw.Divider(color: PdfColors.black),
                ]);
              },
            ),
            pw.Divider(color: PdfColors.black),
            pw.Row(
              children: [
                pw.SizedBox(width: 10),
                pw.Expanded(
                    flex: 6,
                    child: pw.Text("Subtotal",
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                        ))),
                pw.SizedBox(width: 10),
                pw.Expanded(child: pw.Text(subTotal.toStringAsFixed(2))),
                pw.SizedBox(width: 10),
              ],
            ),
            pw.SizedBox(height: 5),
          ],
        ),
      ),
    ],
  );
}

pw.Widget userDetails(UserModel userModel, OrderModel orderData) {
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.start,
    children: [
      pw.Text(
        'Bill To',
        style: pw.TextStyle(
          letterSpacing: .5,
          fontSize: 13,
          color: PdfColors.black,
          fontWeight: pw.FontWeight.bold,
        ),
      ),
      pw.SizedBox(height: 6),
      pw.Text(
        userModel.name!.trim().isEmpty ? '-' : userModel.name!.trim(),
        style: pw.TextStyle(
          fontSize: 13,
          color: PdfColors.black,
        ),
      ),
      pw.SizedBox(height: 3),
      pw.Text(
        orderData.address ?? '-',
        style: pw.TextStyle(
          fontSize: 13,
          color: PdfColors.black,
        ),
      ),
      pw.SizedBox(height: 3),
      pw.Text(
        userModel.phone ?? '-',
        style: pw.TextStyle(
          fontSize: 13,
          color: PdfColors.black,
        ),
      ),
      pw.SizedBox(height: 3),
      pw.Text(
        userModel.email.trim().isEmpty ? '-' : userModel.email,
        style: pw.TextStyle(
          fontSize: 13,
          color: PdfColors.black,
        ),
      ),
      /*  pw.SizedBox(height: 3),
      pw.RichText(
        text: pw.TextSpan(
          children: [
            pw.TextSpan(
              text: 'Mob :  ',
              style: pw.TextStyle(
                fontSize: 13,
                color: PdfColors.black,
              ),
            ),
            pw.TextSpan(
              text: userModel.phone ?? '-',
              style: pw.TextStyle(
                fontSize: 13,
                color: PdfColors.black,
              ),
            ),
          ],
        ),
      ),
      pw.SizedBox(height: 3),
      pw.RichText(
        text: pw.TextSpan(
          children: [
            pw.TextSpan(
              text: 'Email :  ',
              style: pw.TextStyle(
                fontSize: 13,
                color: PdfColors.black,
              ),
            ),
            pw.TextSpan(
              text: userModel.email.isEmpty ? '-' : userModel.email,
              style: pw.TextStyle(
                fontSize: 13,
                color: PdfColors.black,
              ),
            ),
          ],
        ),
      ),
     */
    ],
  );
}

pw.Widget _headingText(String txt) => pw.Padding(
    padding: const pw.EdgeInsets.symmetric(vertical: 3),
    child: pw.Text(txt,
        textAlign: pw.TextAlign.center,
        style: pw.TextStyle(
          fontSize: 11,
          // fontSize: 13,
          fontWeight: pw.FontWeight.bold,
        )));

pw.Widget _normalText(String txt) => pw.Padding(
    padding: const pw.EdgeInsets.symmetric(vertical: 2),
    child: pw.Text(txt,
        style: const pw.TextStyle(fontSize: 9),
        // style: const TextStyle(fontSize: 11),
        textAlign: pw.TextAlign.center,
        maxLines: 1,
        overflow: pw.TextOverflow.clip));

pw.Widget otherText(String heading, String txt) => pw.RichText(
        text: pw.TextSpan(children: [
      pw.TextSpan(
          text: heading, style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
      pw.TextSpan(text: txt),
    ]));

Future<pw.ImageProvider> networkImage(
  String url, {
  bool cache = true,
  Map<String, String>? headers,
  PdfImageOrientation? orientation,
  double? dpi,
  PdfBaseCache? pdfCache,
}) async {
  pdfCache ??= PdfBaseCache.defaultCache;
  final bytes = await pdfCache.resolve(
    name: url,
    uri: Uri.parse(url),
    cache: cache,
    headers: headers,
  );

  return pw.MemoryImage(bytes, orientation: orientation, dpi: dpi);
}

Future<pw.ImageProvider> assetImage(
  String assetPath, {
  PdfImageOrientation? orientation,
  double? dpi,
}) async {
  final ByteData bytes = await rootBundle.load(assetPath);
  final Uint8List imageBytes = bytes.buffer.asUint8List();

  return pw.MemoryImage(imageBytes, orientation: orientation, dpi: dpi);
}
