import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:wedding_super_admin/models/user_model.dart';
import 'package:wedding_super_admin/views/users/widgets/pagination_button.dart';
import 'package:wedding_super_admin/views/users/widgets/user_table_header.dart';
import 'package:wedding_super_admin/views/users/widgets/user_tile.dart';
import '../../shared/const.dart';
import '../../shared/firebase.dart';
import '../../shared/theme.dart';
import '../common/header_search_feild.dart';
import '../common/top_sections.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends State<UsersPage> {
  int currentPage = 1; // 1-based for user readability
  // int itemsPerPage = 2;
  int totalItems = 0;
  int totalPages = 0;
  final searchController = TextEditingController();
  bool dataloaded = false;

  List<List<UserModel>> paginatedData = [];
  DocumentSnapshot? lastFetchedDoc;
  Timer? debounce;

  @override
  void initState() {
    super.initState();
    initializePagination();
  }

  // NORMAL DATA
  Future<void> initializePagination() async {
    currentPage = 1;
    final userCountSnap = await FBFireStore.users.count().get();
    totalItems = userCountSnap.count ?? 0;
    totalPages = (totalItems / perPageUsers).ceil();
    await fetchPage(1); // fetch first page (1-based)
    dataloaded = true;
    setState(() {});
  }

  // SEARCHED DATA
  getSearchData(String str) async {
    final userCountSnap = await FBFireStore.users
        .where('name', isGreaterThanOrEqualTo: str)
        .where('name', isLessThanOrEqualTo: "$str\uf7ff")
        .count()
        .get();
    totalItems = userCountSnap.count ?? 0;
    totalPages = (totalItems / perPageUsers).ceil();
    if (debounce?.isActive ?? false) debounce?.cancel();
    debounce = Timer(const Duration(milliseconds: 200), () async {
      await fetchPage(1, forSearch: true);
      setState(() {});
    }); // fetch first page (1-based)
    dataloaded = true;
    setState(() {});
  }

  Future<void> fetchPage(int pageIndex, {bool forSearch = false}) async {
    int pageZeroIndex = pageIndex - 1;
    final str = searchController.text.toLowerCase().trim();
    if (pageZeroIndex < paginatedData.length &&
        paginatedData[pageZeroIndex].isNotEmpty) {
      return;
    }

    Query query = forSearch
        ? FBFireStore.users
            .where('name', isGreaterThanOrEqualTo: str)
            .where('name', isLessThanOrEqualTo: "$str\uf7ff")
            .limit(perPageUsers)
        : FBFireStore.users.limit(perPageUsers);

    if (lastFetchedDoc != null) {
      query = query.startAfterDocument(lastFetchedDoc!);
    }

    final snap = await query.get();

    if (snap.docs.isNotEmpty) {
      final users = snap.docs.map((doc) => UserModel.fromSnap(doc)).toList();
      lastFetchedDoc = snap.docs.last;

      while (paginatedData.length <= pageZeroIndex) {
        paginatedData.add([]);
      }
      paginatedData[pageZeroIndex] = users;
    }
  }

  void handlePageChange(int newPage) async {
    dataloaded = false;
    setState(() {});
    await fetchPage(newPage,
        forSearch: searchController.text.trim().isNotEmpty);
    setState(() {
      dataloaded = true;
      currentPage = newPage;
    });
  }

  @override
  Widget build(BuildContext context) {
    final users = (currentPage - 1) < paginatedData.length
        ? paginatedData[currentPage - 1]
        : [];
    return Column(
      children: [
        const TopSectionOfPages(
          pageTile: 'User Management',
          pagesubTile: 'Manage user and their activities',
        ),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                SearchField(
                  searchController: searchController,
                  onChanged: (value) async {
                    // getSearchedData(value);
                    if (debounce?.isActive ?? false) {
                      debounce?.cancel();
                    }
                    debounce =
                        Timer(const Duration(milliseconds: 500), () async {
                      paginatedData.clear();
                      lastFetchedDoc = null;
                      if (value.trim().isNotEmpty) {
                        await getSearchData(value.toLowerCase().trim());
                      } else {
                        initializePagination();
                      }
                    });
                  },
                ),
                const SizedBox(height: 20),
                UserTableHeader(),
                !dataloaded
                    ? Center(
                        child: Column(
                          children: [
                            SizedBox(height: 15),
                            SizedBox(
                              height: 26,
                              width: 26,
                              child: Center(
                                child:
                                    CircularProgressIndicator(strokeWidth: 3),
                              ),
                            ),
                          ],
                        ),
                      )
                    : users.isEmpty
                        ? Container(
                            padding: const EdgeInsets.symmetric(vertical: 15),
                            decoration: const BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(color: dividerColor),
                                    left: BorderSide(color: dividerColor),
                                    right: BorderSide(color: dividerColor)),
                                borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(8),
                                    bottomRight: Radius.circular(8))),
                            child: Center(
                                child: const Text(
                              'No Data Available',
                              style: TextStyle(
                                  color: Color(0xff737373),
                                  fontSize: 14.5,
                                  fontWeight: FontWeight.w500),
                            )))
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ...List.generate(
                                users.length,
                                (index) {
                                  return InkWell(
                                    highlightColor: Colors.transparent,
                                    overlayColor: const WidgetStatePropertyAll(
                                        Colors.transparent),
                                    hoverColor: Colors.transparent,
                                    onTap: () {
                                      // context.push(
                                      //     '${Routes.vendor}/${finalFilteredList[index].docId}');
                                    },
                                    child: UserTile(
                                      index: index,
                                      userModel: users[index],
                                      isLast: index == (users.length - 1),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                if (totalPages > 1)
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: DynamicPagination(
                      currentPage: currentPage,
                      totalPages: totalPages,
                      onPageChanged: handlePageChange,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
