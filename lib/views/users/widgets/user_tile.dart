import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/models/user_model.dart';

import '../../../shared/methods.dart';
import '../../../shared/router.dart';
import '../../../shared/theme.dart';

class UserTile extends StatefulWidget {
  const UserTile(
      {super.key, required this.index, this.userModel, required this.isLast});
  final int index;
  final UserModel? userModel;
  final bool isLast;
  @override
  State<UserTile> createState() => _UserTileState();
}

class _UserTileState extends State<UserTile> {
  @override
  Widget build(BuildContext context) {
    return widget.userModel != null
        ? InkWell(
            splashColor: Colors.transparent,
            hoverColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              context.push('${Routes.user}/${widget.userModel?.docId}');
            },
            child: Container(
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: dividerColor),
                      left: BorderSide(color: dividerColor),
                      right: BorderSide(color: dividerColor)),
                  borderRadius: widget.isLast
                      ? BorderRadius.only(
                          bottomLeft: Radius.circular(8),
                          bottomRight: Radius.circular(8))
                      : null),
              child: Row(
                children: [
                  SizedBox(
                      width: 80,
                      child: Text((widget.index + 1).toString(),
                          textAlign: TextAlign.center)),
                  const SizedBox(width: 5),

                  Expanded(
                      child: Text(capilatlizeFirstLetter(
                          widget.userModel!.name != null &&
                                  widget.userModel!.name!.trim().isNotEmpty
                              ? widget.userModel!.name!
                              : "-"))),
                  const SizedBox(width: 5),

                  // Expanded(child: Text(widget.userModel?.vendorName ?? "")),
                  // const SizedBox(width: 5),

                  // Expanded(flex: 2, child: Text(property?.email ?? "")),
                  Expanded(
                      child: Text(widget.userModel!.phone != null &&
                              widget.userModel!.phone!.trim().isNotEmpty
                          ? widget.userModel!.phone!
                          : "-")),
                  const SizedBox(width: 5),

                  Expanded(
                      child: Text(widget.userModel!.email.trim().isNotEmpty
                          ? widget.userModel!.email
                          : "-")),
                  const SizedBox(width: 5),

                  SizedBox(
                    width: 60,
                    child: IconButton(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      onPressed: () {
                        context
                            .push('${Routes.user}/${widget.userModel?.docId}');
                      },
                      icon: const Icon(
                        Icons.edit,
                        color: themeColor,
                        size: 22,
                      ),
                    ),
                  ),

                  // SizedBox(
                  //   width: 60,
                  //   child: Transform.scale(
                  //     scale: .65,
                  //     child: CupertinoSwitch(
                  //       value: widget.userModel!.isActive,
                  //       onChanged: (value) async {
                  //         await FBFireStore.vendors
                  //             .doc(widget.userModel?.docId)
                  //             .update({'isActive': value});
                  //       },
                  //     ),
                  //   ),
                  // ),
                  // SizedBox(
                  //   width: 60,
                  //   child: IconButton(
                  //     highlightColor: Colors.transparent,
                  //     hoverColor: Colors.transparent,
                  //     onPressed: () {
                  //       /*
                  //       showDialog(
                  //         context: context,
                  //         builder: (context) {
                  //           bool loading = false;
                  //           return StatefulBuilder(
                  //             builder: (context, setState2) {
                  //               return AlertDialog(
                  //                 backgroundColor: Colors.white,
                  //                 surfaceTintColor: Colors.white,
                  //                 title: const Text("Alert"),
                  //                 content: const Text(
                  //                     "Are you sure you want to delete"),
                  //                 actions: loading
                  //                     ? [
                  //                         const Center(
                  //                           child: SizedBox(
                  //                             height: 25,
                  //                             width: 25,
                  //                             child: CircularProgressIndicator(
                  //                               strokeWidth: 2.5,
                  //                             ),
                  //                           ),
                  //                         )
                  //                       ]
                  //                     : [
                  //                         TextButton(
                  //                             onPressed: () async {
                  //                               if (loading) return;

                  //                               try {
                  //                                 setState2(() {
                  //                                   loading = true;
                  //                                 });
                  //                                 HttpsCallableResult<
                  //                                         Map<String, bool>> res =
                  //                                     await FBFunctions.ff
                  //                                         .httpsCallable(
                  //                                             'deleteVendor')
                  //                                         .call({
                  //                                   'uid':
                  //                                       widget.userModel?.docId,
                  //                                 });

                  //                                 if (res.data['success']
                  //                                     as bool) {
                  //                                   const snackBar = SnackBar(
                  //                                       content: Text(
                  //                                           "Vendor deleted successfully"));
                  //                                   if (context.mounted) {
                  //                                     Navigator.of(context).pop();
                  //                                     setState2(() {
                  //                                       loading = false;
                  //                                     });
                  //                                     ScaffoldMessenger.of(
                  //                                             context)
                  //                                         .showSnackBar(snackBar);
                  //                                   }
                  //                                 } else {
                  //                                   const snackBar = SnackBar(
                  //                                       content: Text(
                  //                                           "Something went wrong"));
                  //                                   setState2(() {
                  //                                     loading = false;
                  //                                   });
                  //                                   ScaffoldMessenger.of(context)
                  //                                       .showSnackBar(snackBar);
                  //                                 }
                  //                               } catch (e) {
                  //                                 debugPrint(e.toString());

                  //                                 if (context.mounted) {
                  //                                   setState2(() {
                  //                                     loading = false;
                  //                                   });
                  //                                   Navigator.of(context).pop();
                  //                                 }
                  //                               }
                  //                               // await FBFireStore.vendors
                  //                               //     .doc(widget.userModel?.docId)
                  //                               //     .delete();
                  //                             },
                  //                             child: const Text('Yes')),
                  //                         TextButton(
                  //                             onPressed: () {
                  //                               Navigator.of(context).pop();
                  //                             },
                  //                             child: const Text('No')),
                  //                       ],
                  //               );
                  //             },
                  //           );
                  //         },
                  //       );
                  //      */
                  //     },
                  //     icon: const Icon(Icons.delete,
                  //         // color: Colors.redAccent,
                  //         color: themeColor),
                  //   ),
                  // ),
                ],
              ),
            ),
          )
        : const SizedBox();
  }
}
