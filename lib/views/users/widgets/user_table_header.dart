import 'package:flutter/material.dart';

import '../../../shared/theme.dart';
import '../../common/table_header.dart';

class UserTableHeader extends StatelessWidget {
  const UserTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 3),
      decoration: BoxDecoration(
          color: dashboardSelectedColor,
          border: Border.all(color: dividerColor),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8))
          // color: const Color.fromARGB(255, 228, 228, 228),
          // color: themeColor,
          // color: const Color.fromARGB(255, 177, 139, 86),
          // color: Colors.white70,
          // color: tableHeaderColor,
          // borderRadius: BorderRadius.circular(4),
          // border: Border.symmetric(
          //     horizontal: BorderSide(color: Colors.grey.shade300, width: 1)),
          // border: Border.all(color: Colors.grey.shade300, width: 1),

          // borderRadius: BorderRadius.circular(4),
          ),
      child: Row(
        children: [
          const SizedBox(
            width: 80,
            child: Text(
              'Sr No',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                letterSpacing: 1.2,
              ),
            ),
          ),
          // const SizedBox(width: 5),

          // const Expanded(child: TableHeaderText(headerName: 'Added on')),
          const SizedBox(width: 5),

          const Expanded(child: TableHeaderText(headerName: 'Name')),
          const SizedBox(width: 5),

          // const Expanded(child: TableHeaderText(headerName: 'Onwer Name')),
          // const SizedBox(width: 5),

          const Expanded(child: TableHeaderText(headerName: 'Contact')),
          const SizedBox(width: 5),

          const Expanded(child: TableHeaderText(headerName: 'Email')),
          const SizedBox(width: 5),

          Opacity(
            opacity: 0,
            child: SizedBox(
              width: 60,
              child: IconButton(
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onPressed: () {},
                icon: const Icon(
                  Icons.edit,
                  size: 22,
                ),
              ),
            ),
          ),
          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //       width: 60,
          //       child: Transform.scale(
          //         scale: .65,
          //         child: CupertinoSwitch(
          //           value: true,
          //           onChanged: (value) {},
          //         ),
          //       )),
          // ),
          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //     width: 60,
          //     child: IconButton(
          //       highlightColor: Colors.transparent,
          //       hoverColor: Colors.transparent,
          //       onPressed: () {},
          //       icon: const Icon(
          //         Icons.delete,
          //         color: themeColor,
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
