import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_date_range_picker/flutter_date_range_picker.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/bookings_model.dart';
import 'package:wedding_super_admin/models/order_model.dart';
import 'package:wedding_super_admin/models/session_model.dart';
import 'package:wedding_super_admin/views/common/header_search_feild.dart';

import '../../../models/user_model.dart';
import '../../../shared/const.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';
import '../../common/table_header.dart';

class UserDeliveryTabView extends StatefulWidget {
  const UserDeliveryTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserDeliveryTabView> createState() => _UserDeliveryTabViewState();
}

class _UserDeliveryTabViewState extends State<UserDeliveryTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? todaysDeliveryStream;
  final searchCtrl = TextEditingController();
  List<DeliveryDetailsModel> filteredDeliveryList = [];
  List<DeliveryDetailsModel> dropdownfilteredDeliveryList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todayDeliveryStream();
    selectedDropdown = historyDropdownList[0];
  }

  todayDeliveryStream() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysDeliveryStream?.cancel();
      todaysDeliveryStream = FBFireStore.deliveryDetails
          .where('uId', isEqualTo: widget.userDocId)
          .where('dispatchedOn',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('dispatchedOn',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredDeliveryList.clear();
        filteredDeliveryList
            .addAll(event.docs.map((e) => DeliveryDetailsModel.fromSnap(e)));
        filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredDeliveryList.clear();
        dropdownfilteredDeliveryList.addAll(filteredDeliveryList);
        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyDeliveryStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyDeliverySnap = await FBFireStore.deliveryDetails
          .where('uId', isEqualTo: widget.userDocId)
          .where('dispatchedOn',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('dispatchedOn',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredDeliveryList.clear();
      filteredDeliveryList.addAll(
          weeklyDeliverySnap.docs.map((e) => DeliveryDetailsModel.fromSnap(e)));
      filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredDeliveryList.clear();
      dropdownfilteredDeliveryList.addAll(filteredDeliveryList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyDeliveryStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyTransactionSnap = await FBFireStore.deliveryDetails
          .where('uId', isEqualTo: widget.userDocId)
          .where('dispatchedOn',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('dispatchedOn',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredDeliveryList.clear();
      filteredDeliveryList.addAll(monthlyTransactionSnap.docs
          .map((e) => DeliveryDetailsModel.fromSnap(e)));
      filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredDeliveryList.clear();
      dropdownfilteredDeliveryList.addAll(filteredDeliveryList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customDeliveryStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customTransactionSnap = await FBFireStore.deliveryDetails
          .where('uId', isEqualTo: widget.userDocId)
          .where('dispatchedOn',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('dispatchedOn',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredDeliveryList.clear();
      filteredDeliveryList.addAll(customTransactionSnap.docs
          .map((e) => DeliveryDetailsModel.fromSnap(e)));
      filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredDeliveryList.clear();
      dropdownfilteredDeliveryList.addAll(filteredDeliveryList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredDeliveryList.clear();
    if (str.isEmpty) {
      filteredDeliveryList.addAll(dropdownfilteredDeliveryList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    todaysDeliveryStream?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                  width: 400,
                  child: SearchField(
                    searchController: searchCtrl,
                    onChanged: (value) {
                      getSearchedData(value.trim());
                    },
                  )),
              SizedBox(width: 20),
              SizedBox(
                width: 150,
                child: DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                  value: selectedDropdown,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                  ),
                  items: historyDropdownList
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  onChanged: (value) async {
                    selectedDropdown = value;
                    searchCtrl.clear();
                    if (value == historyDropdownList[0]) {
                      todayDeliveryStream();
                    } else if (value == historyDropdownList[1]) {
                      weeklyDeliveryStream();
                    } else if (value == historyDropdownList[2]) {
                      monthlyDeliveryStream();
                    } else if (value == historyDropdownList[3]) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          DateRange? selectedDateRange;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                content: SizedBox(
                                    height: 400,
                                    width: 600,
                                    child: DateRangePickerWidget(
                                      minDate: DateTime(2010),
                                      maxDate: DateTime.now(),
                                      onDateRangeChanged: (value) {
                                        selectedDateRange = value;
                                        setState2(() {});
                                      },
                                    )),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      // print("Range confirmed: $_selectedRange");
                                      if (selectedDateRange != null) {
                                        selEndDate = selectedDateRange!.end;
                                        selstartDate = selectedDateRange!.start;
                                        customDeliveryStream();
                                      }
                                      Navigator.of(context).pop();
                                    },
                                    child: Text("Confirm"),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      );
                      //   final res = await showDateRangePicker(
                      //       context: context,
                      //       firstDate: DateTime(2010),
                      //       lastDate: DateTime.now());

                      //   if (res != null) {
                      //     selstartDate = res.start;
                      //     selEndDate = res.end;
                      //     customTransactionsStream();
                      //   }
                    }
                  },
                )),
              ),
              if (selectedDropdown == historyDropdownList[3]) ...[
                SizedBox(width: 20),
                InkWell(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date Range',
                        style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: Color.fromARGB(255, 92, 92, 92)),
                      ),
                      Text(
                        '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
              ]
            ],
          ),
          SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
                color: dashboardSelectedColor,
                border: Border.all(color: dividerColor),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8), topRight: Radius.circular(8))

                // color: const Color.fromARGB(255, 228, 228, 228),
                // color: themeColor,
                // color: const Color.fromARGB(255, 177, 139, 86),
                // color: tableHeaderColor,
                // borderRadius: BorderRadius.circular(4),
                ),
            child: Row(
              children: [
                const SizedBox(
                  width: 80,
                  child: Text(
                    'Sr No',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Dispatch Date')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Delivery Partner')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Charge')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Order ID')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Items')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Tracking ID')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Status')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Delivered On')),
                Opacity(
                  opacity: 0,
                  child: IgnorePointer(
                    ignoring: true,
                    child: SizedBox(
                      width: 60,
                      child: IconButton(
                        highlightColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        onPressed: () {},
                        icon: const Icon(
                          Icons.delete,
                          color: themeColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (filteredDeliveryList.isEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: dividerColor),
                      left: BorderSide(color: dividerColor),
                      right: BorderSide(color: dividerColor)),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8))),
              child: const Center(
                child: Text(
                  'No Data Available',
                  style: TextStyle(
                      color: Color(0xff737373),
                      fontSize: 14.5,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ...List.generate(
            filteredDeliveryList.length,
            (index) {
              final isLast = index == filteredDeliveryList.length - 1;
              final delivery = filteredDeliveryList[index];
              return Container(
                decoration: BoxDecoration(
                    border: const Border(
                        bottom: BorderSide(color: dividerColor),
                        left: BorderSide(color: dividerColor),
                        right: BorderSide(color: dividerColor)),
                    borderRadius: isLast
                        ? const BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8))
                        : null),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text((index + 1).toString(),
                          textAlign: TextAlign.center),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '${delivery.dispatchedOn?.goodDayDate() ?? '-'}  ${delivery.dispatchedOn?.goodTime() ?? ''}')),

                    const SizedBox(width: 5),
                    Expanded(child: Text(delivery.deliveryPartner)),
                    const SizedBox(width: 5),
                    Expanded(child: Text(delivery.charges.toStringAsFixed(2))),

                    const SizedBox(width: 5),
                    Expanded(child: Text(delivery.orderId)),
                    const SizedBox(width: 5),
                    Expanded(
                        child:
                            Text(delivery.dispatchedProduct.length.toString())),
                    const SizedBox(width: 5),
                    Expanded(child: Text(delivery.trackingId)),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(delivery.isDelivered
                            ? 'Delivered'
                            : 'Out for Delivery')),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(delivery.deliveredOn != null
                            ? '${delivery.deliveredOn?.goodDayDate()}  ${delivery.deliveredOn?.goodTime()}'
                            : '-')),

                    // Opacity(
                    //   opacity: 0,
                    //   child: IgnorePointer(
                    //     ignoring: true,
                    //     child: SizedBox(
                    //       width: 60,
                    //       child: IconButton(
                    //         highlightColor: Colors.transparent,
                    //         hoverColor: Colors.transparent,
                    //         onPressed: () {},
                    //         icon: const Icon(
                    //           Icons.edit,
                    //           size: 22,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    Opacity(
                      opacity: 0,
                      child: SizedBox(
                        width: 60,
                        child: IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onPressed: null,
                          icon: const Icon(
                            Icons.delete,
                            color: themeColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          )
        ],
      ),
    );
  }
}

class UserTransactionTabView extends StatefulWidget {
  const UserTransactionTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserTransactionTabView> createState() => _UserTransactionTabViewState();
}

class _UserTransactionTabViewState extends State<UserTransactionTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      todaysTransactionStream;
  final searchCtrl = TextEditingController();
  List<TransactionModel> filteredTransactionList = [];
  List<TransactionModel> dropdownfilteredTransactionList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todaysTransactionsStream();
    selectedDropdown = historyDropdownList[0];
  }

  todaysTransactionsStream() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysTransactionStream?.cancel();
      todaysTransactionStream = FBFireStore.transaction
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredTransactionList.clear();
        filteredTransactionList
            .addAll(event.docs.map((e) => TransactionModel.fromSnap(e)));
        filteredTransactionList
            .sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredTransactionList.clear();
        dropdownfilteredTransactionList.addAll(filteredTransactionList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyTransactionsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyTransactionSnap = await FBFireStore.transaction
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredTransactionList.clear();
      filteredTransactionList.addAll(
          weeklyTransactionSnap.docs.map((e) => TransactionModel.fromSnap(e)));
      filteredTransactionList
          .sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredTransactionList.clear();
      dropdownfilteredTransactionList.addAll(filteredTransactionList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyTransactionsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyTransactionSnap = await FBFireStore.transaction
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredTransactionList.clear();
      filteredTransactionList.addAll(
          monthlyTransactionSnap.docs.map((e) => TransactionModel.fromSnap(e)));
      filteredTransactionList
          .sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredTransactionList.clear();
      dropdownfilteredTransactionList.addAll(filteredTransactionList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customTransactionsStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customTransactionSnap = await FBFireStore.transaction
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredTransactionList.clear();
      filteredTransactionList.addAll(
          customTransactionSnap.docs.map((e) => TransactionModel.fromSnap(e)));
      filteredTransactionList
          .sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredTransactionList.clear();
      dropdownfilteredTransactionList.addAll(filteredTransactionList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredTransactionList.clear();
    if (str.isEmpty) {
      filteredTransactionList.addAll(dropdownfilteredTransactionList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredTransactionList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    todaysTransactionStream?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                  width: 400,
                  child: SearchField(
                    searchController: searchCtrl,
                    onChanged: (value) {
                      getSearchedData(value.trim());
                    },
                  )),
              SizedBox(width: 20),
              SizedBox(
                width: 150,
                child: DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                  value: selectedDropdown,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                  ),
                  items: historyDropdownList
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  onChanged: (value) async {
                    selectedDropdown = value;
                    searchCtrl.clear();
                    if (value == historyDropdownList[0]) {
                      todaysTransactionsStream();
                    } else if (value == historyDropdownList[1]) {
                      weeklyTransactionsStream();
                    } else if (value == historyDropdownList[2]) {
                      monthlyTransactionsStream();
                    } else if (value == historyDropdownList[3]) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          DateRange? selectedDateRange;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                content: SizedBox(
                                    height: 400,
                                    width: 600,
                                    child: DateRangePickerWidget(
                                      minDate: DateTime(2010),
                                      maxDate: DateTime.now(),
                                      onDateRangeChanged: (value) {
                                        selectedDateRange = value;
                                        setState2(() {});
                                      },
                                    )),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      // print("Range confirmed: $_selectedRange");
                                      if (selectedDateRange != null) {
                                        selEndDate = selectedDateRange!.end;
                                        selstartDate = selectedDateRange!.start;
                                        customTransactionsStream();
                                      }
                                      Navigator.of(context).pop();
                                    },
                                    child: Text("Confirm"),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      );
                      //   final res = await showDateRangePicker(
                      //       context: context,
                      //       firstDate: DateTime(2010),
                      //       lastDate: DateTime.now());

                      //   if (res != null) {
                      //     selstartDate = res.start;
                      //     selEndDate = res.end;
                      //     customTransactionsStream();
                      //   }
                    }
                  },
                )),
              ),
              if (selectedDropdown == historyDropdownList[3]) ...[
                SizedBox(width: 20),
                InkWell(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date Range',
                        style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: Color.fromARGB(255, 92, 92, 92)),
                      ),
                      Text(
                        '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
              ]
            ],
          ),
          SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
                color: dashboardSelectedColor,
                border: Border.all(color: dividerColor),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8), topRight: Radius.circular(8))

                // color: const Color.fromARGB(255, 228, 228, 228),
                // color: themeColor,
                // color: const Color.fromARGB(255, 177, 139, 86),
                // color: tableHeaderColor,
                // borderRadius: BorderRadius.circular(4),
                ),
            child: Row(
              children: [
                const SizedBox(
                  width: 80,
                  child: Text(
                    'Sr No',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Date')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Amount')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Method')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Status')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Transaction ID')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Paid On')),
                Opacity(
                  opacity: 0,
                  child: IgnorePointer(
                    ignoring: true,
                    child: SizedBox(
                      width: 60,
                      child: IconButton(
                        highlightColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        onPressed: () {},
                        icon: const Icon(
                          Icons.delete,
                          color: themeColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (filteredTransactionList.isEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: dividerColor),
                      left: BorderSide(color: dividerColor),
                      right: BorderSide(color: dividerColor)),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8))),
              child: const Center(
                child: Text(
                  'No Data Available',
                  style: TextStyle(
                      color: Color(0xff737373),
                      fontSize: 14.5,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ...List.generate(
            filteredTransactionList.length,
            (index) {
              final isLast = index == filteredTransactionList.length - 1;
              final transacton = filteredTransactionList[index];
              return Container(
                decoration: BoxDecoration(
                    border: const Border(
                        bottom: BorderSide(color: dividerColor),
                        left: BorderSide(color: dividerColor),
                        right: BorderSide(color: dividerColor)),
                    borderRadius: isLast
                        ? const BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8))
                        : null),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text((index + 1).toString(),
                          textAlign: TextAlign.center),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '${transacton.createdAt.goodDayDate()}  ${transacton.createdAt.goodTime()}')),

                    const SizedBox(width: 5),
                    Expanded(child: Text(transacton.amount.toStringAsFixed(2))),

                    const SizedBox(width: 5),
                    Expanded(child: Text(transacton.method ?? '-')),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(transacton.isPaid ? 'Paid' : 'Requested')),
                    const SizedBox(width: 5),
                    Expanded(child: Text(transacton.transactionId ?? '-')),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(transacton.paymentTime != null
                            ? '${transacton.paymentTime?.goodDayDate()}  ${transacton.paymentTime?.goodTime()}'
                            : '-')),

                    // Opacity(
                    //   opacity: 0,
                    //   child: IgnorePointer(
                    //     ignoring: true,
                    //     child: SizedBox(
                    //       width: 60,
                    //       child: IconButton(
                    //         highlightColor: Colors.transparent,
                    //         hoverColor: Colors.transparent,
                    //         onPressed: () {},
                    //         icon: const Icon(
                    //           Icons.edit,
                    //           size: 22,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    Opacity(
                      opacity: 0,
                      child: SizedBox(
                        width: 60,
                        child: IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onPressed: null,
                          icon: const Icon(
                            Icons.delete,
                            color: themeColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          )
        ],
      ),
    );
  }
}

class UserBookingsTabView extends StatefulWidget {
  const UserBookingsTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserBookingsTabView> createState() => _UserBookingsTabViewState();
}

class _UserBookingsTabViewState extends State<UserBookingsTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? todaysBookingsStream;
  final searchCtrl = TextEditingController();
  List<BookingsModel> filteredBookingList = [];
  List<BookingsModel> dropdownfilteredBookingList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todaysBookings();
    selectedDropdown = historyDropdownList[0];
  }

  todaysBookings() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysBookingsStream?.cancel();
      todaysBookingsStream = FBFireStore.bookings
          .where('uId', isEqualTo: widget.userDocId)
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredBookingList.clear();
        filteredBookingList
            .addAll(event.docs.map((e) => BookingsModel.fromSnap(e)));
        filteredBookingList
            .sort((a, b) => b.bookingDate.compareTo(a.bookingDate));
        dropdownfilteredBookingList.clear();
        dropdownfilteredBookingList.addAll(filteredBookingList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyBookingsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyBookingsSnap = await FBFireStore.bookings
          .where('uId', isEqualTo: widget.userDocId)
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredBookingList.clear();
      filteredBookingList.addAll(
          weeklyBookingsSnap.docs.map((e) => BookingsModel.fromSnap(e)));
      filteredBookingList
          .sort((a, b) => b.bookingDate.compareTo(a.bookingDate));
      dropdownfilteredBookingList.clear();
      dropdownfilteredBookingList.addAll(filteredBookingList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyBookingsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyBookingsSnap = await FBFireStore.bookings
          .where('uId', isEqualTo: widget.userDocId)
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredBookingList.clear();
      filteredBookingList.addAll(
          monthlyBookingsSnap.docs.map((e) => BookingsModel.fromSnap(e)));
      filteredBookingList
          .sort((a, b) => b.bookingDate.compareTo(a.bookingDate));
      dropdownfilteredBookingList.clear();
      dropdownfilteredBookingList.addAll(filteredBookingList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customBookingsStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customBookingsSnap = await FBFireStore.bookings
          .where('uId', isEqualTo: widget.userDocId)
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();

      filteredBookingList.clear();
      filteredBookingList.addAll(
          customBookingsSnap.docs.map((e) => BookingsModel.fromSnap(e)));
      filteredBookingList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredBookingList.clear();
      dropdownfilteredBookingList.addAll(filteredBookingList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredBookingList.clear();
    if (str.isEmpty) {
      filteredBookingList.addAll(dropdownfilteredBookingList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredBookingList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        children: [
          SizedBox(
              width: 400,
              child: SearchField(
                searchController: searchCtrl,
                onChanged: (value) {
                  getSearchedData(value.trim());
                },
              )),
          SizedBox(width: 20),
          SizedBox(
            width: 150,
            child: DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
              value: selectedDropdown,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
                enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
                focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
              ),
              items: historyDropdownList
                  .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                  .toList(),
              onChanged: (value) async {
                selectedDropdown = value;
                searchCtrl.clear();
                if (value == historyDropdownList[0]) {
                  todaysBookings();
                } else if (value == historyDropdownList[1]) {
                  weeklyBookingsStream();
                } else if (value == historyDropdownList[2]) {
                  monthlyBookingsStream();
                } else if (value == historyDropdownList[3]) {
                  showDialog(
                    context: context,
                    builder: (context) {
                      DateRange? selectedDateRange;
                      return StatefulBuilder(
                        builder: (context, setState2) {
                          return AlertDialog(
                            backgroundColor: Colors.white,
                            surfaceTintColor: Colors.white,
                            content: SizedBox(
                                height: 400,
                                width: 600,
                                child: DateRangePickerWidget(
                                  minDate: DateTime(2010),
                                  maxDate: DateTime.now(),
                                  onDateRangeChanged: (value) {
                                    selectedDateRange = value;
                                    setState2(() {});
                                  },
                                )),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  // print("Range confirmed: $_selectedRange");
                                  if (selectedDateRange != null) {
                                    selEndDate = selectedDateRange!.end;
                                    selstartDate = selectedDateRange!.start;
                                    customBookingsStream();
                                  }
                                  Navigator.of(context).pop();
                                },
                                child: Text("Confirm"),
                              ),
                            ],
                          );
                        },
                      );
                    },
                  );
                }
              },
            )),
          ),
          if (selectedDropdown == historyDropdownList[3]) ...[
            SizedBox(width: 20),
            InkWell(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Date Range',
                    style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                        color: Color.fromARGB(255, 92, 92, 92)),
                  ),
                  Text(
                    '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  )
                ],
              ),
            ),
          ]
        ],
      ),
      SizedBox(height: 20),
      Container(
        decoration: BoxDecoration(
            color: dashboardSelectedColor,
            border: Border.all(color: dividerColor),
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8), topRight: Radius.circular(8))

            // color: const Color.fromARGB(255, 228, 228, 228),
            // color: themeColor,
            // color: const Color.fromARGB(255, 177, 139, 86),
            // color: tableHeaderColor,
            // borderRadius: BorderRadius.circular(4),
            ),
        child: Row(
          children: [
            const SizedBox(
              width: 80,
              child: Text(
                'Sr No',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
            ),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Created At')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Booking Date')),
            const SizedBox(width: 5),
            const Expanded(
                child: TableHeaderText(headerName: 'Slot Time & Gap')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Consultant')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Styler')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Status')),
            // const SizedBox(width: 5),
            // const Expanded(child: TableHeaderText(headerName: 'Completed On')),
            Opacity(
              opacity: 0,
              child: IgnorePointer(
                ignoring: true,
                child: SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {},
                    icon: const Icon(
                      Icons.delete,
                      color: themeColor,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      if (filteredBookingList.isEmpty)
        Container(
          padding: const EdgeInsets.symmetric(vertical: 15),
          decoration: const BoxDecoration(
              border: Border(
                  bottom: BorderSide(color: dividerColor),
                  left: BorderSide(color: dividerColor),
                  right: BorderSide(color: dividerColor)),
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8))),
          child: const Center(
            child: Text(
              'No Data Available',
              style: TextStyle(
                  color: Color(0xff737373),
                  fontSize: 14.5,
                  fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ...List.generate(
        filteredBookingList.length,
        (index) {
          final isLast = index == filteredBookingList.length - 1;
          final bookingData = filteredBookingList[index];
          final teamCtrl = Get.find<HomeCtrl>().teamMembers;
          final consultant = teamCtrl.firstWhereOrNull(
              (element) => element.docId == bookingData.teamMemberId);
          final styler = teamCtrl.firstWhereOrNull(
              (element) => element.docId == bookingData.stylerId);
          return Container(
            decoration: BoxDecoration(
                border: const Border(
                    bottom: BorderSide(color: dividerColor),
                    left: BorderSide(color: dividerColor),
                    right: BorderSide(color: dividerColor)),
                borderRadius: isLast
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8))
                    : null),
            child: Row(
              children: [
                SizedBox(
                  width: 80,
                  child:
                      Text((index + 1).toString(), textAlign: TextAlign.center),
                ),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                        '${bookingData.createdAt.goodDayDate()}  ${bookingData.createdAt.goodTime()}')),

                const SizedBox(width: 5),
                Expanded(child: Text(bookingData.bookingDate.goodDayDate())),

                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                        '${bookingData.bookingstartTime.goodTime()} - ${bookingData.bookingendTime.goodTime()}, ${bookingData.slotgap}')),
                const SizedBox(width: 5),
                Expanded(
                    child:
                        Text(capilatlizeFirstLetter(consultant?.name ?? '-'))),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(capilatlizeFirstLetter(styler?.name ?? '-'))),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(bookingData.cancelledAt != null
                        ? 'Cancelled'
                        : bookingData.completedAt != null
                            ? 'Completed'
                            : 'pending')),
                // const SizedBox(width: 5),
                // Expanded(
                //     child: Text(inquiry.endedOn != null
                //         ? '${inquiry.endedOn?.goodDayDate()}  ${inquiry.endedOn?.goodTime()}'
                //         : '-')),

                // Opacity(
                //   opacity: 0,
                //   child: IgnorePointer(
                //     ignoring: true,
                //     child: SizedBox(
                //       width: 60,
                //       child: IconButton(
                //         highlightColor: Colors.transparent,
                //         hoverColor: Colors.transparent,
                //         onPressed: () {},
                //         icon: const Icon(
                //           Icons.edit,
                //           size: 22,
                //         ),
                //       ),
                //     ),
                //   ),
                // ),
                Opacity(
                  opacity: 0,
                  child: SizedBox(
                    width: 60,
                    child: IconButton(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      onPressed: null,
                      icon: const Icon(
                        Icons.delete,
                        color: themeColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      )
    ]));
  }
}

class UserInquiriesTabView extends StatefulWidget {
  const UserInquiriesTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserInquiriesTabView> createState() => _UserInquiriesTabViewState();
}

class _UserInquiriesTabViewState extends State<UserInquiriesTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      todaysInquiriesStream;
  final searchCtrl = TextEditingController();
  List<SessionModel> filteredInquiriesList = [];
  List<SessionModel> dropdownfilteredInquiriesList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todaysInquiries();
    selectedDropdown = historyDropdownList[0];
  }

  todaysInquiries() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysInquiriesStream?.cancel();
      todaysInquiriesStream = FBFireStore.sessions
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredInquiriesList.clear();
        filteredInquiriesList
            .addAll(event.docs.map((e) => SessionModel.fromSnap(e)));
        filteredInquiriesList
            .sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredInquiriesList.clear();
        dropdownfilteredInquiriesList.addAll(filteredInquiriesList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyInquiriesStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyInquiriesSnap = await FBFireStore.sessions
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredInquiriesList.clear();
      filteredInquiriesList.addAll(
          weeklyInquiriesSnap.docs.map((e) => SessionModel.fromSnap(e)));
      filteredInquiriesList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredInquiriesList.clear();
      dropdownfilteredInquiriesList.addAll(filteredInquiriesList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyInquiriesStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyInquiriesSnap = await FBFireStore.sessions
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredInquiriesList.clear();
      filteredInquiriesList.addAll(
          monthlyInquiriesSnap.docs.map((e) => SessionModel.fromSnap(e)));
      filteredInquiriesList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredInquiriesList.clear();
      dropdownfilteredInquiriesList.addAll(filteredInquiriesList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customInquiriesStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customInquiriesSnap = await FBFireStore.sessions
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredInquiriesList.clear();
      filteredInquiriesList.addAll(
          customInquiriesSnap.docs.map((e) => SessionModel.fromSnap(e)));
      filteredInquiriesList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredInquiriesList.clear();
      dropdownfilteredInquiriesList.addAll(filteredInquiriesList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredInquiriesList.clear();
    if (str.isEmpty) {
      filteredInquiriesList.addAll(dropdownfilteredInquiriesList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredInquiriesList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        children: [
          SizedBox(
              width: 400,
              child: SearchField(
                searchController: searchCtrl,
                onChanged: (value) {
                  getSearchedData(value.trim());
                },
              )),
          SizedBox(width: 20),
          SizedBox(
            width: 150,
            child: DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
              value: selectedDropdown,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
                enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
                focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
              ),
              items: historyDropdownList
                  .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                  .toList(),
              onChanged: (value) async {
                selectedDropdown = value;
                searchCtrl.clear();
                if (value == historyDropdownList[0]) {
                  todaysInquiries();
                } else if (value == historyDropdownList[1]) {
                  weeklyInquiriesStream();
                } else if (value == historyDropdownList[2]) {
                  monthlyInquiriesStream();
                } else if (value == historyDropdownList[3]) {
                  showDialog(
                    context: context,
                    builder: (context) {
                      DateRange? selectedDateRange;
                      return StatefulBuilder(
                        builder: (context, setState2) {
                          return AlertDialog(
                            backgroundColor: Colors.white,
                            surfaceTintColor: Colors.white,
                            content: SizedBox(
                                height: 400,
                                width: 600,
                                child: DateRangePickerWidget(
                                  minDate: DateTime(2010),
                                  maxDate: DateTime.now(),
                                  onDateRangeChanged: (value) {
                                    selectedDateRange = value;
                                    setState2(() {});
                                  },
                                )),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  // print("Range confirmed: $_selectedRange");
                                  if (selectedDateRange != null) {
                                    selEndDate = selectedDateRange!.end;
                                    selstartDate = selectedDateRange!.start;
                                    customInquiriesStream();
                                  }
                                  Navigator.of(context).pop();
                                },
                                child: Text("Confirm"),
                              ),
                            ],
                          );
                        },
                      );
                    },
                  );
                }
              },
            )),
          ),
          if (selectedDropdown == historyDropdownList[3]) ...[
            SizedBox(width: 20),
            InkWell(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Date Range',
                    style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                        color: Color.fromARGB(255, 92, 92, 92)),
                  ),
                  Text(
                    '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  )
                ],
              ),
            ),
          ]
        ],
      ),
      SizedBox(height: 20),
      Container(
        decoration: BoxDecoration(
            color: dashboardSelectedColor,
            border: Border.all(color: dividerColor),
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8), topRight: Radius.circular(8))

            // color: const Color.fromARGB(255, 228, 228, 228),
            // color: themeColor,
            // color: const Color.fromARGB(255, 177, 139, 86),
            // color: tableHeaderColor,
            // borderRadius: BorderRadius.circular(4),
            ),
        child: Row(
          children: [
            const SizedBox(
              width: 80,
              child: Text(
                'Sr No',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
            ),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Created At')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Inquiry ID')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Consultant')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Styler')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Status')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Completed On')),
            Opacity(
              opacity: 0,
              child: IgnorePointer(
                ignoring: true,
                child: SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {},
                    icon: const Icon(
                      Icons.delete,
                      color: themeColor,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      if (filteredInquiriesList.isEmpty)
        Container(
          padding: const EdgeInsets.symmetric(vertical: 15),
          decoration: const BoxDecoration(
              border: Border(
                  bottom: BorderSide(color: dividerColor),
                  left: BorderSide(color: dividerColor),
                  right: BorderSide(color: dividerColor)),
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8))),
          child: const Center(
            child: Text(
              'No Data Available',
              style: TextStyle(
                  color: Color(0xff737373),
                  fontSize: 14.5,
                  fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ...List.generate(
        filteredInquiriesList.length,
        (index) {
          final isLast = index == filteredInquiriesList.length - 1;
          final inquiry = filteredInquiriesList[index];
          final teamCtrl = Get.find<HomeCtrl>().teamMembers;
          final teamMember = teamCtrl.firstWhereOrNull(
              (element) => element.docId == inquiry.consultantId);
          final styler = teamCtrl
              .firstWhereOrNull((element) => element.docId == inquiry.stylerId);
          return Container(
            decoration: BoxDecoration(
                border: const Border(
                    bottom: BorderSide(color: dividerColor),
                    left: BorderSide(color: dividerColor),
                    right: BorderSide(color: dividerColor)),
                borderRadius: isLast
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8))
                    : null),
            child: Row(
              children: [
                SizedBox(
                  width: 80,
                  child:
                      Text((index + 1).toString(), textAlign: TextAlign.center),
                ),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                        '${inquiry.createdAt.goodDayDate()}  ${inquiry.createdAt.goodTime()}')),

                const SizedBox(width: 5),
                Expanded(child: Text(inquiry.inquiryId)),

                const SizedBox(width: 5),
                Expanded(
                    child:
                        Text(capilatlizeFirstLetter(teamMember?.name ?? '-'))),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(capilatlizeFirstLetter(styler?.name ?? '-'))),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(inquiry.isActive ? 'Pending' : 'Completed')),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(inquiry.endedOn != null
                        ? '${inquiry.endedOn?.goodDayDate()}  ${inquiry.endedOn?.goodTime()}'
                        : '-')),

                // Opacity(
                //   opacity: 0,
                //   child: IgnorePointer(
                //     ignoring: true,
                //     child: SizedBox(
                //       width: 60,
                //       child: IconButton(
                //         highlightColor: Colors.transparent,
                //         hoverColor: Colors.transparent,
                //         onPressed: () {},
                //         icon: const Icon(
                //           Icons.edit,
                //           size: 22,
                //         ),
                //       ),
                //     ),
                //   ),
                // ),
                Opacity(
                  opacity: 0,
                  child: SizedBox(
                    width: 60,
                    child: IconButton(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      onPressed: null,
                      icon: const Icon(
                        Icons.delete,
                        color: themeColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      )
    ]));
  }
}

class UserOrdersTabView extends StatefulWidget {
  const UserOrdersTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserOrdersTabView> createState() => _UserOrdersTabViewState();
}

class _UserOrdersTabViewState extends State<UserOrdersTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? todaysOrdersStream;
  final searchCtrl = TextEditingController();
  List<OrderModel> filteredOrdersList = [];
  List<OrderModel> dropdownfilteredOrdersList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todaysOrders();
    selectedDropdown = historyDropdownList[0];
  }

  todaysOrders() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysOrdersStream?.cancel();
      todaysOrdersStream = FBFireStore.orders
          .where('uid', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredOrdersList.clear();
        filteredOrdersList
            .addAll(event.docs.map((e) => OrderModel.fromSnap(e)));
        filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredOrdersList.clear();
        dropdownfilteredOrdersList.addAll(filteredOrdersList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyOrdersStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyOrderSnap = await FBFireStore.orders
          .where('uid', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredOrdersList.clear();
      filteredOrdersList
          .addAll(weeklyOrderSnap.docs.map((e) => OrderModel.fromSnap(e)));
      filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredOrdersList.clear();
      dropdownfilteredOrdersList.addAll(filteredOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyOrdersStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyOrdersSnap = await FBFireStore.orders
          .where('uid', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredOrdersList.clear();
      filteredOrdersList
          .addAll(monthlyOrdersSnap.docs.map((e) => OrderModel.fromSnap(e)));
      filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredOrdersList.clear();
      dropdownfilteredOrdersList.addAll(filteredOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customOrdersStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customOrdersSnap = await FBFireStore.orders
          .where('uid', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredOrdersList.clear();
      filteredOrdersList
          .addAll(customOrdersSnap.docs.map((e) => OrderModel.fromSnap(e)));
      filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredOrdersList.clear();
      dropdownfilteredOrdersList.addAll(filteredOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredOrdersList.clear();
    if (str.isEmpty) {
      filteredOrdersList.addAll(dropdownfilteredOrdersList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                  width: 400,
                  child: SearchField(
                    searchController: searchCtrl,
                    onChanged: (value) {
                      getSearchedData(value.trim());
                    },
                  )),
              SizedBox(width: 20),
              SizedBox(
                width: 150,
                child: DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                  value: selectedDropdown,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                  ),
                  items: historyDropdownList
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  onChanged: (value) async {
                    selectedDropdown = value;
                    searchCtrl.clear();
                    if (value == historyDropdownList[0]) {
                      todaysOrders();
                    } else if (value == historyDropdownList[1]) {
                      weeklyOrdersStream();
                    } else if (value == historyDropdownList[2]) {
                      monthlyOrdersStream();
                    } else if (value == historyDropdownList[3]) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          DateRange? selectedDateRange;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                content: SizedBox(
                                    height: 400,
                                    width: 600,
                                    child: DateRangePickerWidget(
                                      minDate: DateTime(2010),
                                      maxDate: DateTime.now(),
                                      onDateRangeChanged: (value) {
                                        selectedDateRange = value;
                                        setState2(() {});
                                      },
                                    )),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      // print("Range confirmed: $_selectedRange");
                                      if (selectedDateRange != null) {
                                        selEndDate = selectedDateRange!.end;
                                        selstartDate = selectedDateRange!.start;
                                        customOrdersStream();
                                      }
                                      Navigator.of(context).pop();
                                    },
                                    child: Text("Confirm"),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      );
                    }
                  },
                )),
              ),
              if (selectedDropdown == historyDropdownList[3]) ...[
                SizedBox(width: 20),
                InkWell(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date Range',
                        style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: Color.fromARGB(255, 92, 92, 92)),
                      ),
                      Text(
                        '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
              ]
            ],
          ),
          SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
                color: dashboardSelectedColor,
                border: Border.all(color: dividerColor),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8), topRight: Radius.circular(8))

                // color: const Color.fromARGB(255, 228, 228, 228),
                // color: themeColor,
                // color: const Color.fromARGB(255, 177, 139, 86),
                // color: tableHeaderColor,
                // borderRadius: BorderRadius.circular(4),
                ),
            child: Row(
              children: [
                const SizedBox(
                  width: 80,
                  child: Text(
                    'Sr No',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Created At')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Order ID')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Total Amount')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Paid Amount')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Items')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Status')),
                // const SizedBox(width: 5),
                // const Expanded(child: TableHeaderText(headerName: 'Completed On')),
                Opacity(
                  opacity: 0,
                  child: IgnorePointer(
                    ignoring: true,
                    child: SizedBox(
                      width: 60,
                      child: IconButton(
                        highlightColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        onPressed: () {},
                        icon: const Icon(
                          Icons.delete,
                          color: themeColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (filteredOrdersList.isEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: dividerColor),
                      left: BorderSide(color: dividerColor),
                      right: BorderSide(color: dividerColor)),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8))),
              child: const Center(
                child: Text(
                  'No Data Available',
                  style: TextStyle(
                      color: Color(0xff737373),
                      fontSize: 14.5,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ...List.generate(
            filteredOrdersList.length,
            (index) {
              final isLast = index == filteredOrdersList.length - 1;
              final orderData = filteredOrdersList[index];
              return Container(
                decoration: BoxDecoration(
                    border: const Border(
                        bottom: BorderSide(color: dividerColor),
                        left: BorderSide(color: dividerColor),
                        right: BorderSide(color: dividerColor)),
                    borderRadius: isLast
                        ? const BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8))
                        : null),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text((index + 1).toString(),
                          textAlign: TextAlign.center),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '${orderData.createdAt.goodDayDate()}  ${orderData.createdAt.goodTime()}')),

                    const SizedBox(width: 5),
                    Expanded(child: Text(orderData.orderId)),

                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(orderData.totalAmount.toStringAsFixed(2))),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(orderData.paidAmount.toStringAsFixed(2))),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(capilatlizeFirstLetter(
                            orderData.orderProductData.length.toString()))),
                    const SizedBox(width: 5),
                    Expanded(child: Text(orderData.orderStatus)),
                    // const SizedBox(width: 5),
                    // Expanded(
                    //     child: Text(inquiry.endedOn != null
                    //         ? '${inquiry.endedOn?.goodDayDate()}  ${inquiry.endedOn?.goodTime()}'
                    //         : '-')),

                    // Opacity(
                    //   opacity: 0,
                    //   child: IgnorePointer(
                    //     ignoring: true,
                    //     child: SizedBox(
                    //       width: 60,
                    //       child: IconButton(
                    //         highlightColor: Colors.transparent,
                    //         hoverColor: Colors.transparent,
                    //         onPressed: () {},
                    //         icon: const Icon(
                    //           Icons.edit,
                    //           size: 22,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    Opacity(
                      opacity: 0,
                      child: SizedBox(
                        width: 60,
                        child: IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onPressed: null,
                          icon: const Icon(
                            Icons.delete,
                            color: themeColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          )
        ],
      ),
    );
  }
}

class UserDetailsTabView extends StatefulWidget {
  const UserDetailsTabView({super.key, required this.userDocId});
  final String userDocId;
  @override
  State<UserDetailsTabView> createState() => _UserDetailsTabViewState();
}

class _UserDetailsTabViewState extends State<UserDetailsTabView> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: FBFireStore.users.doc(widget.userDocId).snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: SizedBox(
                height: 25,
                width: 25,
                child: CircularProgressIndicator(strokeWidth: 3.5)),
          );
        }
        if (snapshot.hasError) {
          debugPrint(snapshot.error.toString());
          return Center(
            child: Text("Unable to load data"),
          );
        }
        if (snapshot.hasData) {
          final userModel = snapshot.data != null
              ? UserModel.fromDocSnap(snapshot.data!)
              : null;

          return userModel != null
              ? SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 350,
                        crossAxisSpacing: 15,
                        mainAxisSpacing: 15,

                        // crossAxisAlignment: WrapCrossAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              border: Border(
                                  bottom: BorderSide(
                                      color: themeColor.withValues(alpha: .8))),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Name",
                                  style: TextStyle(
                                      // fontWeight: FontWeight.w00,
                                      fontSize: 14.5,
                                      color: Color.fromARGB(255, 93, 93, 93)),
                                ),
                                SizedBox(height: 5),
                                Text(
                                  userModel.name!.trim().isNotEmpty
                                      ? capilatlizeFirstLetter(userModel.name!)
                                      : '-',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xff3F3F3F)),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              border: Border(
                                  bottom: BorderSide(
                                      color: themeColor.withValues(alpha: .8))),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Email",
                                  style: TextStyle(
                                      // fontWeight: FontWeight.w00,
                                      fontSize: 14.5,
                                      color: Color.fromARGB(255, 93, 93, 93)),
                                ),
                                SizedBox(height: 5),
                                Text(
                                  userModel.email.trim().isNotEmpty
                                      ? userModel.email
                                      : '-',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xff3F3F3F)),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              border: Border(
                                  bottom: BorderSide(
                                      color: themeColor.withValues(alpha: .8))),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Mobile",
                                  style: TextStyle(
                                      // fontWeight: FontWeight.w00,
                                      fontSize: 14.5,
                                      color: Color.fromARGB(255, 93, 93, 93)),
                                ),
                                SizedBox(height: 5),
                                Text(
                                  userModel.phone!.trim().isNotEmpty
                                      ? userModel.phone!
                                      : '-',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xff3F3F3F)),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 25),
                      Text(
                        'Addresses',
                        style:
                            GoogleFonts.mulish(fontSize: 17, color: themeColor),
                      ),
                      SizedBox(height: 13),
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 350,
                        crossAxisSpacing: 15,
                        mainAxisSpacing: 15,
                        children: [
                          ...List.generate(
                            userModel.addresses.length,
                            (index) {
                              final address = userModel.addresses[index];
                              return Container(
                                padding: EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.grey.shade50,
                                  // color: const Color(0xfff8fafc),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Address ${index + 1}",
                                      style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600),
                                    ),
                                    SizedBox(height: 5),
                                    Text(
                                      '${address.flatHouse}, ${address.area}, ${address.city}, ${address.state}, ${address.pincode}',
                                      style: TextStyle(
                                          color: Color(0xff3C3C3C),
                                          fontSize: 15.5,
                                          fontWeight: FontWeight.w500),
                                    ),
                                  ],
                                ),
                              );
                            },
                          )
                        ],
                      )
                    ],
                  ),
                )
              : Center(
                  child: Text("No data"),
                );
        }
        return Center(
          child: Text("Something went wrong"),
        );
      },
    );
  }
}

class UserPackageView extends StatefulWidget {
  const UserPackageView({super.key, required this.userDocId});
  final String userDocId;

  @override
  State<UserPackageView> createState() => _UserPackageViewState();
}

class _UserPackageViewState extends State<UserPackageView> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const Placeholder(),
        ],
      ),
    );
  }
}
