import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/router.dart';
import '../../../shared/theme.dart';

class DashboardTile extends StatelessWidget {
  const DashboardTile(
      {super.key,
      required this.icon,
      required this.textName,
      this.isSelected = false,
      this.tab = false,
      this.scafKey,
      required this.route});

  final IconData icon;
  final String textName;
  final bool isSelected;
  final bool tab;
  final GlobalKey<ScaffoldState>? scafKey;
  final String route;

  @override
  Widget build(BuildContext context) {
    final selected = appRouter.routeInformationProvider.value.uri.path == route;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        // hoverColor: Colors.grey.shade200,
        // highlightColor: themeColor,
        // focusColor: themeColor,
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          context.go(route);
          scafKey?.currentState?.closeDrawer();
        },
        child: Container(
          decoration: BoxDecoration(
              color: selected ? themeColor : Colors.transparent,
              // color: selected ? themeColor.withOpacity(0.1) : Colors.transparent,
              borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.all(10),
          width: 250,
          child: tab ? _column(selected) : _row(selected),
        ),
      ),
    );
  }

// F O R   O T H E R
  Row _row(bool selected) {
    return Row(
      children: [
        _icon(selected),
        const SizedBox(width: 15),
        _text(selected),
      ],
    );
  }

// F O R  T A B L E T
  Column _column(bool selected) {
    return Column(
      children: [
        _icon(selected),
        const SizedBox(height: 5),
        _text(selected),
      ],
    );
  }

  Text _text(bool selected) {
    return Text(textName,
        textAlign: TextAlign.center,
        style: selected
            ? const TextStyle(
                fontSize: 16,
                letterSpacing: 1.4,
                fontWeight: FontWeight.w500,
                color: Colors.white)
            : const TextStyle(
                fontSize: 15,
                letterSpacing: 1.4,
                fontWeight: FontWeight.w400,
                color: Colors.black));
  }

  Icon _icon(bool selected) =>
      Icon(size: 22, icon, color: selected ? Colors.white : null);
  // Icon(icon, color: selected ? themeColor.withOpacity(0.85) : null);
}




/*
  Text _text(bool selected, [double? size]) {
    return Text(
      textName,
      style: selected
          ? TextStyle(
              fontWeight: FontWeight.w600,
              color: Color(0xff4676fe),
              fontSize: size)
          : null,
    );
  }
}

        child: Row(
          children: [
            Icon(icon),
            SizedBox(width: 15),
            Text(textName),
          ],
        ),
        */