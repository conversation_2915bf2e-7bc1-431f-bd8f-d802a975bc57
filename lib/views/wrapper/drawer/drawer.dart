import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/shared/theme.dart';
import '../../../controller/home_ctrl.dart';
import '../../../shared/const.dart';
import '../../../shared/router.dart';
import 'drawer_logout.dart';
import 'drawer_tile.dart';

class DashboardDrawer extends StatelessWidget {
  const DashboardDrawer({
    super.key,
    this.isTablet = false,
    this.scafKey,
  });

  final bool isTablet;
  final GlobalKey<ScaffoldState>? scafKey;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: isTablet ? 100 : 260,
      child: GetBuilder<HomeCtrl>(
        builder: (hctrl) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // L O G O
              const SizedBox(height: 12),
              InkWell(
                  hoverColor: Colors.transparent,
                  // highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  // overlayColor: ,
                  highlightColor: Colors.transparent,
                  onTap: () {
                    context.go(homeRoute);
                  },
                  child: DashHeader(isTablet: isTablet)),

              Text(
                "Admin Portal",
                style: TextStyle(letterSpacing: .7, color: themeColor),
              ),
              const SizedBox(height: 12),
              Divider(
                height: 0,
                thickness: 1.1,
                color: dividerColor,
                // thickness: .5,
              ),

              // I T E M S
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // requirements
                      const SizedBox(height: 20),
                      DashboardTile(
                        icon: CupertinoIcons.home,
                        textName: 'Dashboard',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: Routes.dashboard,
                        isSelected: true,
                      ),
                      const SizedBox(height: 10),
                      DashboardTile(
                        icon: CupertinoIcons.cube_box,
                        textName: 'Orders',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: Routes.orders,
                        isSelected: true,
                      ),
                      const SizedBox(height: 10),
                      DashboardTile(
                        icon: CupertinoIcons.doc_checkmark,
                        textName: 'Vendor Order',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: Routes.vendorOrders,
                        isSelected: true,
                      ),
                      const SizedBox(height: 10),

                      if (hctrl.currentUserType == UserTypes.admin) ...[
                        DashboardTile(
                          icon: CupertinoIcons.person_3,
                          textName: 'Team',
                          tab: isTablet,
                          scafKey: scafKey,
                          route: Routes.teamMembers,
                          isSelected: true,
                        ),
                        const SizedBox(height: 10),
                      ],
                      if (hctrl.currentUserType == UserTypes.admin ||
                          hctrl.currentUserType == UserTypes.manager) ...[
                        DashboardTile(
                          icon: CupertinoIcons.person,
                          textName: 'Vendors',
                          tab: isTablet,
                          scafKey: scafKey,
                          route: Routes.vendors,
                        ),
                        const SizedBox(height: 10),
                      ],
                      if (hctrl.currentUserType == UserTypes.admin) ...[
                        DashboardTile(
                          icon: CupertinoIcons.profile_circled,
                          textName: 'Users',
                          tab: isTablet,
                          scafKey: scafKey,
                          route: Routes.users,
                          isSelected: true,
                        ),
                        const SizedBox(height: 10),
                      ],
                      if (hctrl.currentUserType == UserTypes.admin ||
                          hctrl.currentUserType == UserTypes.manager) ...[
                        DashboardTile(
                          icon: CupertinoIcons.add,
                          textName: 'Offerings',
                          tab: isTablet,
                          scafKey: scafKey,
                          isSelected: true,
                          route: Routes.categories,
                        ),
                        const SizedBox(height: 10),
                        DashboardTile(
                          icon: CupertinoIcons.clock,
                          textName: 'Inquiries',
                          tab: isTablet,
                          scafKey: scafKey,
                          isSelected: true,
                          route: Routes.sessions,
                        ),
                        const SizedBox(height: 10),
                        DashboardTile(
                          icon: CupertinoIcons.calendar,
                          textName: 'Bookings',
                          tab: isTablet,
                          scafKey: scafKey,
                          isSelected: true,
                          route: Routes.bookings,
                        ),
                        const SizedBox(height: 10),
                        DashboardTile(
                          icon: Icons.currency_rupee,
                          textName: 'Packages',
                          tab: isTablet,
                          scafKey: scafKey,
                          isSelected: true,
                          route: Routes.packages,
                        ),
                        const SizedBox(height: 10),
                        /*
                        DashboardTile(
                          icon: CupertinoIcons.doc_append,
                          textName: 'Blogs',
                          tab: isTablet,
                          scafKey: scafKey,
                          isSelected: true,
                          route: Routes.blogs,
                        ),
                        const SizedBox(height: 10),
                        DashboardTile(
                          icon: CupertinoIcons.hand_thumbsup,
                          textName: 'Testimonials',
                          tab: isTablet,
                          scafKey: scafKey,
                          isSelected: true,
                          route: Routes.testimonials,
                        ),
                        const SizedBox(height: 10),
                        DashboardTile(
                          icon: CupertinoIcons.camera,
                          textName: 'Gallery',
                          tab: isTablet,
                          scafKey: scafKey,
                          isSelected: true,
                          route: Routes.gallery,
                        ),
                        const SizedBox(height: 10),
                        DashboardTile(
                          icon: CupertinoIcons.question_circle,
                          textName: 'Questionaire',
                          tab: isTablet,
                          scafKey: scafKey,
                          isSelected: true,
                          route: Routes.questionaires,
                        ),
                        const SizedBox(height: 10), 
                        */
                      ],
                    ],
                  ),
                ),
              ),

              Container(
                width: double.maxFinite,
                decoration: const BoxDecoration(
                    border: Border(top: BorderSide(color: dividerColor)),
                    color: dashboardSelectedColor),
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 12.0),
                      child: Text(
                        hctrl.currentUserType?.toUpperCase() ?? "",
                        style: const TextStyle(
                            fontSize: 12,
                            letterSpacing: 1.5,
                            fontWeight: FontWeight.w600,
                            color: Color(0xff737373)),
                      ),
                    ),
                    const SizedBox(height: 10),

                    DashboardTile(
                      icon: CupertinoIcons.settings,
                      textName: 'Settings',
                      tab: isTablet,
                      scafKey: scafKey,
                      route: Routes.settings,
                    ),
                    const SizedBox(height: 5),
                    // logout button

                    DrawerLogout(tab: isTablet),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class DashHeader extends StatelessWidget {
  const DashHeader({
    super.key,
    this.isTablet = false,
  });
  final bool isTablet;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8),
      child: Container(
          width: double.maxFinite,
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
          child: Image.asset(
            'assets/logo.png',
            fit: BoxFit.contain,
          )
          /*  Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Image.asset(
                'assets/sudarshan_logo.png',
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 10),
            if (!isTablet)
              const Expanded(
                  flex: 2,
                  child: Text(
                    "Sudarshan",
                    // textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 25,
                      fontWeight: FontWeight.w600,
                      // letterSpacing: 3,
                    ),
                  ))
          ],
        ), */
          ),
    );
  }
}
