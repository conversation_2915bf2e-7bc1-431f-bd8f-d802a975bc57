import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/shared/theme.dart';
import 'package:wedding_super_admin/views/wrapper/drawer/drawer_logout.dart';
import '../../shared/responsive.dart';
import 'drawer/drawer.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key, required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return true
        ? _Desktop(child)
        : ResponsiveWid(
            mobile: _Mobile(child),
            tablet: _Tablet(child),
            desktop: _Desktop(child),
          );
  }
}

// D E S K T O P

class _Desktop extends StatelessWidget {
  const _Desktop(this.child);
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: const Color.fromARGB(255, 250, 250, 250),
      // backgroundColor: Color.fromARGB(255, 214, 213, 213),
      body: GetBuilder<HomeCtrl>(builder: (hctrl) {
        return hctrl.dataLoaded
            ? hctrl.currentUser == null
                ? const NoUserPage()
                : hctrl.currentUser!.isActive
                    ? Row(
                        children: [
                          Container(
                            decoration: const BoxDecoration(
                                border: Border(
                                  right: BorderSide(color: dividerColor),
                                ),
                                color: dashboardSelectedColor),
                            // padding:
                            //     const EdgeInsets.symmetric(horizontal: 8.0),
                            child: const DashboardDrawer(),
                          ),
                          // const SizedBox(width: 20),
                          Expanded(child: child),
                        ],
                      )
                    : const UserBlockedPage()
            : const Center(child: CircularProgressIndicator(strokeWidth: 3.5));
      }),
    );
  }
}

class UserBlockedPage extends StatelessWidget {
  const UserBlockedPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        // crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            "User Blocked!",
            style: TextStyle(fontSize: 20),
          ),
          SizedBox(height: 10),
          Text(
            "Contact admin",
            style: TextStyle(fontSize: 15),
          ),
          SizedBox(height: 20),
          DrawerLogout(),
        ],
      ),
    );
  }
}

class NoUserPage extends StatelessWidget {
  const NoUserPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        // crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            "No user found",
            style: TextStyle(fontSize: 20),
          ),
          SizedBox(height: 20),
          NoUserLogout(),
        ],
      ),
    );
  }
}

// T A B L E T

class _Tablet extends StatelessWidget {
  const _Tablet(this.child);
  final Widget child;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: const Color.fromARGB(255, 250, 250, 250),
      body: Row(
        children: [
          // side bar
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(8.0),
            child: const DashboardDrawer(isTablet: true),
          ),
          // const SizedBox(width: 10),

          // const SizedBox(width: 20),
          // home page
          Expanded(child: child),
        ],
      ),
    );
  }
}

// M O B I L E

final _scafKey = GlobalKey<ScaffoldState>();

class _Mobile extends StatelessWidget {
  const _Mobile(this.child);
  final Widget child;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      key: _scafKey,
      appBar: AppBar(
        title: const Text('Sudarshan'),
        centerTitle: true,
        scrolledUnderElevation: 12,
        elevation: 12,
        surfaceTintColor: Colors.white,
      ),
      drawer: Theme(
        data: ThemeData(
            canvasColor: Colors.white,
            drawerTheme: const DrawerThemeData(
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
            )),
        child: Drawer(
            child: Padding(
          padding: const EdgeInsets.all(8),
          child: DashboardDrawer(scafKey: _scafKey),
        )),
      ),
      body: child,
    );
  }
}
