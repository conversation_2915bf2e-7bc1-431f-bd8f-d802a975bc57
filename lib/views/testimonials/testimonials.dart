import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/testimonials.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';
import 'package:wedding_super_admin/views/testimonials/widgets/testimonial_table_header.dart';
import 'package:wedding_super_admin/views/testimonials/widgets/testimonial_tile.dart';

import '../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';

class Testimonials extends StatefulWidget {
  const Testimonials({super.key});

  @override
  State<Testimonials> createState() => _TestimonialsState();
}

class _TestimonialsState extends State<Testimonials> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
            pageTile: 'Testimonial Management',
            pagesubTile: 'Showcase client feedback'),
        Expanded(
          child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
              child: Column(children: [
                PageHeaderWithTrailingAndBack(
                  title: 'Testimonials',
                  showTrailing: true,
                  trailing: ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeColor,
                      elevation: 0,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4)),
                      // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                    ),
                    onPressed: () {
                      _addEditTestimonials(context, null);
                    },
                    icon: Icon(
                      CupertinoIcons.add,
                      size: 20,
                    ),
                    label: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text('Add',
                          style: GoogleFonts.livvic(
                              letterSpacing: 1.3,
                              fontSize: 14,
                              fontWeight: FontWeight.w500)),
                    ),
                  ),
                ),
                // PageHeaderWithButton(
                //   title: "Testimonials",
                //   button: true,
                //   onPressed: () async {
                //     _addEditTestimonials(context, null);
                //   },
                //   buttonName: 'Add',
                //   icon: CupertinoIcons.add,
                // ),
                const SizedBox(height: 20),
                const TestimonialTableHeader(),
                GetBuilder<HomeCtrl>(
                  builder: (hctrl) {
                    return hctrl.testimonials.isEmpty
                        ? Container(
                            padding: const EdgeInsets.symmetric(vertical: 15),
                            decoration: const BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(color: dividerColor),
                                    left: BorderSide(color: dividerColor),
                                    right: BorderSide(color: dividerColor)),
                                borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(8),
                                    bottomRight: Radius.circular(8))),
                            child: Center(
                              child: const Text(
                                "No Testimonial Added",
                                style: TextStyle(
                                    color: Color(0xff737373),
                                    fontSize: 14.5,
                                    fontWeight: FontWeight.w500),
                              ),
                            ),
                          )
                        : Column(
                            children: [
                              ...List.generate(
                                hctrl.testimonials.length,
                                (index) {
                                  final testimonial = hctrl.testimonials[index];
                                  return TestimonialTile(
                                      isLast: index ==
                                          (hctrl.testimonials.length - 1),
                                      onEdit: () {
                                        _addEditTestimonials(
                                            context, testimonial);
                                      },
                                      index: index,
                                      testimonialModel: testimonial);
                                },
                              )
                            ],
                          );
                  },
                )
              ])),
        ),
      ],
    );
  }

  _addEditTestimonials(BuildContext context, TestimonialsModel? testimonial) {
    bool loading = false;
    TextEditingController titleCtrl = TextEditingController();
    TextEditingController linkCtrl = TextEditingController();
    if (testimonial != null) {
      titleCtrl.text = testimonial.title;
      linkCtrl.text = testimonial.videoLink;
    }
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            // backgroundColor: dashboardColor,
            // surfaceTintColor: dashboardColor,
            // shadowColor: dashboardColor,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            contentPadding:
                const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
            title: Text(
                testimonial != null ? "Edit Testimonial" : "Add Testimonial"),
            content: SizedBox(
              width: 280,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: titleCtrl,
                    cursorHeight: 20,
                    decoration: inpDecor().copyWith(labelText: 'Title'),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: linkCtrl,
                    cursorHeight: 20,
                    decoration: inpDecor().copyWith(labelText: 'Video Link'),
                  ),
                ],
              ),
            ),
            actionsAlignment: MainAxisAlignment.center,
            actions: loading
                ? [
                    const Center(
                      child: SizedBox(
                        height: 25,
                        width: 25,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                          color: themeColor,
                        ),
                      ),
                    )
                  ]
                : [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: themeColor,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: () async {
                        if (loading) return;

                        if (titleCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(context, 'Title Required');
                          return;
                        }
                        if (linkCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(context, 'Link Required');
                          return;
                        }
                        try {
                          setState2(() {
                            loading = true;
                          });
                          // print(
                          //     '${DateTime.now().toLocal()} - ${DateTime.now().toUtc()} - ${DateTime.now().toLocal().millisecondsSinceEpoch} - ${DateTime.now().toUtc().millisecondsSinceEpoch}');

                          final data = {
                            'title': titleCtrl.text.toLowerCase().trim(),
                            'videoLink': linkCtrl.text.trim(),
                            'createdAt': testimonial != null
                                ? testimonial.createdAt.millisecondsSinceEpoch
                                : DateTime.now().millisecondsSinceEpoch,
                            'isActive': testimonial != null
                                ? testimonial.isActive
                                : true,
                            // 'utc': Timestamp.fromDate(DateTime.now().toUtc()),
                            // 'local':
                            //     DateTime.now().toLocal().millisecondsSinceEpoch,
                            // 'localz':
                            //     DateTime.now().toUtc().millisecondsSinceEpoch,
                          };
                          testimonial != null
                              ? await FBFireStore.testimonials
                                  .doc(testimonial.docId)
                                  .update(data)
                              : await FBFireStore.testimonials.add(data);
                          setState2(() {
                            loading = false;
                          });

                          titleCtrl.clear();
                          linkCtrl.clear();
                          if (context.mounted) {
                            Navigator.of(context).pop();
                            showAppSnackBar(
                              context,
                              testimonial != null
                                  ? 'Testimonial Updated'
                                  : 'Testimonial Added',
                            );
                            // ScaffoldMessenger.of(context).showSnackBar(snackBar);
                          }
                        } catch (e) {
                          debugPrint(e.toString());
                          showAppSnackBar(context, e.toString());
                        }
                      },
                      child: const Text("Add"),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text("Cancel"),
                    ),
                  ],
          );
        });
      },
    );
  }
}
