import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wedding_super_admin/models/testimonials.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';

class TestimonialTile extends StatefulWidget {
  const TestimonialTile(
      {super.key,
      required this.index,
      this.testimonialModel,
      required this.onEdit,
      required this.isLast});
  final int index;
  final bool isLast;
  final TestimonialsModel? testimonialModel;
  final Function()? onEdit;
  @override
  State<TestimonialTile> createState() => _TestimonialTileState();
}

class _TestimonialTileState extends State<TestimonialTile> {
  @override
  Widget build(BuildContext context) {
    // print(widget.testimonialModel?.title);
    // print(widget.testimonialModel?.createdAt.toDate().isUtc);
    return widget.testimonialModel != null
        ? Container(
            decoration: BoxDecoration(
                border: const Border(
                    bottom: BorderSide(color: dividerColor),
                    left: BorderSide(color: dividerColor),
                    right: BorderSide(color: dividerColor)),
                borderRadius: widget.isLast
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8))
                    : null),
            child: Row(
              children: [
                SizedBox(
                    width: 80,
                    child: Text((widget.index + 1).toString(),
                        textAlign: TextAlign.center)),
                const SizedBox(width: 5),

                Expanded(
                  flex: 2,
                  child: Text(
                    capilatlizeFirstLetter(
                        widget.testimonialModel?.title ?? ""),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 5),

                // Expanded(child: Text(widget.vendorModel?.vendorName ?? "")),
                // const SizedBox(width: 5),

                // Expanded(flex: 2, child: Text(property?.email ?? "")),
                Expanded(
                    flex: 3,
                    child: Text(
                      widget.testimonialModel?.videoLink ?? "",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    )),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                        "${widget.testimonialModel?.createdAt.convertToDDMMYY()}, ${widget.testimonialModel?.createdAt.isUtc ?? false ? widget.testimonialModel?.createdAt.toLocal().goodTime() : widget.testimonialModel?.createdAt.goodTime()}")),
                const SizedBox(width: 5),
                SizedBox(
                  width: 60,
                  child: Transform.scale(
                    scale: .65,
                    child: CupertinoSwitch(
                      value: widget.testimonialModel!.isActive,
                      onChanged: (value) async {
                        await FBFireStore.testimonials
                            .doc(widget.testimonialModel?.docId)
                            .update({'isActive': value});
                      },
                    ),
                  ),
                ),
                SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: widget.onEdit,
                    icon: const Icon(
                      Icons.edit,
                      color: themeColor,
                      size: 22,
                    ),
                  ),
                ),
                SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () async {
                      showDialog(
                        context: context,
                        builder: (context) {
                          bool loading = false;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                title: const Text("Alert"),
                                content: const Text(
                                    "Are you sure you want to delete"),
                                actions: loading
                                    ? [
                                        const Center(
                                          child: SizedBox(
                                            height: 25,
                                            width: 25,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                            ),
                                          ),
                                        )
                                      ]
                                    : [
                                        TextButton(
                                            onPressed: () async {
                                              if (loading) return;

                                              try {
                                                setState2(() {
                                                  loading = true;
                                                });
                                                try {
                                                  await FBFireStore.testimonials
                                                      .doc(widget
                                                          .testimonialModel
                                                          ?.docId)
                                                      .delete();
                                                } on Exception catch (e) {
                                                  debugPrint(e.toString());
                                                }
                                                const snackBar = SnackBar(
                                                    content: Text(
                                                        "Testimonial deleted successfully"));
                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(snackBar);
                                                }
                                              } catch (e) {
                                                debugPrint(e.toString());

                                                if (context.mounted) {
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop();
                                                }
                                              }
                                            },
                                            child: const Text('Yes')),
                                        TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('No')),
                                      ],
                              );
                            },
                          );
                        },
                      );
                    },
                    icon: const Icon(Icons.delete, color: themeColor),
                  ),
                ),
              ],
            ),
          )
        : const SizedBox();
  }
}
