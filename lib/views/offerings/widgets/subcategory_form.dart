import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/product.dart';
import '../../../models/subcategories.dart';
import '../../../models/variants.dart';
import '../../../services/image_picker.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';
import '../../common/form_header_tile.dart';
import '../../common/page_header.dart';

class SubcategoryForm extends StatefulWidget {
  const SubcategoryForm({super.key, this.subCatId, required this.mainCatId});
  final String? subCatId;
  final String mainCatId;
  @override
  State<SubcategoryForm> createState() => _SubcategoryFormState();
}

class _SubcategoryFormState extends State<SubcategoryForm> {
// SubCategory? subCategory;

//   getSubCat() async {
//     // final subCatSnap = await FBFireStore.subCategories
//     //     .where('mainCatId', isEqualTo: widget.categoryId)
//     //     .get();
//     // subCategories =
//     //     subCatSnap.docs.map((e) => SubCategory.fromSnap(e)).toList();
//     subCategory = ctrl.subcategories
//         .where((element) => element.offeringId == widget.categoryId)
//         .toList();
//     setState(() {});
//   }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (hCtrl) {
        final subCategory = hCtrl.subcategories
            .firstWhereOrNull((element) => element.docId == widget.subCatId);
        return SubCatForm2(
          subCategory: subCategory,
          mainCatId: widget.mainCatId,
        );
      },
    );
  }
}

class SubCatForm2 extends StatefulWidget {
  const SubCatForm2({
    super.key,
    required this.subCategory,
    required this.mainCatId,
  });

  final SubCategory? subCategory;
  final String mainCatId;
  @override
  State<SubCatForm2> createState() => _SubCatForm2State();
}

class _SubCatForm2State extends State<SubCatForm2> {
  SelectedImage? subCatPickedImage;
  final subCatNameCtrl = TextEditingController();
  final marginPercentageCtrl = TextEditingController();
  final tagCtrl = TextEditingController();
  // List<String> tags = [];
  List<String> removedTags = [];
  List<String> addedTags = [];
  // List<Map<String, dynamic>> detailsTypes = [];
  List<String> removedDetailsTypes = [];
  List<Map<String, dynamic>> addedDetailsTypes = [];
  // String? subCatImageUrl;
  bool onSubmitLoad = false;

  @override
  void initState() {
    super.initState();
    if (widget.subCategory != null) {
      subCatNameCtrl.text = widget.subCategory?.name ?? "";
      marginPercentageCtrl.text =
          widget.subCategory?.marginPercentage.toString() ?? '0';
      // subCatImageUrl = widget.subCategory?.image;
      // tags.clear();
      // tags.addAll(widget.subCategory?.tags ?? []);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithTrailingAndBack(
            title: "Sub-Category Form",
            showTrailing: widget.subCategory != null,
            trailing: SizedBox(
              width: 100,
              child: Transform.scale(
                scale: .9,
                child: CupertinoSwitch(
                  value: widget.subCategory?.isActive ?? true,
                  onChanged: (value) async {
                    //TODO: to ask off all products
                    // await FBFireStore.offerings
                    //     .doc(widget.subCatId)
                    //     .update({'isActive': value});
                    // getCategoryData();
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const FormHeaderTile(title: 'Sub-Category Details'),
              const SizedBox(height: 25),
              SizedBox(
                height: 200,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(7),
                          // color: const Color.fromARGB(9, 0, 0, 0),
                          color: Colors.white),
                      clipBehavior: Clip.antiAlias,
                      child: subCatPickedImage != null
                          ? Image.memory(
                              subCatPickedImage!.uInt8List,
                              width: double.maxFinite,
                              fit: BoxFit.cover,
                            )
                          : widget.subCategory?.image != null
                              ? AspectRatio(
                                  aspectRatio: 1,
                                  child: CachedNetworkImage(
                                    imageUrl: widget.subCategory?.image ?? "",
                                    // width: double.maxFinite,
                                    // height: double.maxFinite,
                                    fit: BoxFit.cover,
                                    errorWidget: (context, url, error) {
                                      return const Icon(CupertinoIcons
                                          .photo_fill_on_rectangle_fill);
                                    },
                                    placeholder: (context, url) {
                                      return const Icon(CupertinoIcons
                                          .photo_fill_on_rectangle_fill);
                                    },
                                  ),
                                )
                              : const Icon(
                                  CupertinoIcons.photo_fill_on_rectangle_fill),
                    ),
                    const SizedBox(width: 25),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: subCatNameCtrl,
                              cursorHeight: 20,
                              decoration: inpDecor()
                                  .copyWith(labelText: 'Sub-Category Name*'),
                            ),
                          ),
                          const SizedBox(height: 20),
                          Expanded(
                            child: TextFormField(
                              controller: marginPercentageCtrl,
                              cursorHeight: 20,
                              decoration:
                                  inpDecor().copyWith(labelText: 'Margin (%)*'),
                            ),
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    overlayColor: Colors.transparent,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  onPressed: () async {
                                    final res = await ImagePickerService()
                                        .pickImageNew(context,
                                            useCompressor: true);
                                    if (res != null) {
                                      subCatPickedImage = res;
                                    }
                                    setState(() {});
                                  },
                                  child: const Text("Change Image")),
                              const SizedBox(width: 15),
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    overlayColor: Colors.transparent,
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(8))),
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) {
                                      return Dialog(
                                        backgroundColor: Colors.white,
                                        surfaceTintColor: Colors.white,
                                        child: Container(
                                          height: 500,
                                          width: 500,
                                          clipBehavior: Clip.antiAlias,
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          child: Stack(
                                            children: [
                                              InteractiveViewer(
                                                maxScale: 1.5,
                                                minScale: .9,
                                                child: AspectRatio(
                                                  aspectRatio: 1,
                                                  child: CachedNetworkImage(
                                                    imageUrl: widget
                                                        .subCategory!.image,
                                                    width: double.maxFinite,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                              Align(
                                                alignment: Alignment.topRight,
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 8.0, top: 8),
                                                  child: InkWell(
                                                    onTap: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                    child: Container(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              3),
                                                      decoration:
                                                          const BoxDecoration(
                                                              color:
                                                                  Colors.white,
                                                              shape: BoxShape
                                                                  .circle,
                                                              boxShadow: [
                                                            BoxShadow(
                                                                color: Colors
                                                                    .black26,
                                                                blurRadius: 2,
                                                                // spreadRadius: 0,
                                                                spreadRadius: 1,
                                                                offset: Offset(
                                                                    0, 2)),
                                                          ]),
                                                      child: const Icon(
                                                        CupertinoIcons.xmark,
                                                        size: 18,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                },
                                child: const Text("View Image"),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              FormHeaderTile(
                title: 'Details Types',
                button: true,
                buttonName: "Add",
                icon: CupertinoIcons.add,
                onPressed: () {
                  _addEditDetailType(context, null);
                },
              ),
              const SizedBox(height: 20),
              Builder(
                builder: (context) {
                  List<Map<String, dynamic>> detailTypes = widget
                          .subCategory?.detailTypes
                          .where((element) => !removedDetailsTypes
                              .contains(element['detailType']))
                          .toList() ??
                      [];
                  return StaggeredGrid.extent(
                    maxCrossAxisExtent: 220,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                    // alignment: WrapAlignment.start,
                    // runAlignment: WrapAlignment.start,
                    children: [
                      ...List.generate(
                        detailTypes.length,
                        (index) {
                          return ListTile(
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                                side: const BorderSide(color: themeColor)),
                            title: Text(
                              capilatlizeFirstLetter(
                                  detailTypes[index]['detailType']),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            subtitle: Text(detailTypes[index]['priorityNo']),
                            trailing: IconButton(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) {
                                      bool isLoading = false;
                                      return StatefulBuilder(
                                          builder: (context, setState2) {
                                        return AlertDialog(
                                          backgroundColor: Colors.white,
                                          surfaceTintColor: Colors.white,
                                          title: const Text("Delete"),
                                          content: const Text(
                                              "Are you sure you want to delete this detail type? It will be removed from all products in this sub-category."),
                                          actions: isLoading
                                              ? [
                                                  const SizedBox(
                                                    height: 25,
                                                    width: 25,
                                                    child: Center(
                                                      child:
                                                          CircularProgressIndicator(
                                                              strokeWidth: 3.5),
                                                    ),
                                                  )
                                                ]
                                              : [
                                                  TextButton(
                                                    onPressed: () {
                                                      if (isLoading) return;

                                                      isLoading = true;
                                                      setState2(() {});
                                                      removedDetailsTypes.add(
                                                          detailTypes[index]
                                                              ['detailType']);
                                                      isLoading = false;
                                                      setState2(() {});
                                                      Navigator.of(context)
                                                          .pop();
                                                      setState(() {});
                                                    },
                                                    child: const Text("Yes"),
                                                  ),
                                                  TextButton(
                                                    onPressed: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                    child: const Text("No"),
                                                  ),
                                                ],
                                        );
                                      });
                                    },
                                  );
                                },
                                icon: const Icon(CupertinoIcons.delete)),
                          );
                        },
                      ),
                      ...List.generate(
                        addedDetailsTypes.length,
                        (index) {
                          return ListTile(
                            title: Text(
                              capilatlizeFirstLetter(
                                  addedDetailsTypes[index]['detailType']),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            subtitle:
                                Text(addedDetailsTypes[index]['priorityNo']),
                            trailing: IconButton(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) {
                                      bool isLoading = false;
                                      return StatefulBuilder(
                                          builder: (context, setState2) {
                                        return AlertDialog(
                                          backgroundColor: Colors.white,
                                          surfaceTintColor: Colors.white,
                                          title: const Text("Delete"),
                                          content: const Text(
                                              "Are you sure you want to delete detail type?"),
                                          actions: isLoading
                                              ? [
                                                  const SizedBox(
                                                    height: 25,
                                                    width: 25,
                                                    child: Center(
                                                      child:
                                                          CircularProgressIndicator(
                                                              strokeWidth: 3.5),
                                                    ),
                                                  )
                                                ]
                                              : [
                                                  TextButton(
                                                    onPressed: () {
                                                      if (isLoading) return;

                                                      isLoading = true;
                                                      setState2(() {});
                                                      addedDetailsTypes
                                                          .removeAt(index);
                                                      isLoading = false;
                                                      setState2(() {});
                                                      Navigator.of(context)
                                                          .pop();
                                                      setState(() {});
                                                    },
                                                    child: const Text("Yes"),
                                                  ),
                                                  TextButton(
                                                    onPressed: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                    child: const Text("No"),
                                                  ),
                                                ],
                                        );
                                      });
                                    },
                                  );
                                },
                                icon: const Icon(CupertinoIcons.delete)),
                          );
                        },
                      ),
                    ],
                  );
                },
              ),
              const SizedBox(height: 20),

              const FormHeaderTile(title: 'Tags'),
              const SizedBox(height: 25),
              // ChipTags(
              //   decoration: inpDecor().copyWith(labelText: 'Enter tag name'),
              //   list: tags,
              //   createTagOnSubmit: true,
              // ),
              TextFormField(
                controller: tagCtrl,
                onFieldSubmitted: (value) async {
                  addedTags.add(tagCtrl.text.toLowerCase());
                  tagCtrl.clear();
                  setState(() {});
                },
                decoration: inpDecor().copyWith(
                    labelText: 'Enter tag name',
                    hintText: ' Hit (enter) once written'),
              ),
              const SizedBox(height: 20),
              Builder(builder: (context) {
                // tags.clear();
                List<String> tags = widget.subCategory?.tags
                        .where((element) => !removedTags.contains(element))
                        .toList() ??
                    [];
                // for (String tag in widget.subCategory?.tags ?? []) {
                //   tags.addIf(!removedTags.contains(tag), tag);
                // }
                return Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  alignment: WrapAlignment.start,
                  runAlignment: WrapAlignment.start,
                  children: [
                    ...List.generate(tags.length, (index) {
                      return Chip(
                        elevation: 0,
                        // padding: const EdgeInsets.all(5),
                        label: Text(capilatlizeFirstLetter(tags[index])),
                        // labelStyle: const TextStyle(),
                        // labelPadding: const EdgeInsets.all(1),
                        backgroundColor: Colors.white,
                        deleteIcon: const Icon(CupertinoIcons.xmark, size: 18),
                        deleteIconColor: Colors.black,
                        onDeleted: () async {
                          removedTags.add(tags[index]);
                          setState(() {});
                          // await FBFireStore.tags.doc(tagData?.docId).update({
                          //   'tags': FieldValue.arrayRemove([tags[index]])
                          // });
                          // getTag();
                        },
                        side: BorderSide(color: Colors.grey.shade300),
                      );
                    }),
                    ...List.generate(addedTags.length, (index) {
                      return Chip(
                        elevation: 0,
                        // padding: const EdgeInsets.all(5),
                        label: Text(capilatlizeFirstLetter(addedTags[index])),
                        // labelStyle: const TextStyle(),
                        // labelPadding: const EdgeInsets.all(1),
                        backgroundColor: Colors.white,
                        deleteIcon: const Icon(CupertinoIcons.xmark, size: 18),
                        deleteIconColor: Colors.black,
                        onDeleted: () async {
                          addedTags.removeAt(index);
                          setState(() {});
                          // await FBFireStore.tags.doc(tagData?.docId).update({
                          //   'tags': FieldValue.arrayRemove([tags[index]])
                          // });
                          // getTag();
                        },
                        side: BorderSide(color: Colors.grey.shade300),
                      );
                    }),
                  ],
                );
              }),
              const SizedBox(height: 25),
              Center(
                child: onSubmitLoad
                    ? const SizedBox(
                        height: 30,
                        width: 30,
                        child: CircularProgressIndicator(
                          color: themeColor,
                          strokeWidth: 3.5,
                        ),
                      )
                    : ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: themeColor,
                          elevation: 0,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4)),
                        ),
                        onPressed: () async {
                          try {
                            if (onSubmitLoad) return;
                            if (subCatNameCtrl.text.trim().isEmpty) {
                              showErrorAppSnackBar(context, 'Name Required');
                              return;
                            }
                            if (marginPercentageCtrl.text.trim().isEmpty) {
                              showErrorAppSnackBar(
                                  context, 'Margin Percentage Required');
                              return;
                            }

                            final imageUrl = subCatPickedImage != null
                                ? await uploadSubCategoryFile(
                                    subCatPickedImage!)
                                : widget.subCategory?.image ?? "";
                            if (imageUrl == null || imageUrl == "") {
                              showErrorAppSnackBar(context, 'Image Required');
                              return;
                            }

                            List<Map<String, dynamic>> uploadDetailTypes = [];
                            uploadDetailTypes.addAll(widget
                                    .subCategory?.detailTypes
                                    .where((element) =>
                                        !removedDetailsTypes.contains(
                                            element['detailType'] as String))
                                    .toList() ??
                                []);
                            uploadDetailTypes.addAll(addedDetailsTypes);

                            if (uploadDetailTypes.isEmpty) {
                              showErrorAppSnackBar(
                                  context, 'Detail types cannot be empty');
                              return;
                            }

                            List<String> uploadTags = [];
                            uploadTags.addAll(widget.subCategory?.tags
                                    .where((element) =>
                                        !removedTags.contains(element))
                                    .toList() ??
                                []);
                            uploadTags.addAll(addedTags);
                            addedTags.clear();

                            if (uploadTags.isEmpty) {
                              showErrorAppSnackBar(
                                  context, 'Tags cannot be empty');
                              return;
                            }

                            setState(() {
                              onSubmitLoad = true;
                            });
                            final batch = FBFireStore.fb.batch();
                            if (removedDetailsTypes.isNotEmpty &&
                                widget.subCategory != null) {
                              final variantSnap = await FBFireStore.variants
                                  .where('subCatId',
                                      isEqualTo: widget.subCategory?.docId)
                                  .get();
                              final noofloops =
                                  (variantSnap.docs.length / 450).ceil();
                              for (var i = 0; i < noofloops; i++) {
                                // final start = ;
                                final snapList = variantSnap.docs.sublist(
                                  i * 450,
                                  i == (noofloops - 1)
                                      ? variantSnap.docs.length
                                      : (i * 450) + 499,
                                );
                                for (var variantDoc in snapList) {
                                  final variant =
                                      NewVariantModel.fromSnap(variantDoc);
                                  variant.detailTypes.removeWhere(
                                      (key, value) =>
                                          removedDetailsTypes.contains(key));
                                  batch.update(
                                      FBFireStore.variants.doc(variant.docId), {
                                    'detailTypes': variant.detailTypes,
                                  });
                                }
                                // for (var variantDoc in snapList) {
                                //   final product =
                                //       NewVariantModel.fromDocSnap(variantDoc);

                                //   final oldvariants = product.variants;
                                //   product.combinedDetailsList.removeWhere(
                                //       (element) => removedDetailsTypes
                                //           .contains(element));
                                //   final updatedVaraints = <VariantModel>[];
                                //   for (var variant in oldvariants) {
                                //     print(
                                //         'before , name: ${product.name}, product doc: ${variantDoc.data()['variants']['ogEhtijr']},old variants: ${variant.detailTypes}');
                                //     product.detailTypes.removeWhere(
                                //         (key, value) =>
                                //             removedDetailsTypes.contains(key));
                                //     updatedVaraints.add(variant);
                                //     print(
                                //         'after, name: ${product.name}, updated variants: ${variant.detailTypes}');
                                //   }
                                //   final finalVaraintMap = {};
                                //   for (var element in updatedVaraints) {
                                //     finalVaraintMap.addAll(element.toJson());
                                //   }
                                //   batch.update(
                                //       FBFireStore.variants.doc(product.docId), {
                                //     'combinedDetailsList':
                                //         product.combinedDetailsList,
                                //     'variants': finalVaraintMap,
                                //   });
                                // }
                              }
                            }

                            if (removedDetailsTypes.isNotEmpty &&
                                widget.subCategory != null) {
                              final productSnap = await FBFireStore.products
                                  .where('subCatDocId',
                                      isEqualTo: widget.subCategory?.docId)
                                  .get();
                              final noofloops =
                                  (productSnap.docs.length / 450).ceil();
                              for (var i = 0; i < noofloops; i++) {
                                // final start = ;
                                final productsnapList =
                                    productSnap.docs.sublist(
                                  i * 450,
                                  i == (noofloops - 1)
                                      ? productSnap.docs.length
                                      : (i * 450) + 499,
                                );

                                for (var productDoc in productsnapList) {
                                  final product =
                                      ProductModel.fromSnap(productDoc);
                                  product.detailsList.removeWhere((element) =>
                                      removedDetailsTypes.contains(element));
                                  batch.update(
                                      FBFireStore.products.doc(product.docId), {
                                    'detailsList': product.detailsList,
                                  });
                                }
                              }
                            }

                            final subcatref = widget.subCategory != null
                                ? FBFireStore.subCategories
                                    .doc(widget.subCategory?.docId)
                                : FBFireStore.subCategories.doc();

                            final combinationNames = generateCombinations(
                                subCatNameCtrl.text.toLowerCase());

                            final allData = widget.subCategory?.allData ?? {};
                            // REMOVE FROM ALL DATA IF IS REMOVED
                            allData.removeWhere((key, value) =>
                                removedDetailsTypes.contains(key));
                            // ADD  DATA

                            for (var element in addedDetailsTypes) {
                              allData.addEntries(
                                  [MapEntry(element['detailType'], [])]);
                            }

                            final data = {
                              'name': subCatNameCtrl.text.toLowerCase(),
                              'image': imageUrl,
                              'isActive': widget.subCategory != null
                                  ? widget.subCategory?.isActive
                                  : true,
                              'combinationNames': combinationNames,
                              'offeringId': widget.mainCatId,
                              'minPrice': widget.subCategory != null
                                  ? widget.subCategory?.minPrice
                                  : 0,
                              'maxPrice': widget.subCategory != null
                                  ? widget.subCategory?.maxPrice
                                  : 0,
                              'tags': uploadTags,
                              'detailTypes': uploadDetailTypes,
                              'marginPercentage': num.tryParse(
                                  marginPercentageCtrl.text.trim()),
                              'allData': allData,
                            };
                            batch.set(subcatref, data);
                            // widget.subCategory != null
                            //     ? await FBFireStore.subCategories
                            //         .doc(widget.subCategory?.docId)
                            //         .set(data)
                            //     : await FBFireStore.subCategories.add(data);
                            await batch.commit();
                            addedDetailsTypes.clear();
                            setState(() {
                              onSubmitLoad = false;
                            });

                            final snackBar = SnackBar(
                              content: Text(widget.subCategory != null
                                  ? "Sub-category Updated"
                                  : "Sub-Category Added"),
                              duration: const Duration(seconds: 2),
                            );

                            if (context.mounted) {
                              context.pop(true);
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(snackBar);
                            }
                          } on Exception catch (e) {
                            debugPrint(e.toString());
                            setState(() {
                              onSubmitLoad = false;
                            });
                            showErrorAppSnackBar(
                                context, 'something went wrong');
                          }
                        },
                        child: const Text("Save"),
                      ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Future<dynamic> _addEditDetailType(
      BuildContext context, Map<String, dynamic>? detailType) {
    TextEditingController detailNameCtrl = TextEditingController();
    TextEditingController priorityCtrl = TextEditingController();
    if (detailType != null) {
      detailNameCtrl.text = detailType['detailType'];
      priorityCtrl.text = detailType['priortiyNo'];
    }
    return showDialog(
      context: context,
      builder: (context) {
        bool loading = false;
        return StatefulBuilder(
          builder: (context, setState2) {
            return AlertDialog(
                backgroundColor: Colors.white,
                surfaceTintColor: Colors.white,
                // backgroundColor: dashboardColor,
                // surfaceTintColor: dashboardColor,
                // shadowColor: dashboardColor,
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
                title: const Text("Add Detail Type"),
                content: SizedBox(
                  width: 280,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextFormField(
                        controller: detailNameCtrl,
                        cursorHeight: 20,
                        decoration:
                            inpDecor().copyWith(labelText: 'Detail Type'),
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: priorityCtrl,
                        cursorHeight: 20,
                        decoration:
                            inpDecor().copyWith(labelText: 'Priority No'),
                      ),
                    ],
                  ),
                ),
                actionsAlignment: MainAxisAlignment.center,
                actions: loading
                    ? [
                        const Center(
                          child: SizedBox(
                            height: 25,
                            width: 25,
                            child: CircularProgressIndicator(
                              strokeWidth: 2.5,
                              color: themeColor,
                            ),
                          ),
                        )
                      ]
                    : [
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: themeColor,
                            foregroundColor: Colors.white,
                          ),
                          onPressed: () async {
                            if (loading) return;

                            if (detailNameCtrl.text.trim().isEmpty) {
                              showErrorAppSnackBar(context, 'Title Required');
                              return;
                            }
                            if (priorityCtrl.text.trim().isEmpty) {
                              showErrorAppSnackBar(context, 'Link Required');
                              return;
                            }
                            try {
                              setState2(() {
                                loading = true;
                              });

                              final data = {
                                'detailType':
                                    detailNameCtrl.text.toLowerCase().trim(),
                                'priorityNo': priorityCtrl.text.trim(),
                              };

                              addedDetailsTypes.add(data);
                              // testimonial != null
                              //     ? await FBFireStore.testimonials
                              //         .doc(testimonial.docId)
                              //         .update(data)
                              //     : await FBFireStore.testimonials
                              //         .add(data);
                              setState2(() {
                                loading = false;
                              });

                              setState(() {});
                              priorityCtrl.clear();
                              detailNameCtrl.clear();
                              if (context.mounted) {
                                Navigator.of(context).pop();
                                // showAppSnackBar(context, 'Testimonial Added');
                                // ScaffoldMessenger.of(context).showSnackBar(snackBar);
                              }
                            } catch (e) {
                              debugPrint(e.toString());
                              showAppSnackBar(context, e.toString());
                            }
                          },
                          child: const Text("Add"),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: const Text("Cancel"),
                        ),
                      ]);
          },
        );
      },
    );
  }
}
