import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/models/offerings.dart';
import 'package:wedding_super_admin/models/subcategories.dart';
import '../../../controller/home_ctrl.dart';
import '../../../services/image_picker.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/router.dart';
import '../../common/form_header_tile.dart';
import '../../common/page_header.dart';

class CategoriesForm extends StatefulWidget {
  const CategoriesForm({super.key, this.categoryId});
  final String? categoryId;
  @override
  State<CategoriesForm> createState() => _CategoriesFormState();
}

class _CategoriesFormState extends State<CategoriesForm> {
  OfferingsModel? offering;
  // TagModel? tagData;
  List<SubCategory> subCategories = [];
  List<String> tags = [];
  final ctrl = Get.find<HomeCtrl>();

  @override
  void initState() {
    super.initState();
    getCategoryData();
    // getTag();
    getSubCat();
  }

  getCategoryData() {
    // final mainCatSnap =
    //     await FBFireStore.categories.doc(widget.categoryId).get();
    // mainCategory = MainCategory.fromDocSnap(mainCatSnap);
    offering = ctrl.offerings
        .firstWhereOrNull((element) => element.docId == widget.categoryId);
    tags = offering?.tags ?? [];
    setState(() {});
  }

  // getTag() async {
  //   // final tagSnap = await FBFireStore.tags
  //   //     .where('type', isEqualTo: TypeModel.category)
  //   //     .where('typeId', isEqualTo: widget.categoryId)
  //   //     .get();
  //   // tagData = tagSnap.docs.isNotEmpty
  //   //     ? TagModel.fromDocSnap(tagSnap.docs.first)
  //   //     : null;
  //   tags = ctrl.offerings
  //           .firstWhereOrNull((element) => element.docId == widget.categoryId)
  //           ?.tags ??
  //       [];
  //   setState(() {});
  // }

  getSubCat() {
    // final subCatSnap = await FBFireStore.subCategories
    //     .where('mainCatId', isEqualTo: widget.categoryId)
    //     .get();
    // subCategories =
    //     subCatSnap.docs.map((e) => SubCategory.fromSnap(e)).toList();
    subCategories = ctrl.subcategories
        .where((element) => element.offeringId == widget.categoryId)
        .toList();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final mainCatDocRef = FBFireStore.offerings.doc(widget.categoryId);
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithTrailingAndBack(
            title: "Offering Form",
            showTrailing: true,
            trailing: SizedBox(
              width: 100,
              child: Transform.scale(
                scale: .9,
                child: CupertinoSwitch(
                  value: offering?.isActive ?? true,
                  onChanged: (value) async {
                    await FBFireStore.offerings
                        .doc(offering?.docId)
                        .update({'isActive': value});
                    getCategoryData();
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          _offeringDetails(mainCatDocRef),
          const SizedBox(height: 40),
          _tagsDetails(mainCatDocRef),
          const SizedBox(height: 40),
          _subCat(),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _subCat() {
    return Column(
      children: [
        FormHeaderTile(
          title: 'Sub Categories',
          button: true,
          buttonName: "Add",
          icon: CupertinoIcons.add,
          onPressed: () async {
            // _addEditSubCat(subCat: null, subCatTagsGet: [], subCatTagId: null);
            bool? res = await context.push("${Routes.subcategoryF}/${null}",
                extra: widget.categoryId) as bool?;
            if (res == true) {
              getSubCat();
            }
          },
        ),
        const SizedBox(height: 25),
        StaggeredGrid.extent(
          maxCrossAxisExtent: 300,
          mainAxisSpacing: 20,
          crossAxisSpacing: 20,
          children: [
            ...List.generate(
              subCategories.length,
              (index) {
                return Stack(
                  children: [
                    Container(
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(7),
                          color: Colors.white,
                          boxShadow: const [
                            BoxShadow(
                              color: Colors.black26,
                              offset: Offset(0, 2),
                              blurRadius: 5,
                            ),
                          ]),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CachedNetworkImage(
                            imageUrl: subCategories[index].image,
                            height: 200,
                            width: double.maxFinite,
                            fit: BoxFit.cover,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  capilatlizeFirstLetter(
                                      subCategories[index].name),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 15.5),
                                ),
                              ),
                              const SizedBox(width: 10),
                              IconButton(
                                  onPressed: () async {
                                    // final subCatTagsSnap = await FBFireStore
                                    //     .tags
                                    //     .where('typeId',
                                    //         isEqualTo:
                                    //             subCategories[index].docId)
                                    //     .where("type",
                                    //         isEqualTo: TypeModel.subcat)
                                    //     .get();
                                    // final subCatTagData =
                                    //     subCatTagsSnap.docs.isNotEmpty
                                    //         ? TagModel.fromDocSnap(
                                    //             subCatTagsSnap.docs.first)
                                    //         : null;
                                    // final subCatTags =
                                    //     subCatTagData?.tags ?? [];
                                    bool? res = await context.push(
                                        "${Routes.subcategoryF}/${subCategories[index].docId}",
                                        extra: widget.categoryId) as bool?;
                                    if (res == true) {
                                      getSubCat();
                                    }
                                    // _addEditSubCat(
                                    //     subCat: subCategories[index],
                                    //     subCatTagsGet: subCatTags,
                                    //     subCatTagId: subCatTagData?.docId);
                                  },
                                  icon: const Icon(Icons.edit)),
                              const SizedBox(width: 10),
                              Transform.scale(
                                scale: .8,
                                child: CupertinoSwitch(
                                  value: subCategories[index].isActive,
                                  onChanged: (value) async {
                                    showDialog(
                                      context: context,
                                      builder: (context) {
                                        return AlertDialog(
                                          backgroundColor: Colors.white,
                                          surfaceTintColor: Colors.white,
                                          title: const Text('Alert'),
                                          content: Text(
                                              'Are you sure you want to ${value ? 'on' : 'off'} this sub-category ?'),
                                          actions: [
                                            TextButton(
                                              onPressed: () async {
                                                await FBFireStore.subCategories
                                                    .doc(subCategories[index]
                                                        .docId)
                                                    .update(
                                                        {'isActive': value});
                                                getSubCat();
                                                Navigator.of(context).pop();
                                              },
                                              child: const Text('Yes'),
                                            ),
                                            TextButton(
                                              onPressed: () async {
                                                Navigator.of(context).pop();
                                              },
                                              child: const Text('No'),
                                            ),
                                          ],
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 4),
                            ],
                          ),
                          const SizedBox(height: 8),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.topRight,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0, top: 8),
                        child: InkWell(
                          onTap: () async {
                            showDialog(
                              context: context,
                              builder: (context) {
                                bool loader = false;
                                return StatefulBuilder(
                                    builder: (context, setState2) {
                                  return AlertDialog(
                                    backgroundColor: Colors.white,
                                    surfaceTintColor: Colors.white,
                                    title: const Text('Delete'),
                                    content: const Text(
                                        "Are you sure you want to delete?"),
                                    actions: loader
                                        ? [
                                            const Center(
                                              child: SizedBox(
                                                height: 25,
                                                width: 25,
                                                child:
                                                    CircularProgressIndicator(
                                                        strokeWidth: 3.5),
                                              ),
                                            ),
                                          ]
                                        : [
                                            TextButton(
                                                onPressed: () async {
                                                  setState2(() {
                                                    loader = true;
                                                  });
                                                  try {
                                                    await FBFireStore
                                                        .subCategories
                                                        .doc(
                                                            subCategories[index]
                                                                .docId)
                                                        .delete();
                                                    if (context.mounted) {
                                                      setState2(() {
                                                        loader = false;
                                                      });
                                                      ScaffoldMessenger.of(
                                                              context)
                                                          .showSnackBar(
                                                              const SnackBar(
                                                                  content: Text(
                                                                      "Sub-category Deleted Successfully")));
                                                      Navigator.of(context)
                                                          .pop();
                                                      getSubCat();
                                                    }
                                                  } on Exception catch (e) {
                                                    setState2(() {
                                                      loader = false;
                                                    });
                                                    debugPrint(e.toString());
                                                    if (context.mounted) {
                                                      Navigator.of(context)
                                                          .pop();
                                                      ScaffoldMessenger.of(
                                                              context)
                                                          .showSnackBar(
                                                              const SnackBar(
                                                                  content: Text(
                                                                      "Facing Some Issue")));
                                                    }
                                                  }
                                                },
                                                child: const Text("Yes")),
                                            TextButton(
                                                onPressed: () async {
                                                  Navigator.of(context).pop();
                                                },
                                                child: const Text("No")),
                                          ],
                                  );
                                });
                              },
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.all(3),
                            decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                      color: Colors.black26,
                                      blurRadius: 2,
                                      // spreadRadius: 0,
                                      spreadRadius: 1,
                                      offset: Offset(0, 2)),
                                ]),
                            child: const Icon(
                              CupertinoIcons.xmark,
                              size: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            )
          ],
        )
      ],
    );
  }

  Widget _tagsDetails(DocumentReference<Map<String, dynamic>> mainCatDocRef) {
    TextEditingController tagCtrl = TextEditingController();
    // TagModel? tagData;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const FormHeaderTile(title: 'Tags'),
        const SizedBox(height: 25),
        // ChipTags(
        //   decoration: inpDecor().copyWith(labelText: 'Enter tag name'),
        //   list: tags,
        //   createTagOnSubmit: true,
        // ),
        TextFormField(
          controller: tagCtrl,
          onFieldSubmitted: (value) async {
            if (value.isEmpty || value == " ") {
              return;
            }
            await mainCatDocRef.update({
              'tags':
                  FieldValue.arrayUnion([tagCtrl.text.toLowerCase().trim()]),
            });
            tagCtrl.clear();
            getCategoryData();
            // if (tagData == null) {
            //   await FBFireStore.tags.add({
            //     'name': offering?.name,
            //     'type': TypeModel.category,
            //     'typeId': widget.categoryId,
            //     'tags': [tagCtrl.text.toLowerCase().trim()]
            //   });
            // } else {
            //   await FBFireStore.tags.doc(tagData!.docId).update({
            //     'tags':
            //         FieldValue.arrayUnion([tagCtrl.text.toLowerCase().trim()]),
            //   });
            // }
          },
          decoration: inpDecor().copyWith(
              labelText: 'Enter tag name',
              hintText: ' Hit (enter) once written'),
        ),
        const SizedBox(height: 20),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          alignment: WrapAlignment.start,
          runAlignment: WrapAlignment.start,
          children: [
            ...List.generate(tags.length, (index) {
              return Chip(
                elevation: 0,
                // padding: const EdgeInsets.all(5),
                label: Text(capilatlizeFirstLetter(tags[index])),
                // labelStyle: const TextStyle(),
                // labelPadding: const EdgeInsets.all(1),
                backgroundColor: Colors.white,
                deleteIcon: const Icon(CupertinoIcons.xmark, size: 18),
                deleteIconColor: Colors.black,
                onDeleted: () async {
                  await mainCatDocRef.update({
                    'tags': FieldValue.arrayRemove([tags[index]])
                  });
                  // getTag();
                  getCategoryData();
                },
                side: BorderSide(color: Colors.grey.shade300),
              );
            })
          ],
        ),
      ],
    );
  }

  Column _offeringDetails(
      DocumentReference<Map<String, dynamic>> mainCatDocRef) {
    final catNameCtrl = TextEditingController();
    final priorityCtrl = TextEditingController();
    final descCtrl = TextEditingController();
    String? catDataImage;
    // bool showonHomePage = offering?.showonHomePage ?? false;
    catNameCtrl.text = capilatlizeFirstLetter(offering?.name ?? "") ?? '';
    descCtrl.text = capilatlizeFirstLetter(offering?.desc ?? "") ?? '';
    priorityCtrl.text =
        capilatlizeFirstLetter(offering?.priorityNo.toString() ?? "") ?? '';
    catDataImage = offering?.image;
    return Column(
      children: [
        const FormHeaderTile(title: 'Offering Details'),
        const SizedBox(height: 25),
        SizedBox(
          height: 200,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                  width: 200,
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(7),
                      // color: const Color.fromARGB(9, 0, 0, 0),
                      color: Colors.white),
                  clipBehavior: Clip.antiAlias,
                  child: catDataImage != null
                      ? AspectRatio(
                          aspectRatio: 1,
                          child: CachedNetworkImage(
                            imageUrl: catDataImage,
                            // width: double.maxFinite,
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) {
                              return const Icon(
                                  CupertinoIcons.photo_fill_on_rectangle_fill);
                            },
                            placeholder: (context, url) {
                              return const Icon(
                                  CupertinoIcons.photo_fill_on_rectangle_fill);
                            },
                          ),
                        )
                      : const Icon(
                          CupertinoIcons.photo_fill_on_rectangle_fill)),
              const SizedBox(width: 25),
              Expanded(
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: catNameCtrl,
                            onFieldSubmitted: (value) async {
                              final combinationNames = generateCombinations(
                                  value.toLowerCase().trim());

                              await mainCatDocRef.update({
                                'name': value.trim(),
                                'combinationNames': combinationNames,
                              });
                              getCategoryData();
                            },
                            cursorHeight: 20,
                            decoration: inpDecor()
                                .copyWith(labelText: 'Offering Name*'),
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: TextFormField(
                            controller: priorityCtrl,
                            onFieldSubmitted: (value) async {
                              if (int.tryParse(value.trim()) == null) {
                                showErrorAppSnackBar(
                                    context, 'only numeric value required');
                                return;
                              }
                              // final combinationNames =
                              //     generateCombinations(value.toLowerCase());
                              await mainCatDocRef.update({
                                'priorityNo': value.trim(),
                                // 'combinationNames': combinationNames,
                              });
                              getCategoryData();
                            },
                            cursorHeight: 20,
                            decoration:
                                inpDecor().copyWith(labelText: 'Priority No.*'),
                          ),
                        ),
                        // Expanded(
                        //   child: Container(
                        //     clipBehavior: Clip.antiAlias,
                        //     height: 48,
                        //     padding: const EdgeInsets.only(left: 3),
                        //     decoration: BoxDecoration(
                        //         border: Border.all(color: Colors.grey.shade400),
                        //         borderRadius: BorderRadius.circular(7),
                        //         // color: const Color.fromARGB(9, 0, 0, 0),
                        //         color: Colors.transparent),
                        //     child: Row(
                        //       children: [
                        //         Checkbox(
                        //           side: const BorderSide(color: Colors.grey),
                        //           checkColor: Colors.white,
                        //           activeColor: themeColor,
                        //           value: showonHomePage,
                        //           onChanged: (value) async {
                        //             showonHomePage = value!;
                        //             await mainCatDocRef.update({
                        //               'showonHomePage': value,
                        //             });
                        //             getCategoryData();
                        //           },
                        //         ),
                        //         const SizedBox(width: 5),
                        //         const Text(
                        //           "Show On Homepage",
                        //           style: TextStyle(color: Colors.black),
                        //         ),
                        //       ],
                        //     ),
                        //   ),
                        // ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: descCtrl,
                            onFieldSubmitted: (value) async {
                              // final combinationNames =
                              //     generateCombinations(value.toLowerCase());
                              await mainCatDocRef.update({
                                'desc': value.trim(),
                                // 'combinationNames': combinationNames,
                              });
                              getCategoryData();
                            },
                            cursorHeight: 20,
                            decoration:
                                inpDecor().copyWith(labelText: 'Description*'),
                          ),
                        )
                      ],
                    ),
                    const Spacer(),
                    if (offering?.image != null)
                      Row(
                        children: [
                          ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                  overlayColor: Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8))),
                              onPressed: catImagePicker,
                              child: const Text("Change Image")),
                          const SizedBox(width: 15),
                          ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                  overlayColor: Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8))),
                              onPressed: () {
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return Dialog(
                                      backgroundColor: Colors.white,
                                      surfaceTintColor: Colors.white,
                                      child: Container(
                                        height: 500,
                                        width: 500,
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Stack(
                                          children: [
                                            InteractiveViewer(
                                              maxScale: 1.5,
                                              minScale: .9,
                                              child: AspectRatio(
                                                aspectRatio: 1,
                                                child: CachedNetworkImage(
                                                  imageUrl: offering!.image,
                                                  width: double.maxFinite,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                            ),
                                            Align(
                                              alignment: Alignment.topRight,
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 8.0, top: 8),
                                                child: InkWell(
                                                  onTap: () {
                                                    Navigator.of(context).pop();
                                                  },
                                                  child: Container(
                                                    padding:
                                                        const EdgeInsets.all(3),
                                                    decoration:
                                                        const BoxDecoration(
                                                            color: Colors.white,
                                                            shape:
                                                                BoxShape.circle,
                                                            boxShadow: [
                                                          BoxShadow(
                                                              color: Colors
                                                                  .black26,
                                                              blurRadius: 2,
                                                              // spreadRadius: 0,
                                                              spreadRadius: 1,
                                                              offset:
                                                                  Offset(0, 2)),
                                                        ]),
                                                    child: const Icon(
                                                      CupertinoIcons.xmark,
                                                      size: 18,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                              child: const Text("View Image")),
                        ],
                      ),
                    const SizedBox(width: 15),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  catImagePicker() async {
    final res =
        await ImagePickerService().pickImageNew(context, useCompressor: true);
    // if (res != null) {
    //   catPickedImage = res;
    // }
    final imageUrl = res != null ? await uploadCategoryFile(res) : null;
    if (res != null) {
      await FBFireStore.offerings.doc(widget.categoryId).update({
        'image': imageUrl,
      });
    }
    getCategoryData();
  }
}
