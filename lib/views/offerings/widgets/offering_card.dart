import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/methods.dart';
import '../../../shared/router.dart';

class OfferingCard extends StatelessWidget {
  const OfferingCard({
    super.key,
    required this.imageString,
    required this.textString,
    required this.categoryId,
    required this.isActive,
  });

  final String imageString;
  final String textString;
  final String categoryId;
  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1,
      child: InkWell(
        hoverColor: Colors.transparent,
        highlightColor: Colors.transparent,
        overlayColor: const WidgetStatePropertyAll(Colors.transparent),
        /* onLongPress: () async {
          showDialog(
            context: context,
            builder: (context) {
              bool loading = false;
              return StatefulBuilder(builder: (context, setState2) {
                return AlertDialog(
                  title: const Text("Confirm Delete"),
                  content: const Text(
                      "Are you sure you want to delete this category?"),
                  actions: loading
                      ? [
                          const Center(
                            child: SizedBox(
                              height: 25,
                              width: 25,
                              child: CircularProgressIndicator(
                                strokeWidth: 2.5,
                              ),
                            ),
                          )
                        ]
                      : [
                          TextButton(
                            onPressed: () async {
                              try {
                                setState2(() {
                                  loading = true;
                                });

                                await FBFireStore.categories
                                    .doc(categoryId)
                                    .delete();

                                const snackBar = SnackBar(
                                  content: Row(
                                    children: [
                                      Icon(CupertinoIcons.delete,
                                          color: Colors.red),
                                      SizedBox(width: 10),
                                      Text("Category Deleted"),
                                    ],
                                  ),
                                  duration: Duration(seconds: 2),
                                );
                                setState2(() {
                                  loading = false;
                                });
                                if (context.mounted) {
                                  Navigator.of(context).pop();
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(snackBar);
                                }
                              } catch (e) {
                                debugPrint(e.toString());
                                final snackBar = SnackBar(
                                  content: Row(
                                    children: [
                                      const Icon(
                                          CupertinoIcons
                                              .exclamationmark_octagon,
                                          color: Colors.red),
                                      const SizedBox(width: 10),
                                      Text(
                                          "Unable to delete category. ${e.toString()}."),
                                    ],
                                  ),
                                  duration: const Duration(seconds: 2),
                                );
                                setState2(() {
                                  loading = false;
                                });
                                if (context.mounted) {
                                  Navigator.of(context).pop();
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(snackBar);
                                }
                              }
                            },
                            child: const Text("Yes"),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: const Text("No"),
                          ),
                        ],
                );
              });
            },
          );
        },
         */
        onTap: () {
          context.push('${Routes.category}/$categoryId');
        },
        child: Container(
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Stack(
            fit: StackFit.expand,
            children: [
              CachedNetworkImage(
                imageUrl: imageString,
                fit: BoxFit.cover,
                placeholder: (context, url) {
                  return const Icon(CupertinoIcons.camera);
                },
              ),

              // FadeInImage.memoryNetwork(
              //   image: category.image,
              //   fit: BoxFit.cover,
              //   placeholder: kTransparentImage,
              // ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  alignment: Alignment.bottomCenter,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      stops: [
                        0.07,
                        0.12,
                        0.15,
                        // 0.2,
                        0.3,
                      ],
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: [
                        // Color.fromARGB(255, 41, 40, 40),
                        // Color.fromARGB(179, 41, 40, 40),
                        Color.fromARGB(210, 41, 40, 40),
                        Colors.black45,
                        // Colors.black38,
                        Color.fromARGB(79, 0, 0, 0),
                        // Colors.white38,
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Align(
                    alignment: Alignment.bottomLeft,
                    child: Padding(
                      padding:
                          const EdgeInsets.only(left: 15, bottom: 8, right: 15),
                      child: Text(
                        capilatlizeFirstLetter(textString),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 18),
                      ),
                    ),
                  ),
                ),
              ),
              if (!isActive)
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(.55),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
