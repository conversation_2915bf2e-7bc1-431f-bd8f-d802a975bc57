import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wedding_super_admin/shared/theme.dart';
import '../../controller/home_ctrl.dart';
import '../../services/image_picker.dart';
import '../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../common/header_search_feild.dart';
import '../common/page_header.dart';
import '../common/top_sections.dart';
import 'widgets/offering_card.dart';

class OfferingsPage extends StatefulWidget {
  const OfferingsPage({super.key});

  @override
  State<OfferingsPage> createState() => _OfferingsPageState();
}

class _OfferingsPageState extends State<OfferingsPage> {
  // List<MainCategory> filteredList = [];
  final searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
          pageTile: 'Offering Management',
          pagesubTile: 'Organize services into customizable categories',
        ),
        Expanded(
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PageHeaderWithButton(
                  title: "Offerings",
                  onPressed: () {
                    _newOfferingForm(context);
                  },
                  buttonName: 'New',
                  button: true,
                  icon: CupertinoIcons.add,
                ),
                const SizedBox(height: 20),
                SearchField(
                  searchController: searchController,
                  onChanged: (p0) {
                    setState(() {});
                  },
                ),
                const SizedBox(height: 20),
                GetBuilder<HomeCtrl>(builder: (hctrl) {
                  final filteredList = hctrl.offerings
                      .where((element) => element.name
                          .toLowerCase()
                          .contains(searchController.text.toLowerCase()))
                      .toList();
                  return filteredList.isEmpty
                      ? const Center(child: Text('No Offering available'))
                      : StaggeredGrid.extent(
                          maxCrossAxisExtent: 300,
                          crossAxisSpacing: 20,
                          mainAxisSpacing: 20,
                          children: [
                            ...List.generate(
                              filteredList.length,
                              (index) => Stack(
                                children: [
                                  OfferingCard(
                                    categoryId: filteredList[index].docId,
                                    imageString: filteredList[index].image,
                                    textString: filteredList[index].name,
                                    isActive: filteredList[index].isActive,
                                  ),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Tooltip(
                                      message: 'Delete',
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                            right: 8.0, top: 8),
                                        child: InkWell(
                                          onTap: () {
                                            showDialog(
                                              context: context,
                                              builder: (context) {
                                                bool isLoading = false;
                                                return StatefulBuilder(builder:
                                                    (context, setState2) {
                                                  return AlertDialog(
                                                    backgroundColor:
                                                        Colors.white,
                                                    surfaceTintColor:
                                                        Colors.white,
                                                    title: const Text('Delete'),
                                                    content: const Text(
                                                        "Are you sure you want to delete?"),
                                                    actions: isLoading
                                                        ? [
                                                            const SizedBox(
                                                              height: 25,
                                                              width: 25,
                                                              child: Center(
                                                                child: CircularProgressIndicator(
                                                                    strokeWidth:
                                                                        3.5),
                                                              ),
                                                            )
                                                          ]
                                                        : [
                                                            TextButton(
                                                                onPressed:
                                                                    () async {
                                                                  if (isLoading) {
                                                                    return;
                                                                  }

                                                                  isLoading =
                                                                      true;
                                                                  setState2(
                                                                      () {});
                                                                  try {
                                                                    await FBFireStore
                                                                        .offerings
                                                                        .doc(filteredList[index]
                                                                            .docId)
                                                                        .delete();
                                                                    if (context
                                                                        .mounted) {
                                                                      ScaffoldMessenger.of(
                                                                              context)
                                                                          .showSnackBar(
                                                                              const SnackBar(content: Text("Offering Deleted Successfully")));
                                                                      isLoading =
                                                                          false;
                                                                      setState2(
                                                                          () {});
                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();
                                                                    }
                                                                  } on Exception catch (e) {
                                                                    debugPrint(e
                                                                        .toString());
                                                                    isLoading =
                                                                        false;
                                                                    setState2(
                                                                        () {});
                                                                    if (context
                                                                        .mounted) {
                                                                      ScaffoldMessenger.of(
                                                                              context)
                                                                          .showSnackBar(
                                                                              const SnackBar(content: Text("Facing Some Issue")));
                                                                    }
                                                                  }
                                                                },
                                                                child:
                                                                    const Text(
                                                                        "Yes")),
                                                            TextButton(
                                                                onPressed:
                                                                    () async {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                },
                                                                child:
                                                                    const Text(
                                                                        "No")),
                                                          ],
                                                  );
                                                });
                                              },
                                            );
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.all(3),
                                            decoration: const BoxDecoration(
                                                color: Colors.white,
                                                shape: BoxShape.circle,
                                                boxShadow: [
                                                  BoxShadow(
                                                      color: Colors.black26,
                                                      blurRadius: 2,
                                                      // spreadRadius: 0,
                                                      spreadRadius: 1,
                                                      offset: Offset(0, 2)),
                                                ]),
                                            child: const Icon(
                                              CupertinoIcons.xmark,
                                              size: 18,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        );
                })
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<dynamic> _newOfferingForm(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) {
        bool loading = false;
        SelectedImage? catPickedImage;
        // bool categoryShowOnSearch = false;
        TextEditingController catNameCtrl = TextEditingController();
        TextEditingController descCtrl = TextEditingController();
        TextEditingController priorityCtrl = TextEditingController();
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            // backgroundColor: const Color.fromARGB(255, 255, 248, 237),
            // surfaceTintColor: const Color.fromARGB(255, 255, 248, 237),
            // shadowColor: const Color.fromARGB(255, 255, 248, 237),
            // backgroundColor: dashboardColor,
            // surfaceTintColor: dashboardColor,
            // shadowColor: dashboardColor,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            contentPadding:
                const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
            title: const Text("Offering"),
            content: SizedBox(
              width: 280,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: catNameCtrl,
                    cursorHeight: 20,
                    decoration:
                        inpDecor().copyWith(labelText: 'Offering Name*'),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: priorityCtrl,
                    cursorHeight: 20,
                    decoration: inpDecor().copyWith(
                        labelText: 'Priority No.*',
                        hintText: ' numeric value only'),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: descCtrl,
                    cursorHeight: 20,
                    maxLines: 4,
                    decoration: inpDecor().copyWith(labelText: 'Description*'),
                  ),
                  // const SizedBox(height: 20),
                  // Container(
                  //   clipBehavior: Clip.antiAlias,
                  //   height: 55,
                  //   padding: const EdgeInsets.only(left: 3),
                  //   decoration: BoxDecoration(
                  //     borderRadius: BorderRadius.circular(7),
                  //     border: Border.all(color: Colors.grey.shade400),
                  //     // OutlineInputBorder(
                  //     //   borderRadius: BorderRadius.circular(7),
                  //     //   borderSide: BorderSide(color: Colors.grey.shade400),
                  //     // ),
                  //     // color: const Color.fromARGB(9, 0, 0, 0),
                  //     color: Colors.transparent,
                  //   ),
                  //   child: Row(
                  //     children: [
                  //       Checkbox(
                  //         side: const BorderSide(color: Colors.grey),
                  //         checkColor: Colors.white,
                  //         activeColor: themeColor,
                  //         value: categoryShowOnSearch,
                  //         onChanged: (value) => setState2(
                  //           () {
                  //             categoryShowOnSearch = value!;
                  //           },
                  //         ),
                  //       ),
                  //       const SizedBox(width: 5),
                  //       const Text(
                  //         "Show On Search",
                  //         style: TextStyle(color: Colors.black),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  const SizedBox(height: 20),
                  InkWell(
                    onTap: () async {
                      final res = await ImagePickerService()
                          .pickImageNew(context, useCompressor: true);
                      if (res != null) {
                        catPickedImage = res;
                      }
                      setState2(() {});
                    },
                    borderRadius: BorderRadius.circular(3),
                    child: Container(
                      width: double.maxFinite,
                      height: 150,
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade400),
                          borderRadius: BorderRadius.circular(7),
                          // color: const Color.fromARGB(9, 0, 0, 0),
                          color: Colors.transparent),
                      child: catPickedImage == null
                          ? const Icon(
                              CupertinoIcons.photo_fill_on_rectangle_fill)
                          : Image.memory(
                              catPickedImage!.uInt8List,
                              width: double.maxFinite,
                              fit: BoxFit.cover,
                            ),
                    ),
                  )
                ],
              ),
            ),
            actionsAlignment: MainAxisAlignment.center,
            actions: loading
                ? [
                    const Center(
                      child: SizedBox(
                        height: 25,
                        width: 25,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                          color: themeColor,
                        ),
                      ),
                    )
                  ]
                : [
                    ElevatedButton(
                      onPressed: () async {
                        if (loading) return;

                        if (catNameCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(context, 'Category Name Empty');
                          return;
                        }
                        if (descCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(
                              context, 'Category Description Empty');
                          return;
                        }
                        if (priorityCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(
                              context, 'Priority Number Empty');
                          return;
                        }
                        if (catPickedImage == null) {
                          showErrorAppSnackBar(context, 'Image not picked');
                          return;
                        }
                        if (int.tryParse(priorityCtrl.text.trim()) == null) {
                          showErrorAppSnackBar(
                              context, 'only numeric value required');
                          return;
                        }
                        try {
                          setState2(() {
                            loading = true;
                          });
                          final imageUrl = catPickedImage != null
                              ? await uploadCategoryFile(catPickedImage!)
                              : "";
                          final combinationNames = generateCombinations(
                              catNameCtrl.text.trim().toLowerCase());
                          final data = {
                            'name': catNameCtrl.text.trim().toLowerCase(),
                            'desc': descCtrl.text.trim(),
                            'image': imageUrl,
                            'createdAt': Timestamp.now(),
                            'priorityNo': priorityCtrl.text.trim(),
                            'isActive': true,
                            'combinationNames': combinationNames,
                          };
                          await FBFireStore.offerings.add(data);
                          setState2(() {
                            loading = false;
                          });
                          const snackBar = SnackBar(
                            content: Text("Category Added"),
                            duration: Duration(seconds: 2),
                          );
                          catNameCtrl.clear();
                          descCtrl.clear();
                          priorityCtrl.clear();
                          catPickedImage = null;
                          if (context.mounted) {
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                          }
                        } catch (e) {
                          debugPrint(e.toString());
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: themeColor,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text("Save"),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text("Cancel"),
                    ),
                  ],
          );
        });
      },
    );
  }
}
