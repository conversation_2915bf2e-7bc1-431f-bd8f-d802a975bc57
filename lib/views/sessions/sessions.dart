import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_date_range_picker/flutter_date_range_picker.dart';
import 'package:get/get.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/session_model.dart';
import 'package:wedding_super_admin/views/common/header_search_feild.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';
import 'package:wedding_super_admin/views/sessions/widgets/session_table_header.dart';
import 'package:wedding_super_admin/views/sessions/widgets/session_tile.dart';

import '../../shared/const.dart';
import '../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';

class AllActiceSessionsPage extends StatefulWidget {
  const AllActiceSessionsPage({super.key});

  @override
  State<AllActiceSessionsPage> createState() => _AllActiceSessionsPageState();
}

class _AllActiceSessionsPageState extends State<AllActiceSessionsPage> {
  TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
            pageTile: 'Inquiry Management',
            pagesubTile: 'Manage client inquiries and chat responses'),
        GetBuilder<HomeCtrl>(
          builder: (hCtrl) {
            return Expanded(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                child: SessionDisplayPage(
                    key: ValueKey(DateTime.now()),
                    activeSessionsList: hCtrl.activeSessionsList),
              ),
            );
          },
        ),
      ],
    );
  }
}

class SessionDisplayPage extends StatefulWidget {
  const SessionDisplayPage({super.key, required this.activeSessionsList});
  final List<SessionModel> activeSessionsList;
  @override
  State<SessionDisplayPage> createState() => _SessionDisplayPageState();
}

class _SessionDisplayPageState extends State<SessionDisplayPage> {
  // final filterSessionList = searchController.text.trim().isNotEmpty
  //     ? hCtrl.activeSessionsList
  //         .where((element) => (element.inquiryId
  //             .toLowerCase()
  //             .contains(searchController.text.toLowerCase())))
  //         .toList()
  //     : hCtrl.activeSessionsList;

  final searchCtrl = TextEditingController();
  List<SessionModel> filteredSessionList = [];
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? todaysSessionStream;
  // List<VendorOrderModel> filteredVendorOrdersList = [];
  List<SessionModel> dropdownfilteredSessionList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    getSearchedData('');
  }

  getSearchedData(String str) {
    filteredSessionList.clear();
    if (str.isEmpty) {
      if (selectedDropdown != null) {
        filteredSessionList.addAll(dropdownfilteredSessionList);
      } else {
        filteredSessionList.addAll(widget.activeSessionsList);
      }
    } else {
      filteredSessionList.addAll(widget.activeSessionsList
          .where((element) => (element.inquiryId.toLowerCase().contains(str)))
          .toList());
    }
    filteredSessionList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    setState(() {});
  }

  todaysSessions() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysSessionStream?.cancel();
      todaysSessionStream = FBFireStore.sessions
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredSessionList.clear();
        filteredSessionList
            .addAll(event.docs.map((e) => SessionModel.fromSnap(e)));
        filteredSessionList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredSessionList.clear();
        dropdownfilteredSessionList.addAll(filteredSessionList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklySessionsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklySessionSnap = await FBFireStore.sessions
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredSessionList.clear();
      filteredSessionList
          .addAll(weeklySessionSnap.docs.map((e) => SessionModel.fromSnap(e)));
      filteredSessionList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredSessionList.clear();
      dropdownfilteredSessionList.addAll(filteredSessionList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlySessionsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlySessionSnap = await FBFireStore.sessions
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredSessionList.clear();
      filteredSessionList
          .addAll(monthlySessionSnap.docs.map((e) => SessionModel.fromSnap(e)));
      filteredSessionList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredSessionList.clear();
      dropdownfilteredSessionList.addAll(filteredSessionList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customSessionStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customSessionSnap = await FBFireStore.sessions
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredSessionList.clear();
      filteredSessionList
          .addAll(customSessionSnap.docs.map((e) => SessionModel.fromSnap(e)));
      filteredSessionList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredSessionList.clear();
      dropdownfilteredSessionList.addAll(filteredSessionList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void dispose() {
    super.dispose();
    searchCtrl.dispose();
    todaysSessionStream?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PageHeaderWithButton(
          title: 'Inquiries',
          button: false,
          // buttonName: 'New',
          // icon: CupertinoIcons.add,
          onPressed: () {},
        ),
        const SizedBox(height: 20),
        Row(
          children: [
            SizedBox(
              width: 400,
              child: SearchField(
                searchController: searchCtrl,
                onChanged: (value) {
                  getSearchedData(value.toLowerCase().trim());
                },
              ),
            ),
            SizedBox(width: 15),
            SizedBox(
              width: 150,
              child: DropdownButtonHideUnderline(
                  child: DropdownButtonFormField(
                hint: Text(
                  'Filter',
                  style: TextStyle(
                    fontSize: 14,
                  ),
                ),
                value: selectedDropdown,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                      borderSide: const BorderSide(color: Color(0xffede2de)),
                      borderRadius: BorderRadius.circular(7)),
                  enabledBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: Color(0xffede2de)),
                      borderRadius: BorderRadius.circular(7)),
                  focusedBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: Color(0xffede2de)),
                      borderRadius: BorderRadius.circular(7)),
                ),
                items: historyDropdownList
                    .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                    .toList(),
                onChanged: (value) async {
                  selectedDropdown = value;
                  searchCtrl.clear();
                  if (value == historyDropdownList[0]) {
                    todaysSessions();
                  } else if (value == historyDropdownList[1]) {
                    weeklySessionsStream();
                  } else if (value == historyDropdownList[2]) {
                    monthlySessionsStream();
                  } else if (value == historyDropdownList[3]) {
                    showDialog(
                      context: context,
                      builder: (context) {
                        DateRange? selectedDateRange;
                        return StatefulBuilder(
                          builder: (context, setState2) {
                            return AlertDialog(
                              backgroundColor: Colors.white,
                              surfaceTintColor: Colors.white,
                              content: SizedBox(
                                  height: 400,
                                  width: 600,
                                  child: DateRangePickerWidget(
                                    minDate: DateTime(2010),
                                    maxDate: DateTime.now(),
                                    onDateRangeChanged: (value) {
                                      selectedDateRange = value;
                                      setState2(() {});
                                    },
                                  )),
                              actions: [
                                TextButton(
                                  onPressed: () {
                                    // print("Range confirmed: $_selectedRange");
                                    if (selectedDateRange != null) {
                                      selEndDate = selectedDateRange!.end;
                                      selstartDate = selectedDateRange!.start;
                                      customSessionStream();
                                    }
                                    Navigator.of(context).pop();
                                  },
                                  child: Text("Confirm"),
                                ),
                              ],
                            );
                          },
                        );
                      },
                    );
                  }
                },
              )),
            ),
            if (selectedDropdown == historyDropdownList[3]) ...[
              SizedBox(width: 20),
              InkWell(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date Range',
                      style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                          color: Color.fromARGB(255, 92, 92, 92)),
                    ),
                    Text(
                      '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                    )
                  ],
                ),
              ),
            ],
            if (selectedDropdown != null) ...[
              SizedBox(width: 15),
              ElevatedButton(
                  style: ElevatedButton.styleFrom(elevation: 0),
                  onPressed: () {
                    selectedDropdown = null;
                    searchCtrl.clear();
                    getSearchedData('');
                  },
                  child: Text("Clear"))
            ]
          ],
        ),
        const SizedBox(height: 25),
        const SessionTableHeader(),
        filteredSessionList.isEmpty
            ? Container(
                padding: const EdgeInsets.symmetric(vertical: 15),
                decoration: const BoxDecoration(
                    border: Border(
                        bottom: BorderSide(color: dividerColor),
                        left: BorderSide(color: dividerColor),
                        right: BorderSide(color: dividerColor)),
                    borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8))),
                child: Center(
                  child: Text(
                    'No Data Available',
                    style: TextStyle(
                        color: Color(0xff737373),
                        fontSize: 14.5,
                        fontWeight: FontWeight.w500),
                  ),
                ),
              )
            : Column(
                children: [
                  ...List.generate(
                    filteredSessionList.length,
                    (index) {
                      return SessionTile(
                          isLast: index == (filteredSessionList.length - 1),
                          sessionModel: filteredSessionList[index],
                          index: index);
                    },
                  ),
                ],
              ),
      ],
    );
  }
}
