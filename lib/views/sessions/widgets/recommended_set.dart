import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_network/image_network.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/controller/recommended_set_controller.dart';
import 'package:wedding_super_admin/models/session_model.dart';

import '../../../models/product.dart';
import '../../../models/subcategories.dart';
import '../../../models/variants.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';

const int perPageProducts = 15;

class RecommendedSetSelection extends StatefulWidget {
  const RecommendedSetSelection(
      {super.key, this.stylerProducts, required this.sessionId});

  final StylerProducts? stylerProducts;
  final String sessionId;
  @override
  State<RecommendedSetSelection> createState() =>
      _RecommendedSetSelectionState();
}

class _RecommendedSetSelectionState extends State<RecommendedSetSelection> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    final rCtrl = Get.find<RecommendedSetController>();

    rCtrl.getData(widget.stylerProducts);
    print('rCtrl.selectedVariants.length: ${rCtrl.selectedVariants.length}');
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RecommendedSetController>(
      builder: (rCtrl) {
        print(
            'rCtrl.selectedVariants.length: ${rCtrl.selectedVariants.length}');
        rCtrl.selectedSubCategories.clear();

        for (var ele in rCtrl.selectedVariants) {
          final foundSubCat = Get.find<HomeCtrl>()
              .subcategories
              .firstWhereOrNull(
                  (element) => element.docId == ele.selectedVaraint.subCatId);
          rCtrl.selectedSubCategories.addIf(
              (foundSubCat != null &&
                  !(rCtrl.selectedSubCategories
                      .map((e) => e.docId)
                      .contains(foundSubCat.docId))),
              foundSubCat!);
        }
        rCtrl.selectedSubCategories.sort((a, b) => a.name.compareTo(b.name));

        return rCtrl.dataLoading
            ? const Center(
                child: SizedBox(
                  height: 50,
                  width: 50,
                  child: CircularProgressIndicator(),
                ),
              )
            : Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(
                          vertical: 20, horizontal: 10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              IconButton(
                                  onPressed: () {
                                    context.pop();
                                  },
                                  icon: const Icon(
                                    CupertinoIcons.arrow_left,
                                    color: themeColor,
                                    size: 25,
                                  )),
                              Text("Selected Products",
                                  style: GoogleFonts.zcoolXiaoWei(
                                    fontSize: 25,
                                    color: themeColor,
                                  )),
                            ],
                          ),

                          // Row(
                          //   children: [
                          //     TextButton.icon(
                          //       icon: const Icon(
                          //           CupertinoIcons.add),
                          //       onPressed: () async {
                          //         // stylerSelectVariantDialog(
                          //         //     context, null);
                          //       },
                          //       label: const Text("Create"),
                          //     ),
                          //   ],
                          // ),
                          // if (stylerproductsList.isNotEmpty) ...[
                          //   const SizedBox(height: 15),
                          //   ...List.generate(
                          //     stylerproductsList.length,
                          //     (index) {
                          //       return ListTile(
                          //         onTap: () {},
                          //         leading: Container(
                          //             padding:
                          //                 const EdgeInsets.all(5),
                          //             decoration: BoxDecoration(
                          //               shape: BoxShape.circle,
                          //               color:
                          //                   Colors.grey.shade100,
                          //             ),
                          //             child: Text((index + 1)
                          //                 .toString())),
                          //         title: Text(
                          //             stylerproductsList[index]
                          //                 .title),
                          //       );
                          //     },
                          //   )
                          // ],
                          // const SizedBox(height: 15),
                          // Text.rich(
                          //   TextSpan(
                          //     children: [
                          //       TextSpan(
                          //           text: "Budget:",
                          //           style: GoogleFonts.poppins(
                          //               fontSize: 13,
                          //               fontWeight:
                          //                   FontWeight.w500)),
                          //       const TextSpan(
                          //           text: ' \$ ',
                          //           style: TextStyle(
                          //               fontSize: 12.5)),
                          //       TextSpan(
                          //           text:
                          //               (sessionData.budget ?? 0)
                          //                   .toStringAsFixed(2)),
                          //     ],
                          //   ),
                          // ),
                          // const SizedBox(height: 10),
                          // Text.rich(
                          //   TextSpan(
                          //     children: [
                          //       TextSpan(
                          //           text: "Used Budget:",
                          //           style: GoogleFonts.poppins(
                          //               fontSize: 13,
                          //               fontWeight:
                          //                   FontWeight.w500)),
                          //       const TextSpan(
                          //           text: ' \$ ',
                          //           style: TextStyle(
                          //               fontSize: 12.5)),
                          //       TextSpan(
                          //           text: currentUsedBudget
                          //               .toStringAsFixed(2)),
                          //     ],
                          //   ),
                          // ),
                          // const SizedBox(height: 10),
                          // Text.rich(
                          //   TextSpan(
                          //     children: [
                          //       TextSpan(
                          //           text: "Remaining Budget:",
                          //           style: GoogleFonts.poppins(
                          //               fontSize: 13,
                          //               fontWeight:
                          //                   FontWeight.w500)),
                          //       const TextSpan(
                          //           text: ' \$ ',
                          //           style: TextStyle(
                          //               fontSize: 12.5)),
                          //       TextSpan(
                          //           text: remainingBudget
                          //               .toStringAsFixed(2)),
                          //     ],
                          //   ),
                          // ),
                          const SizedBox(height: 15),

                          Container(
                            width: double.maxFinite,
                            padding: const EdgeInsets.symmetric(vertical: 7),
                            decoration: BoxDecoration(
                                border: Border.symmetric(
                              horizontal:
                                  BorderSide(color: Colors.grey.shade200),
                            )),
                            child: Text(
                              widget.sessionId,
                              style: GoogleFonts.mulish(
                                fontSize: 15.5,
                                fontWeight: FontWeight.w500,
                                color: const Color(0xffD88F77),
                              ),
                            ),
                          ),
                          // const SizedBox(height: 15),
                          // const Text("Title"),
                          // const SizedBox(height: 5),
                          // Row(
                          //   children: [
                          //     Expanded(
                          //         child: TextFormField(
                          //       controller: rCtrl.titleCtrl,
                          //       decoration: inpDecor().copyWith(
                          //           hintText: ' title',
                          //           hintStyle: const TextStyle(
                          //               color: Color.fromARGB(
                          //                   255, 108, 106, 106))),
                          //     )),
                          //   ],
                          // ),

                          const SizedBox(height: 15),

                          Container(
                              // width: double.maxFinite,
                              // padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 13),
                              // constraints: const BoxConstraints(maxWidth: 451),
                              padding: const EdgeInsets.all(13),
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8)),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 10),
                                  ...List.generate(
                                    rCtrl.selectedSubCategories.length,
                                    (subindex) {
                                      final variants = rCtrl.selectedVariants
                                          .where((element) =>
                                              element
                                                  .selectedVaraint.subCatId ==
                                              rCtrl
                                                  .selectedSubCategories[
                                                      subindex]
                                                  .docId)
                                          .toList();
                                      return Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 30.0),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              rCtrl
                                                  .selectedSubCategories[
                                                      subindex]
                                                  .name,
                                              style: const TextStyle(
                                                  fontSize: 20,
                                                  color: themeColor,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                            const SizedBox(height: 20),
                                            Wrap(
                                              key: ValueKey(DateTime.now()),
                                              spacing: 20, runSpacing: 20,
                                              // maxCrossAxisExtent: 200,
                                              // crossAxisSpacing: 20,
                                              // mainAxisSpacing: 20,
                                              children: [
                                                ...List.generate(
                                                  variants.length,
                                                  (index) {
                                                    return Container(
                                                      height: 250,
                                                      width: getWidth(250, .8),
                                                      clipBehavior:
                                                          Clip.antiAlias,
                                                      decoration: BoxDecoration(
                                                          color: const Color
                                                              .fromARGB(255,
                                                              248, 248, 248),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8)),
                                                      padding:
                                                          const EdgeInsets.all(
                                                              10),
                                                      child: Stack(
                                                        children: [
                                                          Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              Container(
                                                                  height: 180,
                                                                  width:
                                                                      getWidth(
                                                                          250,
                                                                          .8),
                                                                  clipBehavior: Clip
                                                                      .antiAlias,
                                                                  decoration: BoxDecoration(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              7)),
                                                                  child:
                                                                      ImageNetwork(
                                                                    key: ValueKey(
                                                                        DateTime
                                                                            .now()),
                                                                    image: variants[
                                                                            index]
                                                                        .selectedVaraint
                                                                        .images
                                                                        .first,
                                                                    height: 180,
                                                                    width:
                                                                        getWidth(
                                                                      250,
                                                                      .8,
                                                                    ),
                                                                    fitWeb:
                                                                        BoxFitWeb
                                                                            .cover,
                                                                    onLoading:
                                                                        const CircularProgressIndicator(
                                                                      color:
                                                                          themeColor,
                                                                    ),
                                                                  )
                                                                  // Image.network(
                                                                  //   variants[index]
                                                                  //       .selectedVaraint
                                                                  //       .images
                                                                  //       .first,
                                                                  //   fit: BoxFit.cover,
                                                                  // ),
                                                                  ),
                                                              const SizedBox(
                                                                  height: 5),
                                                              Text(
                                                                capilatlizeFirstLetter(variants[
                                                                        index]
                                                                    .selectedVaraint
                                                                    .lowerName),
                                                                maxLines: 2,
                                                                overflow:
                                                                    TextOverflow
                                                                        .ellipsis,
                                                                style: GoogleFonts.mulish(
                                                                    fontSize:
                                                                        14,
                                                                    color: const Color(
                                                                        0xff3E3E3E)),
                                                              ),
                                                            ],
                                                          ),
                                                          Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(5.0),
                                                            child: Align(
                                                              alignment:
                                                                  Alignment
                                                                      .topRight,
                                                              child: InkWell(
                                                                onTap: () {
                                                                  rCtrl.selectedVariants.removeWhere((element) =>
                                                                      element
                                                                          .selectedVaraint
                                                                          .docId ==
                                                                      variants[
                                                                              index]
                                                                          .selectedVaraint
                                                                          .docId);

                                                                  rCtrl
                                                                      .update();
                                                                },
                                                                child:
                                                                    Container(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .all(
                                                                          4),
                                                                  decoration:
                                                                      const BoxDecoration(
                                                                    boxShadow: [
                                                                      BoxShadow(
                                                                        color: Colors
                                                                            .black12,
                                                                        blurRadius:
                                                                            10,
                                                                        offset: Offset(
                                                                            0,
                                                                            2),
                                                                      ),
                                                                    ],
                                                                    color: Colors
                                                                        .white,
                                                                    shape: BoxShape
                                                                        .circle,
                                                                  ),
                                                                  child:
                                                                      const Icon(
                                                                    CupertinoIcons
                                                                        .xmark,
                                                                    size: 15,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          /*  Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(5.0),
                                                            child: Align(
                                                              alignment:
                                                                  Alignment
                                                                      .topLeft,
                                                              child: Checkbox(
                                                                value: rCtrl
                                                                    .selectedVariants
                                                                    .firstWhere((element) =>
                                                                        element
                                                                            .selectedVaraint
                                                                            .docId ==
                                                                        variants[index]
                                                                            .selectedVaraint
                                                                            .docId)
                                                                    .isSelected,
                                                                onChanged:
                                                                    (value) {
                                                                  rCtrl
                                                                      .selectedVariants
                                                                      .firstWhere((element) =>
                                                                          element
                                                                              .selectedVaraint
                                                                              .docId ==
                                                                          variants[index]
                                                                              .selectedVaraint
                                                                              .docId)
                                                                      .isSelected = value ?? false;

                                                                  rCtrl
                                                                      .update();
                                                                },
                                                              ),
                                                            ),
                                                          ),
                                                         */
                                                        ],
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                  const SizedBox(height: 15),
                                  // Text(
                                  //   selectedSubCategories
                                  //       .map((e) => capilatlizeFirstLetter(e.name))
                                  //       .join(', '),
                                  //   style: GoogleFonts.poppins(fontSize: 15),
                                  // ),
                                  // const SizedBox(height: 15),
                                  // Text(
                                  //   selectedSubCategories,
                                  //   style: GoogleFonts.mulish(
                                  //       fontSize: 13, color: const Color(0xff6C6C6C)),
                                  // ),
                                ],
                              )),

                          // const SizedBox(height: 20),
                          Align(
                            alignment: Alignment.centerRight,
                            child: Wrap(
                              runSpacing: 10,
                              spacing: 8,
                              alignment: WrapAlignment.start,
                              runAlignment: WrapAlignment.start,
                              children: [
                                // TextButton(
                                //   onPressed: () {
                                //     Navigator.of(context).pop();
                                //   },
                                //   child: const Text('Cancel'),
                                // ),
                                // const SizedBox(width: 10),
                                /*  OutlinedButton(
                                    onPressed: () async {
                                      List<SelectedProduct> products = [];
                                      // widget.stylerProducts?.selectedProducts ?? [];
                                      for (var element
                                          in rCtrl.selectedVariants) {
                                        bool isPresent = (products
                                            .map((e) => e.variantId)
                                            .contains(
                                                element.selectedVaraint.docId));

                                        if (!isPresent) {
                                          products.add(SelectedProduct(
                                              variantId: element
                                                      .selectedVaraint.docId ??
                                                  '',
                                              isSelected: element.isSelected));
                                        } else {
                                          products.removeWhere((e) =>
                                              e.variantId ==
                                              element.selectedVaraint.docId);
                                          products.add(SelectedProduct(
                                              variantId: element
                                                      .selectedVaraint.docId ??
                                                  '',
                                              isSelected: element.isSelected));
                                        }
                                      }
                                      final data = {
                                        'title': rCtrl.titleCtrl.text,
                                        'products': products
                                            .map((e) => e.toJson())
                                            .toList(),
                                      };
                                      final stylerDoc = widget.stylerProducts !=
                                              null
                                          ? FBFireStore.sessions
                                              .doc(widget.sessionId)
                                              .collection('stylerProducts')
                                              .doc(widget.stylerProducts?.docId)
                                          : FBFireStore.sessions
                                              .doc(widget.sessionId)
                                              .collection('stylerProducts')
                                              .doc();
                                      await stylerDoc.set(data);

                                      // await FBFireStore.sessions
                                      //     .doc(widget.sessionId)
                                      //     .update({
                                      //   'variantIds': FieldValue.arrayUnion(products
                                      //       .map((e) => e.variantId)
                                      //       .toList())
                                      // });
                                      if (context.mounted) {
                                        Navigator.of(context).pop();
                                      }
                                    },
                                    style: OutlinedButton.styleFrom(
                                        side:
                                            const BorderSide(color: themeColor),
                                        foregroundColor: themeColor,
                                        shape: const RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(5)))),
                                    child: const Text("Save")),
                                 */
                                // const SizedBox(width: 10),
                                ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                        overlayColor: Colors.transparent,
                                        backgroundColor: themeColor,
                                        foregroundColor: Colors.white,
                                        shape: const RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(5)))),
                                    onPressed: () async {
                                      List<SelectedProduct> products = widget
                                              .stylerProducts
                                              ?.selectedProducts ??
                                          [];
                                      for (var element
                                          in rCtrl.selectedVariants) {
                                        bool isPresent = products
                                            .map((e) => e.variantId)
                                            .contains(
                                                element.selectedVaraint.docId);

                                        if (!isPresent) {
                                          products.add(SelectedProduct(
                                              variantId: element
                                                      .selectedVaraint.docId ??
                                                  '',
                                              isSelected: false));
                                        }
                                      }

                                      // final data = {
                                      //   'title': rCtrl.titleCtrl.text,
                                      //   'products': products
                                      //       .map((e) => e.toJson())
                                      //       .toList(),
                                      // };

                                      // final stylerDoc = widget.stylerProducts !=
                                      //         null
                                      //     ? FBFireStore.sessions
                                      //         .doc(widget.sessionId)
                                      //         .collection('styler Products')
                                      //         .doc(widget.stylerProducts?.docId)
                                      //     : FBFireStore.sessions
                                      //         .doc(widget.sessionId)
                                      //         .collection('stylerProducts')
                                      //         .doc();

                                      // await stylerDoc.set(data);
                                      final set = {};

                                      for (var element
                                          in rCtrl.selectedVariants) {
                                        if (element.isSelected) {
                                          if (set.containsKey(element
                                              .selectedVaraint.subCatId)) {
                                            (set[element.selectedVaraint
                                                    .subCatId] as List)
                                                .add(element
                                                    .selectedVaraint.docId);
                                          } else {
                                            set.addEntries({
                                              element.selectedVaraint.subCatId:
                                                  [
                                                element.selectedVaraint.docId
                                              ]
                                            }.entries);
                                          }
                                        }
                                      }

                                      Map setData = {};
                                      setData.addEntries({'sets': set}.entries);
                                      setData.addEntries({'desc': ''}.entries);
                                      final recset = {
                                        'data': setData,
                                        'loadingText': "Recommened Sets",
                                        'sendAt': DateTime.now()
                                            .millisecondsSinceEpoch,
                                        'senderId':
                                            FBAuth.auth.currentUser?.uid ?? "",
                                        'sessionId': widget.sessionId,
                                        'type': 'set',
                                      };

                                      await FBFireStore.sessions
                                          .doc(widget.sessionId)
                                          .collection('messages')
                                          .add(recset);

                                      Navigator.of(context).pop();
                                    },
                                    child: const Text("Save & Create Set"))
                              ],
                            ),
                          )

                          /*    selectedVariants.isEmpty
                                ? const Text("No product selected")
                                : Wrap(
                                    // maxCrossAxisExtent: 200,
                                    spacing: 10,
                                    runSpacing: 10,
                                    children: [
                                      ...List.generate(
                                        selectedVariants.length,
                                        (index) {
                                          return Container(
                                            height: 160,
                                            width: 120,
                                            clipBehavior: Clip.antiAlias,
                                            decoration: BoxDecoration(
                                                color: const Color.fromARGB(
                                                    255, 248, 248, 248),
                                                borderRadius:
                                                    BorderRadius.circular(8)),
                                            // padding: const EdgeInsets.all(10),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  height: 110,
                                                  // width: double
                                                  //     .maxFinit,
                                                  width: double.maxFinite,
                                                  clipBehavior: Clip.antiAlias,
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(7)),
                                                  child: ImageNetwork(
                                                    onLoading:
                                                        const CircularProgressIndicator(
                                                      color: themeColor,
                                                    ),
                                                    image: selectedVariants[index]
                                                        .selectedVaraint
                                                        .images
                                                        .first,
                                                    height: 110,
                                                    width: 120,
                                                    fitWeb: BoxFitWeb.cover,
                                                  ),
                                                  //     Image.network(
                                                  //   selectedUserVarinats[
                                                  //           index]
                                                  //       .images
                                                  //       .first,
                                                  //   fit: BoxFit
                                                  //       .cover,
                                                  // ),
                                                ),
                                                const SizedBox(height: 8),
                                                Expanded(
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                            horizontal: 10.0),
                                                    child: Text(
                                                      capilatlizeFirstLetter(
                                                        selectedVariants[index]
                                                            .selectedVaraint
                                                            .lowerName,
                                                      ),
                                                      maxLines: 2,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      style: GoogleFonts.mulish(
                                                          fontSize: 12,
                                                          color: const Color(
                                                              0xff3E3E3E)),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                           */
                        ],
                      ),
                    ),
                  ),
                  const VerticalDivider(width: 4),
                  Expanded(
                    flex: 3,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(
                          vertical: 20, horizontal: 18),
                      // constraints: const BoxConstraints(maxWidth: double.maxFinite),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              // InkWell(
                              //   hoverColor: Colors.white,
                              //   splashColor: Colors.white,
                              //   highlightColor: Colors.white,
                              //   onTap: () {},
                              //   child: const Icon(
                              //     CupertinoIcons.arrow_left,
                              //     size: 20,
                              //   ),
                              // ),
                              // const SizedBox(width: 20),
                              Text(
                                'Search Products',
                                style: GoogleFonts.zcoolXiaoWei(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 25,
                                  color: const Color(0xff3E3E3E),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 15),
                          true
                              ? Row(
                                  children: [
                                    Expanded(
                                      child: TextFormField(
                                        controller: rCtrl.searchCtrl,
                                        decoration: inpDecor().copyWith(
                                            hintText: ' Search',
                                            hintStyle: TextStyle(
                                                color: Color(0xff737373),
                                                fontSize: 14.5,
                                                fontWeight: FontWeight.w500)),
                                        onChanged: (value) async {
                                          await rCtrl.getSearchedData(
                                              value.toLowerCase().trim());
                                        },
                                      ),
                                    ),
                                  ],
                                )
                              : Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Expanded(
                                      // flex: 2,
                                      child: Autocomplete<NewVariantModel>(
                                        fieldViewBuilder: (context,
                                            textEditingController,
                                            focusNode,
                                            onFieldSubmitted) {
                                          return TextFormField(
                                            focusNode: focusNode,
                                            onFieldSubmitted: (value) {
                                              onFieldSubmitted();
                                            },
                                            onChanged: (value) {
                                              rCtrl.getSearchedData(
                                                  textEditingController.text
                                                      .toLowerCase()
                                                      .trim());
                                            },
                                            controller: textEditingController,
                                            decoration: inpDecor().copyWith(
                                              hintText: ' search products',
                                              hintStyle: const TextStyle(
                                                fontSize: 14,
                                              ),
                                            ),
                                          );
                                        },
                                        displayStringForOption: (option) {
                                          return '';
                                        },
                                        optionsViewBuilder:
                                            (context, onSelected, options) {
                                          final optionVariants =
                                              options.toList();
                                          return Container(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 15),
                                            decoration: const BoxDecoration(
                                                color: Colors.white),
                                            child: StaggeredGrid.extent(
                                              maxCrossAxisExtent: 200,
                                              mainAxisSpacing: 15,
                                              crossAxisSpacing: 15,
                                              children: [
                                                ...List.generate(
                                                  optionVariants.length,
                                                  (index) {
                                                    return true
                                                        ? Container(
                                                            height: 160,
                                                            width: 120,
                                                            clipBehavior:
                                                                Clip.antiAlias,
                                                            decoration: BoxDecoration(
                                                                color: const Color
                                                                    .fromARGB(
                                                                    255,
                                                                    248,
                                                                    248,
                                                                    248),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            8)),
                                                            // padding: const EdgeInsets.all(10),
                                                            child: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Container(
                                                                  height: 110,
                                                                  // width: double
                                                                  //     .maxFinit,
                                                                  width: double
                                                                      .maxFinite,
                                                                  clipBehavior:
                                                                      Clip.antiAlias,
                                                                  decoration: BoxDecoration(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              7)),
                                                                  child:
                                                                      ImageNetwork(
                                                                    onLoading:
                                                                        const CircularProgressIndicator(
                                                                      color:
                                                                          themeColor,
                                                                    ),
                                                                    image: optionVariants[
                                                                            index]
                                                                        .images
                                                                        .first,
                                                                    height: 110,
                                                                    width: 120,
                                                                    fitWeb:
                                                                        BoxFitWeb
                                                                            .cover,
                                                                  ),
                                                                  //     Image.network(
                                                                  //   selectedUserVarinats[
                                                                  //           index]
                                                                  //       .images
                                                                  //       .first,
                                                                  //   fit: BoxFit
                                                                  //       .cover,
                                                                  // ),
                                                                ),
                                                                const SizedBox(
                                                                    height: 8),
                                                                Expanded(
                                                                  child:
                                                                      Padding(
                                                                    padding: const EdgeInsets
                                                                        .symmetric(
                                                                        horizontal:
                                                                            10.0),
                                                                    child: Text(
                                                                      capilatlizeFirstLetter(
                                                                        optionVariants[index]
                                                                            .lowerName,
                                                                      ),
                                                                      maxLines:
                                                                          2,
                                                                      overflow:
                                                                          TextOverflow
                                                                              .ellipsis,
                                                                      style: GoogleFonts.mulish(
                                                                          fontSize:
                                                                              12,
                                                                          color:
                                                                              const Color(0xff3E3E3E)),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          )
                                                        : ListTile(
                                                            onTap: () {
                                                              onSelected(
                                                                  optionVariants[
                                                                      index]);
                                                            },
                                                            leading: Container(
                                                                height: 30,
                                                                width: 30,
                                                                clipBehavior: Clip
                                                                    .antiAlias,
                                                                decoration:
                                                                    const BoxDecoration(
                                                                  shape: BoxShape
                                                                      .circle,
                                                                ),
                                                                child:
                                                                    ImageNetwork(
                                                                  image: optionVariants[
                                                                          index]
                                                                      .images
                                                                      .first,
                                                                  height: 30,
                                                                  width: 30,
                                                                  onLoading:
                                                                      const CircularProgressIndicator(
                                                                    color:
                                                                        themeColor,
                                                                  ),
                                                                )
                                                                //    Image.network(
                                                                //       optionVariants[index].images.first),
                                                                ),
                                                            title: Text(capilatlizeFirstLetter(
                                                                optionVariants[
                                                                        index]
                                                                    .lowerName)),
                                                          );
                                                  },
                                                ),
                                              ],
                                            ),

                                            /*    Column(
                                          children: [
                                            ...List.generate(
                                              optionVariants.length,
                                              (index) {
                                                return ListTile(
                                                  onTap: () {
                                                    onSelected(
                                                        optionVariants[index]);
                                                  },
                                                  leading: Container(
                                                      height: 30,
                                                      width: 30,
                                                      clipBehavior: Clip.antiAlias,
                                                      decoration:
                                                          const BoxDecoration(
                                                        shape: BoxShape.circle,
                                                      ),
                                                      child: ImageNetwork(
                                                        image: optionVariants[index]
                                                            .images
                                                            .first,
                                                        height: 30,
                                                        width: 30,
                                                        onLoading:
                                                            const CircularProgressIndicator(
                                                          color: themeColor,
                                                        ),
                                                      )
                                                      //    Image.network(
                                                      //       optionVariants[index].images.first),
                                                      ),
                                                  title: Text(
                                                      capilatlizeFirstLetter(
                                                          optionVariants[index]
                                                              .lowerName)),
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      */
                                          );
                                        },
                                        optionsBuilder: (textEditingValue) {
                                          return rCtrl.getSearchedData(
                                              textEditingValue.text
                                                  .toLowerCase()
                                                  .trim());
                                        },
                                        onSelected: (option) {
                                          rCtrl.selectedVariants.add(
                                              StylerProdSelection(
                                                  isSelected: false,
                                                  selectedVaraint: option));

                                          setState(() {});
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                          const SizedBox(height: 15),
                          if (rCtrl.searchCtrl.text.trim().isNotEmpty)
                            rCtrl.searchedVariants.isEmpty
                                ? const Center(child: Text("No Product Found"))
                                : Wrap(
                                    key: rCtrl.searchKey,
                                    // maxCrossAxisExtent: 200,
                                    runSpacing: 15,
                                    spacing: 15,
                                    children: [
                                      ...List.generate(
                                        rCtrl.searchedVariants.length,
                                        (index) {
                                          return InkWell(
                                            onTap: () {
                                              final res = rCtrl.selectedVariants
                                                  .map((e) =>
                                                      e.selectedVaraint.docId);

                                              if (!(res.contains(rCtrl
                                                  .searchedVariants[index]
                                                  .docId))) {
                                                rCtrl.selectedVariants
                                                    .add(StylerProdSelection(
                                                  isSelected: true,
                                                  selectedVaraint: rCtrl
                                                      .searchedVariants[index],
                                                ));
                                                rCtrl.update();
                                              } else {
                                                showErrorAppSnackBar(
                                                    context, 'Already Exist');
                                              }
                                            },
                                            child: Container(
                                              height: 160,
                                              width: 120,
                                              clipBehavior: Clip.antiAlias,
                                              decoration: BoxDecoration(
                                                  color: const Color.fromARGB(
                                                      255, 248, 248, 248),
                                                  borderRadius:
                                                      BorderRadius.circular(8)),
                                              // padding: const EdgeInsets.all(10),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    height: 110,
                                                    // width: double
                                                    //     .maxFinit,
                                                    width: double.maxFinite,
                                                    clipBehavior:
                                                        Clip.antiAlias,
                                                    decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(7)),
                                                    child: IgnorePointer(
                                                      ignoring: true,
                                                      child: ImageNetwork(
                                                        key: ValueKey(
                                                            DateTime.now()),
                                                        onLoading:
                                                            const CircularProgressIndicator(
                                                          color: themeColor,
                                                        ),
                                                        image: rCtrl
                                                            .searchedVariants[
                                                                index]
                                                            .images
                                                            .first,
                                                        height: 110,
                                                        width: 120,
                                                        fitWeb: BoxFitWeb.cover,
                                                      ),
                                                    ),
                                                    //     Image.network(
                                                    //   selectedUserVarinats[
                                                    //           index]
                                                    //       .images
                                                    //       .first,
                                                    //   fit: BoxFit
                                                    //       .cover,
                                                    // ),
                                                  ),
                                                  const SizedBox(height: 8),
                                                  Expanded(
                                                    child: Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 10.0),
                                                      child: Text(
                                                        capilatlizeFirstLetter(
                                                          rCtrl
                                                              .searchedVariants[
                                                                  index]
                                                              .lowerName,
                                                        ),
                                                        maxLines: 2,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: GoogleFonts.mulish(
                                                            fontSize: 12,
                                                            color: const Color(
                                                                0xff3E3E3E)),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                          if (rCtrl.searchCtrl.text.trim().isEmpty &&
                              rCtrl.selMainCategory == null)
                            const RecommendedCategorySection(),
                          if (rCtrl.searchCtrl.text.trim().isEmpty &&
                              rCtrl.selMainCategory != null &&
                              rCtrl.selSubCategory == null)
                            const RecommendedSubCatSection(),
                          if (rCtrl.searchCtrl.text.trim().isEmpty &&
                              rCtrl.selMainCategory != null &&
                              rCtrl.selSubCategory != null)
                            RecommendedAllProductSection(
                                subCategory: rCtrl.selSubCategory!),

                          /*
                            Container(
                                // width: double.maxFinite,
                                // padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 13),
                                // constraints: const BoxConstraints(maxWidth: 451),
                                padding: const EdgeInsets.all(13),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8)),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 10),
                                    ...List.generate(
                                      selectedSubCategories.length,
                                      (subindex) {
                                        final variants = selectedVariants
                                            .where((element) =>
                                                element.selectedVaraint.subCatId ==
                                                selectedSubCategories[subindex]
                                                    .docId)
                                            .toList();
                                        return Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 30.0),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "${selectedSubCategories[subindex].name}",
                                                style: const TextStyle(
                                                    fontSize: 20,
                                                    color: themeColor,
                                                    fontWeight: FontWeight.w500),
                                              ),
                                              const SizedBox(
                                                height: 20,
                                              ),
                                              Wrap(
                                                key: ValueKey(DateTime.now()),
                                                spacing: 20, runSpacing: 20,
                                                // maxCrossAxisExtent: 200,
                                                // crossAxisSpacing: 20,
                                                // mainAxisSpacing: 20,
                                                children: [
                                                  ...List.generate(
                                                    variants.length,
                                                    (index) {
                                                      return Container(
                                                        height: 250,
                                                        width: getWidth(250, .8),
                                                        clipBehavior:
                                                            Clip.antiAlias,
                                                        decoration: BoxDecoration(
                                                            color: const Color
                                                                .fromARGB(
                                                                255, 248, 248, 248),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(8)),
                                                        padding:
                                                            const EdgeInsets.all(
                                                                10),
                                                        child: Stack(
                                                          children: [
                                                            Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              mainAxisSize:
                                                                  MainAxisSize.min,
                                                              children: [
                                                                Container(
                                                                    height: 180,
                                                                    width: getWidth(
                                                                        250, .8),
                                                                    clipBehavior: Clip
                                                                        .antiAlias,
                                                                    decoration: BoxDecoration(
                                                                        borderRadius:
                                                                            BorderRadius.circular(
                                                                                7)),
                                                                    child:
                                                                        ImageNetwork(
                                                                      image: variants[
                                                                              index]
                                                                          .selectedVaraint
                                                                          .images
                                                                          .first,
                                                                      height: 180,
                                                                      width:
                                                                          getWidth(
                                                                        250,
                                                                        .8,
                                                                      ),
                                                                      fitWeb:
                                                                          BoxFitWeb
                                                                              .cover,
                                                                      onLoading:
                                                                          const CircularProgressIndicator(
                                                                        color:
                                                                            themeColor,
                                                                      ),
                                                                    )
                                                                    // Image.network(
                                                                    //   variants[index]
                                                                    //       .selectedVaraint
                                                                    //       .images
                                                                    //       .first,
                                                                    //   fit: BoxFit.cover,
                                                                    // ),
                                                                    ),
                                                                const SizedBox(
                                                                    height: 5),
                                                                Text(
                                                                  capilatlizeFirstLetter(
                                                                      variants[
                                                                              index]
                                                                          .selectedVaraint
                                                                          .lowerName),
                                                                  maxLines: 2,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis,
                                                                  style: GoogleFonts.mulish(
                                                                      fontSize: 14,
                                                                      color: const Color(
                                                                          0xff3E3E3E)),
                                                                ),
                                                              ],
                                                            ),
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(5.0),
                                                              child: Align(
                                                                alignment: Alignment
                                                                    .topRight,
                                                                child: InkWell(
                                                                  onTap: () {
                                                                    selectedVariants.removeWhere((element) =>
                                                                        element
                                                                            .selectedVaraint
                                                                            .docId ==
                                                                        variants[
                                                                                index]
                                                                            .selectedVaraint
                                                                            .docId);
        
                                                                    setState(() {});
                                                                  },
                                                                  child: Container(
                                                                    padding:
                                                                        const EdgeInsets
                                                                            .all(4),
                                                                    decoration:
                                                                        const BoxDecoration(
                                                                      boxShadow: [
                                                                        BoxShadow(
                                                                          color: Colors
                                                                              .black12,
                                                                          blurRadius:
                                                                              10,
                                                                          offset:
                                                                              Offset(
                                                                                  0,
                                                                                  2),
                                                                        ),
                                                                      ],
                                                                      color: Colors
                                                                          .white,
                                                                      shape: BoxShape
                                                                          .circle,
                                                                    ),
                                                                    child:
                                                                        const Icon(
                                                                      CupertinoIcons
                                                                          .xmark,
                                                                      size: 15,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(5.0),
                                                              child: Align(
                                                                alignment: Alignment
                                                                    .topLeft,
                                                                child: Checkbox(
                                                                  value: selectedVariants
                                                                      .firstWhere((element) =>
                                                                          element
                                                                              .selectedVaraint
                                                                              .docId ==
                                                                          variants[
                                                                                  index]
                                                                              .selectedVaraint
                                                                              .docId)
                                                                      .isSelected,
                                                                  onChanged:
                                                                      (value) {
                                                                    selectedVariants
                                                                        .firstWhere((element) =>
                                                                            element
                                                                                .selectedVaraint
                                                                                .docId ==
                                                                            variants[index]
                                                                                .selectedVaraint
                                                                                .docId)
                                                                        .isSelected = value ?? false;
        
                                                                    setState(
                                                                      () {},
                                                                    );
                                                                  },
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                    const SizedBox(height: 15),
                                    // Text(
                                    //   selectedSubCategories
                                    //       .map((e) => capilatlizeFirstLetter(e.name))
                                    //       .join(', '),
                                    //   style: GoogleFonts.poppins(fontSize: 15),
                                    // ),
                                    // const SizedBox(height: 15),
                                    // Text(
                                    //   selectedSubCategories,
                                    //   style: GoogleFonts.mulish(
                                    //       fontSize: 13, color: const Color(0xff6C6C6C)),
                                    // ),
                                  ],
                                )),
                            // const SizedBox(height: 15),
                            // StaggeredGrid.extent(
                            //   maxCrossAxisExtent: 200,
                            //   mainAxisSpacing: 15,
                            //   crossAxisSpacing: 10,
                            //   children: [
                            //     ...List.generate(
                            //       selectedVariants.length,
                            //       (index) {
                            //         return Container(
                            //           height: 250,
                            //           clipBehavior: Clip.antiAlias,
                            //           decoration: BoxDecoration(
                            //               color: const Color.fromARGB(255, 248, 248, 248),
                            //               borderRadius: BorderRadius.circular(8)),
                            //           padding: const EdgeInsets.all(10),
                            //           child: Stack(
                            //             children: [
                            //               Column(
                            //                 crossAxisAlignment: CrossAxisAlignment.start,
                            //                 mainAxisSize: MainAxisSize.min,
                            //                 children: [
                            //                   Container(
                            //                     height: 180,
                            //                     width: double.maxFinite,
                            //                     clipBehavior: Clip.antiAlias,
                            //                     decoration: BoxDecoration(
                            //                         borderRadius: BorderRadius.circular(7)),
                            //                     child: Image.network(
                            //                       selectedVariants[index]
                            //                           .selectedVaraint
                            //                           .images
                            //                           .first,
                            //                       fit: BoxFit.cover,
                            //                     ),
                            //                   ),
                            //                   const SizedBox(height: 5),
                            //                   Expanded(
                            //                     child: Text(
                            //                       capilatlizeFirstLetter(
                            //                           selectedVariants[index]
                            //                               .selectedVaraint
                            //                               .lowerName),
                            //                       maxLines: 2,
                            //                       overflow: TextOverflow.ellipsis,
                            //                       style: GoogleFonts.mulish(
                            //                           fontSize: 14,
                            //                           color: const Color(0xff3E3E3E)),
                            //                     ),
                            //                   ),
                            //                 ],
                            //               ),
                            //               Padding(
                            //                 padding: const EdgeInsets.all(5.0),
                            //                 child: Align(
                            //                   alignment: Alignment.topRight,
                            //                   child: InkWell(
                            //                     onTap: () {
                            //                       selectedVariants
                            //                           .remove(selectedVariants[index]);
        
                            //                       setState(() {});
                            //                     },
                            //                     child: Container(
                            //                       padding: const EdgeInsets.all(4),
                            //                       decoration: const BoxDecoration(
                            //                         boxShadow: [
                            //                           BoxShadow(
                            //                             color: Colors.black12,
                            //                             blurRadius: 10,
                            //                             offset: Offset(0, 2),
                            //                           ),
                            //                         ],
                            //                         color: Colors.white,
                            //                         shape: BoxShape.circle,
                            //                       ),
                            //                       child: const Icon(
                            //                         CupertinoIcons.xmark,
                            //                         size: 15,
                            //                       ),
                            //                     ),
                            //                   ),
                            //                 ),
                            //               ),
                            //               Padding(
                            //                 padding: const EdgeInsets.all(5.0),
                            //                 child: Align(
                            //                   alignment: Alignment.topLeft,
                            //                   child: InkWell(
                            //                       onTap: () {
                            //                         selectedVariants
                            //                             .remove(selectedVariants[index]);
        
                            //                         setState(() {});
                            //                       },
                            //                       child: Checkbox(
                            //                         value:
                            //                             selectedVariants[index].isSelected,
                            //                         onChanged: (value) {
                            //                           selectedVariants[index].isSelected =
                            //                               value ?? false;
        
                            //                           setState(
                            //                             () {},
                            //                           );
                            //                         },
                            //                       )),
                            //                 ),
                            //               ),
                            //             ],
                            //           ),
                            //         );
                            //       },
                            //     ),
                            //   ],
                            // ),
                            // const Spacer(),
                            const SizedBox(height: 20),
                            Align(
                              alignment: Alignment.centerRight,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: const Text('Cancel'),
                                  ),
                                  const SizedBox(width: 10),
                                  ElevatedButton(
                                      onPressed: () async {
                                        List<SelectedProduct> products = [];
                                        // widget.stylerProducts?.selectedProducts ?? [];
                                        for (var element in selectedVariants) {
                                          bool isPresent = (products
                                              .map((e) => e.variantId)
                                              .contains(
                                                  element.selectedVaraint.docId));
        
                                          if (!isPresent) {
                                            products.add(SelectedProduct(
                                                variantId:
                                                    element.selectedVaraint.docId ??
                                                        '',
                                                isSelected: element.isSelected));
                                          } else {
                                            products.removeWhere((e) =>
                                                e.variantId ==
                                                element.selectedVaraint.docId);
                                            products.add(SelectedProduct(
                                                variantId:
                                                    element.selectedVaraint.docId ??
                                                        '',
                                                isSelected: element.isSelected));
                                          }
                                        }
                                        final data = {
                                          'title': titleCtrl.text,
                                          'products': products
                                              .map((e) => e.toJson())
                                              .toList(),
                                        };
                                        final stylerDoc = widget.stylerProducts !=
                                                null
                                            ? FBFireStore.sessions
                                                .doc(widget.sessionId)
                                                .collection('stylerProducts')
                                                .doc(widget.stylerProducts?.docId)
                                            : FBFireStore.sessions
                                                .doc(widget.sessionId)
                                                .collection('stylerProducts')
                                                .doc();
                                        print(data);
                                        await stylerDoc.set(data);
        
                                        // await FBFireStore.sessions
                                        //     .doc(widget.sessionId)
                                        //     .update({
                                        //   'variantIds': FieldValue.arrayUnion(products
                                        //       .map((e) => e.variantId)
                                        //       .toList())
                                        // });
                                        if (context.mounted) {
                                          Navigator.of(context).pop();
                                        }
                                      },
                                      style: ElevatedButton.styleFrom(
                                          shape: const RoundedRectangleBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(5)))),
                                      child: const Text("Save")),
                                  const SizedBox(width: 10),
                                  ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                          shape: const RoundedRectangleBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(5)))),
                                      onPressed: () async {
                                        List<SelectedProduct> products = widget
                                                .stylerProducts?.selectedProducts ??
                                            [];
                                        for (var element in selectedVariants) {
                                          bool isPresent = products
                                              .map((e) => e.variantId)
                                              .contains(
                                                  element.selectedVaraint.docId);
        
                                          if (!isPresent) {
                                            products.add(SelectedProduct(
                                                variantId:
                                                    element.selectedVaraint.docId ??
                                                        '',
                                                isSelected: false));
                                          }
                                        }
                                        final data = {
                                          'title': titleCtrl.text,
                                          'products': products
                                              .map((e) => e.toJson())
                                              .toList(),
                                        };
                                        final stylerDoc = widget.stylerProducts !=
                                                null
                                            ? FBFireStore.sessions
                                                .doc(widget.sessionId)
                                                .collection('styler Products')
                                                .doc(widget.stylerProducts?.docId)
                                            : FBFireStore.sessions
                                                .doc(widget.sessionId)
                                                .collection('stylerProducts')
                                                .doc();
                                        await stylerDoc.set(data);
                                        final set = {};
        
                                        for (var element in selectedVariants) {
                                          if (element.isSelected) {
                                            if (set.containsKey(
                                                element.selectedVaraint.subCatId)) {
                                              (set[element.selectedVaraint.subCatId]
                                                      as List)
                                                  .add(element
                                                      .selectedVaraint.docId);
                                            } else {
                                              set.addEntries({
                                                element.selectedVaraint.subCatId: [
                                                  element.selectedVaraint.docId
                                                ]
                                              }.entries);
                                            }
                                          }
                                        }
                                        Map setData = {};
                                        setData.addEntries({'sets': set}.entries);
                                        setData.addEntries({'desc': ''}.entries);
                                        final recset = {
                                          'data': setData,
                                          'loadingText': "Recommened Sets",
                                          'sendAt':
                                              DateTime.now().millisecondsSinceEpoch,
                                          'senderId':
                                              FBAuth.auth.currentUser?.uid ?? "",
                                          'sessionId': widget.sessionId,
                                          'type': 'set',
                                        };
                                        await FBFireStore.sessions
                                            .doc(widget.sessionId)
                                            .collection('messages')
                                            .add(recset);
        
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text("Save & Create Set"))
                                ],
                              ),
                            )
                            */
                        ],
                      ),
                    ),
                  ),
                  if (rCtrl.searchCtrl.text.trim().isEmpty &&
                      rCtrl.selectedProduct != null)
                    const VerticalDivider(width: 4),
                  if (rCtrl.searchCtrl.text.trim().isEmpty &&
                      rCtrl.selectedProduct != null)
                    const Expanded(child: ProductDisplayWid())
                ],
              );
      },
    );
  }
}

class RecommendedCategorySection extends StatelessWidget {
  const RecommendedCategorySection({super.key});
  @override
  Widget build(BuildContext context) {
    return GetBuilder<RecommendedSetController>(
      builder: (rCtrl) {
        final allMainCategories = Get.find<HomeCtrl>().offerings;
        return StaggeredGrid.extent(
          mainAxisSpacing: 15,
          crossAxisSpacing: 15,
          maxCrossAxisExtent: 250,
          children: [
            ...List.generate(allMainCategories.length, (index) {
              return AspectRatio(
                aspectRatio: 1,
                child: InkWell(
                  hoverColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor:
                      const WidgetStatePropertyAll(Colors.transparent),
                  onTap: () {
                    rCtrl.selMainCategory = allMainCategories[index];
                    rCtrl.update();
                  },
                  child: Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        CachedNetworkImage(
                          imageUrl: allMainCategories[index].image,
                          fit: BoxFit.cover,
                          placeholder: (context, url) {
                            return const Icon(CupertinoIcons.camera);
                          },
                        ),

                        // FadeInImage.memoryNetwork(
                        //   image: category.image,
                        //   fit: BoxFit.cover,
                        //   placeholder: kTransparentImage,
                        // ),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            alignment: Alignment.bottomCenter,
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                stops: [
                                  0.07,
                                  0.12,
                                  0.15,
                                  // 0.2,
                                  0.3,
                                ],
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                colors: [
                                  // Color.fromARGB(255, 41, 40, 40),
                                  // Color.fromARGB(179, 41, 40, 40),
                                  Color.fromARGB(210, 41, 40, 40),
                                  Colors.black45,
                                  // Colors.black38,
                                  Color.fromARGB(79, 0, 0, 0),
                                  // Colors.white38,
                                  Colors.transparent,
                                ],
                              ),
                            ),
                            child: Align(
                              alignment: Alignment.bottomLeft,
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    left: 15, bottom: 8, right: 15),
                                child: Text(
                                  capilatlizeFirstLetter(
                                      allMainCategories[index].name),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                      color: Colors.white, fontSize: 18),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            })
          ],
        );
      },
    );
  }
}

class RecommendedSubCatSection extends StatelessWidget {
  const RecommendedSubCatSection({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RecommendedSetController>(
      builder: (rCtrl) {
        final allSubCat = Get.find<HomeCtrl>().subcategories;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                InkWell(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    rCtrl.selMainCategory = null;
                    rCtrl.update();
                  },
                  child: const Icon(
                    CupertinoIcons.arrow_left,
                    size: 23,
                  ),
                ),
                const SizedBox(width: 13),
                Text(
                  capilatlizeFirstLetter(rCtrl.selMainCategory?.name ?? ''),
                  style:
                      GoogleFonts.zcoolXiaoWei(fontSize: 23, color: themeColor),
                )
              ],
            ),
            const SizedBox(height: 15),
            StaggeredGrid.extent(
              maxCrossAxisExtent: 230,
              mainAxisSpacing: 15,
              crossAxisSpacing: 15,
              children: [
                ...List.generate(
                  allSubCat.length,
                  (index) {
                    return InkWell(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      onTap: () {
                        rCtrl.selSubCategory = allSubCat[index];
                        rCtrl.getFilterData(allSubCat[index]);
                        rCtrl.update();
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 250,
                            // clipBehavior: Clip.antiAlias,
                            // decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
                            child: CachedNetworkImage(
                              imageUrl: allSubCat[index].image,
                              width: double.maxFinite,
                              errorWidget: (context, url, error) {
                                return const Icon(CupertinoIcons.camera);
                              },
                              placeholder: (context, url) {
                                return Center(
                                  child:
                                      LoadingAnimationWidget.staggeredDotsWave(
                                          color: Colors.black54, size: 20),
                                );
                              },
                              fit: BoxFit.cover,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            capilatlizeFirstLetter(allSubCat[index].name),
                            style: GoogleFonts.zcoolXiaoWei(
                              letterSpacing: 1.5,
                              color: const Color(0xff303030),
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          )
                        ],
                      ),
                    );
                  },
                )
              ],
            ),
          ],
        );
      },
    );
  }
}

class RecommendedAllProductSection extends StatefulWidget {
  const RecommendedAllProductSection({super.key, required this.subCategory});
  final SubCategory subCategory;
  @override
  State<RecommendedAllProductSection> createState() =>
      _RecommendedAllProductSectionState();
}

class _RecommendedAllProductSectionState
    extends State<RecommendedAllProductSection> {
  List<ProductDisplayModel> filteredProducts = [];
  bool loaded = false;
  DocumentSnapshot? lastDocument;
  DocumentSnapshot? firstDocument;
  List<AllProductFilter> filterData = [];
  @override
  void initState() {
    super.initState();
    fetchInitialProducts();
  }

  fetchInitialProducts() async {
    loaded = false;
    setState(() {});

    final productSnap = await FBFireStore.products
        .where('subCatDocId', isEqualTo: widget.subCategory.docId)
        // .where("show", isEqualTo: true)
        .limit(perPageProducts)
        .get();
    filteredProducts.clear();
    filteredProducts.addAll(productSnap.docs.map((e) => ProductDisplayModel(
            productId: e.id,
            productName: e['lowerName'],
            image: e['defaultImage'])
        // ProductModel.fromSnap(e)
        ));

    if (filteredProducts.isNotEmpty) {
      firstDocument = productSnap.docs.first;
      lastDocument = productSnap.docs.last;
    }
    loaded = true;
    setState(() {});
  }

  fetchNextProducts() async {
    if (lastDocument != null) {
      loaded = false;
      setState(() {});

      final productSnap = await FBFireStore.products
          .where('subCatDocId', isEqualTo: widget.subCategory.docId)

          // .where("show", isEqualTo: true)
          .startAfterDocument(lastDocument!)
          .limit(perPageProducts)
          .get();

      if (productSnap.size != 0) {
        filteredProducts.clear();
        filteredProducts.addAll(productSnap.docs.map((e) => ProductDisplayModel(
            productId: e.id,
            productName: e['lowerName'],
            image: e['defaultImage'])));

        firstDocument = productSnap.docs.first;
        lastDocument = productSnap.docs.last;
      }

      loaded = true;
      setState(() {});
    }
  }

  fetchPreviousProducts() async {
    if (firstDocument != null) {
      loaded = false;
      setState(() {});

      final productSnap = await FBFireStore.products
          .where('subCatDocId', isEqualTo: widget.subCategory.docId)
          // .where("show", isEqualTo: true)
          .endBeforeDocument(firstDocument!)
          .limit(perPageProducts)
          .get();

      if (productSnap.size != 0) {
        filteredProducts.clear();
        filteredProducts.addAll(productSnap.docs.map((e) => ProductDisplayModel(
            productId: e.id,
            productName: e['lowerName'],
            image: e['defaultImage'])));

        firstDocument = productSnap.docs.first;
        lastDocument = productSnap.docs.last;
      }

      loaded = true;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final rCtrl = Get.find<RecommendedSetController>();
    filterData = rCtrl.allProductType;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () {
                rCtrl.selSubCategory = null;
                rCtrl.update();
              },
              child: const Icon(
                CupertinoIcons.arrow_left,
                size: 23,
              ),
            ),
            const SizedBox(width: 13),
            Text(
              capilatlizeFirstLetter(rCtrl.selSubCategory?.name ?? ''),
              style: GoogleFonts.zcoolXiaoWei(fontSize: 23, color: themeColor),
            )
          ],
        ),
        const SizedBox(height: 15),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (filterData.isNotEmpty) ...[
              Expanded(
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      width: double.maxFinite,
                      decoration: const BoxDecoration(
                          border: Border(
                        left: BorderSide(color: Color(0xffCBCBCB), width: 1),
                        right: BorderSide(color: Color(0xffCBCBCB), width: 1),
                        top: BorderSide(color: Color(0xffCBCBCB), width: 1),
                      )),
                      child: Text(
                        "Filters",
                        style: GoogleFonts.mulish(
                            color: const Color(0xff6C6C6C),
                            fontSize: 13,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                    Container(
                      width: double.maxFinite,
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          border: Border.all(
                              color: const Color(0xffCBCBCB), width: 1)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...List.generate(
                            filterData.length,
                            (typeIndex) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 15),
                                  Text(
                                    capilatlizeFirstLetter(
                                        filterData[typeIndex].name),
                                    style: GoogleFonts.mulish(
                                      color: const Color(0xff3E3E3E),
                                      fontSize: 13,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Wrap(
                                    spacing: 7,
                                    runSpacing: 10,
                                    alignment: WrapAlignment.start,
                                    runAlignment: WrapAlignment.start,
                                    children: [
                                      ...List.generate(
                                        filterData[typeIndex].value.length,
                                        (index) {
                                          final selected = filterData[typeIndex]
                                              .slelectedFilters
                                              .contains(filterData[typeIndex]
                                                  .value[index]);
                                          return filterChip(
                                            selected,
                                            capilatlizeFirstLetter(
                                                filterData[typeIndex]
                                                    .value[index]),
                                            () {
                                              if (selected) {
                                                filterData[typeIndex]
                                                    .slelectedFilters
                                                    .removeWhere((element) =>
                                                        element ==
                                                        filterData[typeIndex]
                                                            .value[index]);
                                              } else {
                                                filterData[typeIndex]
                                                    .slelectedFilters
                                                    .add(filterData[typeIndex]
                                                        .value[index]);
                                              }
                                              onFilterTap();
                                              setState(() {});
                                            },
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 15),
                                  const Divider(
                                    color: Color(0xffF0F0F0),
                                    height: 0,
                                    thickness: 1,
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 50),
            ],
            Expanded(
                flex: 4,
                child: allProductsDisplay(context: context, isMobile: false))
          ],
        ),
      ],
    );
  }

  Column allProductsDisplay(
      {required BuildContext context, required bool isMobile}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // FILTERS
        Row(
          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              capilatlizeFirstLetter(widget.subCategory.name),
              style: GoogleFonts.zcoolXiaoWei(
                  height: 0, fontSize: 30, fontWeight: FontWeight.w500),
            ),
            const Spacer(flex: 2),
            const Expanded(
              child: SizedBox(
                height: 45,
                // child: searchField(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 30),
        loaded
            ? filteredProducts.isNotEmpty
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...[
                        commonProductDisplayGrid(isMobile, context),
                        const SizedBox(height: 20),
                        if (filteredProducts.length > perPageProducts)
                          Row(
                            children: [
                              IconButton(
                                  onPressed: () async {
                                    fetchPreviousProducts();
                                    // wrapperScrollCtrl.jumpTo(0);
                                  },
                                  icon:
                                      const Icon(CupertinoIcons.chevron_left)),
                              const Spacer(),
                              IconButton(
                                  onPressed: () async {
                                    fetchNextProducts();
                                    // wrapperScrollCtrl.jumpTo(0);
                                  },
                                  icon:
                                      const Icon(CupertinoIcons.chevron_right)),
                            ],
                          )
                      ]
                    ],
                  )
                : const Center(
                    child: Text("No Product"),
                  )
            : const Center(
                child: CircularProgressIndicator(),
              ),
      ],
    );
  }

  Widget commonProductDisplayGrid(bool isMobile, BuildContext context) {
    return StaggeredGrid.extent(
      maxCrossAxisExtent: 200,
      mainAxisSpacing: isMobile ? 20 : 25,
      crossAxisSpacing: isMobile ? 15 : 25,
      children: [
        ...List.generate(
          filteredProducts.length,
          (index) {
            final product = filteredProducts[index];
            // final defaultVarinat =
            // product.variants
            //     .firstWhereOrNull((element) => element.defaultt);
            // final isFavourite =
            //     widget.currentUser?.favourites?.contains(product.productId) ??
            //         false;
            // final imageLink = index % 2 == 0
            //     ? 'assets/images/hero-image.png'
            //     : 'assets/images/hero-image1.png';
            return InkWell(
              onTap: () async {
                // context.go(
                //   "${Routes.product}/${product.productId}/${product.varientId}",
                // );
                final productSnap =
                    await FBFireStore.products.doc(product.productId).get();
                final productModel = ProductModel.fromDocSnap(productSnap);
                final rCtrl = Get.find<RecommendedSetController>();
                rCtrl.selectedProduct = productModel;
                rCtrl.selectedVaraintId = product.varientId;
                rCtrl.getProductVariantData();
                rCtrl.update();
              },
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              child: SizedBox(
                height: isMobile ? 300 : 400,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Stack(
                      children: [
                        // ImageNetwork(
                        //   image: product.image,
                        //   height: isMobile ? 250 : 350,
                        //   width: double.maxFinite,
                        //   duration: 1500,
                        //   curve: Curves.easeIn,
                        //   onPointer: true,
                        //   debugPrint: false,
                        //   backgroundColor: Colors.blue,
                        //   fitAndroidIos: BoxFit.cover,
                        //   fitWeb: BoxFitWeb.cover,
                        //   borderRadius: BorderRadius.circular(70),
                        //   onLoading: const CircularProgressIndicator(
                        //     color: themeColor,
                        //   ),
                        //   onError: const Icon(
                        //     Icons.error,
                        //     color: Colors.red,
                        //   ),
                        //   // onTap: () {
                        //   //   debugPrint("©gabriel_patrick_souza");
                        //   // },
                        // ),
                        IgnorePointer(
                          ignoring: true,
                          child: ImageNetwork(
                            image: product.image,
                            height: 250,
                            width: 250,
                            duration: 1500,
                            curve: Curves.easeIn,
                            onPointer: true,
                            debugPrint: false,
                            backgroundColor: Colors.white,
                            fitAndroidIos: BoxFit.cover,
                            fitWeb: BoxFitWeb.cover,
                            // borderRadius: BorderRadius.circular(70),
                            onLoading: const CircularProgressIndicator(
                              color: themeColor,
                            ),
                            onError: const Icon(
                              Icons.error,
                              color: Colors.red,
                            ),
                            onTap: () {
                              debugPrint("©gabriel_patrick_souza");
                            },
                          ),
                        ),
                        // SizedBox(
                        //   height: isMobile ? 250 : 350,
                        //   width: double.maxFinite,
                        //   child: Image.network(
                        //     product.image,
                        //     fit: BoxFit.cover,
                        //   ),
                        // ),
                        /*    Align(
                          alignment: Alignment.topRight,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 10, top: 10),
                            child: InkWell(
                              onTap: () async {
                                if (widget.currentUser != null) {
                                  if (isFavourite) {
                                    await FBFireStore.users
                                        .doc(widget.currentUser?.docId)
                                        .update({
                                      'favourites': FieldValue.arrayRemove(
                                          [product.productId]),
                                    });
                                  } else {
                                    await FBFireStore.users
                                        .doc(widget.currentUser?.docId)
                                        .update({
                                      'favourites': FieldValue.arrayUnion(
                                          [product.productId]),
                                    });
                                  }
                                } else {
                                  showErrorAppSnackBar(
                                      context, 'No User Exist! Please Login');
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.all(5),
                                decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle),
                                child: Icon(
                                  isFavourite
                                      ? CupertinoIcons.heart_fill
                                      : CupertinoIcons.heart,
                                  size: 17,
                                  color: isFavourite
                                      ? const Color(0xffD88F77)
                                      : const Color(0xff121212),
                                ),
                              ),
                            ),
                          ),
                        ),
                       */
                      ],
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: Text(
                        capilatlizeFirstLetter(product.productName),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: GoogleFonts.zcoolXiaoWei(
                          height: 0,
                          color: const Color(0xff1C1C1C),
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        )
      ],
    );
  }

/*   Widget searchField() {
    // String? searchedRequirement;

    return InkWell(
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () {
        searchDialog(context);
      },
      child: Row(
        children: [
          Expanded(
            /* Autocomplete<SearchModel>(
              optionsBuilder: (textEditingValue) async {
                final searchResults =
                    await getSearchResultData(textEditingValue.text);
                return searchResults;
              },
              optionsViewBuilder: (context, onSelected, options) {
                return Align(
                  alignment: AlignmentDirectional.topStart,
                  child: Material(
                    elevation: 4.0,
                    child: Container(
                      constraints: const BoxConstraints(
                        maxHeight: 300,
                        maxWidth: 300,
                        // dropWidth.toDouble(),
                      ),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                      ),
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemCount: options.length,
                        itemBuilder: (BuildContext context, int index) {
                          final option = options.elementAt(index);
                          return InkWell(
                            onTap: () {
                              onSelected(option);
                            },
                            child: Builder(builder: (BuildContext context) {
                              final bool highlight =
                                  AutocompleteHighlightedOption.of(context) ==
                                      index;
                              // if (highlight) {
                              //   SchedulerBinding.instance.addPostFrameCallback(
                              //       (Duration timeStamp) {
                              //     Scrollable.ensureVisible(context,
                              //         alignment: 0.5);
                              //   },
                              //       debugLabel:
                              //           'AutocompleteOptions.ensureVisible');
                              // }
                              return Container(
                                color: highlight
                                    ? Theme.of(context).focusColor
                                    : null,
                                // color: Colors.white,
                                padding: const EdgeInsets.all(10.0),
                                child: Row(
                                  children: [
                                    Container(
                                        height: 40,
                                        // width: 30,
                                        clipBehavior: Clip.antiAlias,
                                        decoration: const BoxDecoration(
                                            shape: BoxShape.circle),
                                        child:
                                            Image.network(option.displayImage)),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      child: Text(
                                        capilatlizeFirstLetter(
                                            option.displayName),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
              displayStringForOption: (option) =>
                  capilatlizeFirstLetter(option.displayName),
              fieldViewBuilder:
                  (context, textEditingController, focusNode, onFieldSubmitted) {
                return TextFormField(
                  // key: srchKey,
                  onFieldSubmitted: (value) {
                    onFieldSubmitted();
                  },
                  onChanged: (value) {
                    getSearchResultData(value);
                  },
                  focusNode: focusNode,
                  controller: textEditingController,
                  cursorColor: const Color(0xff4F4F4F),
                  decoration: InputDecoration(
                    hoverColor: Colors.transparent,
                    contentPadding: const EdgeInsets.fromLTRB(15, 15, 0, 15),
                    hintText: ' Search',
                    hintStyle: GoogleFonts.mulish(
                        fontSize: 14, color: const Color(0xff828282)),
                    border: const OutlineInputBorder(
                        borderRadius: BorderRadius.zero,
                        borderSide: BorderSide(color: Color(0xffBDBDBD))),
                    enabledBorder: const OutlineInputBorder(
                        borderRadius: BorderRadius.zero,
                        borderSide: BorderSide(color: Color(0xffBDBDBD))),
                    focusedBorder: const OutlineInputBorder(
                        borderRadius: BorderRadius.zero,
                        borderSide: BorderSide(color: Color(0xffBDBDBD))),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                );
              },
            ),
             */
            child: TextFormField(
              maxLines: 1,
              enabled: false,
              decoration: InputDecoration(
                hintText: ' Search',
                hintStyle: GoogleFonts.mulish(
                    fontSize: 14, color: const Color(0xff828282)),
                border: const OutlineInputBorder(
                    borderRadius: BorderRadius.zero,
                    borderSide: BorderSide(color: Color(0xffBDBDBD))),
                enabledBorder: const OutlineInputBorder(
                    borderRadius: BorderRadius.zero,
                    borderSide: BorderSide(color: Color(0xffBDBDBD))),
                disabledBorder: const OutlineInputBorder(
                    borderRadius: BorderRadius.zero,
                    borderSide: BorderSide(color: Color(0xffBDBDBD))),
                focusedBorder: const OutlineInputBorder(
                    borderRadius: BorderRadius.zero,
                    borderSide: BorderSide(color: Color(0xffBDBDBD))),
              ),
            ),
          ),
          Container(
            height: 45,
            constraints: const BoxConstraints(maxWidth: 300),
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(color: themeColor),
            child: const Icon(
              CupertinoIcons.search,
              color: Colors.white,
              size: 22,
            ),
          ),
        ],
      ),
    );
  }
 */
  onFilterTap() async {
    loaded = false;
    setState(() {});

    List<String> selectedTypeName = getSelectedTypeNames();
    selectedTypeName.sort((a, b) => a.compareTo(b));
    List<List<String>> selectedFilterArray = generateCombination(filterData);
    int filterCount = selectedTypeName.length * selectedFilterArray.length;

    if (selectedFilterArray.isEmpty) {
      fetchInitialProducts();
      return;
    }
    List<Filter> multipleAndsFilter =
        createAndFilters(selectedFilterArray, selectedTypeName);

    int breakNumber = calculateBreakNumber(
        selectedTypeName.length, selectedFilterArray.length);

    List<List<Filter>> subMultipleFilter =
        splitFiltersIntoGroups(multipleAndsFilter);

    try {
      List<QuerySnapshot> results = await fetchFilteredResults(
          multipleAndsFilter,
          filterCount,
          breakNumber,
          subMultipleFilter,
          selectedTypeName);

      await processResults(results);

      loaded = true;
      setState(() {});
    } catch (e) {
      loaded = false;
      setState(() {});
      debugPrint(e.toString());
    }
  }

  /// Extracts selected type names from `widget.filterData`
  List<String> getSelectedTypeNames() {
    return filterData
        .where((element) => element.slelectedFilters.isNotEmpty)
        .map((element) => element.name)
        .toList();
  }

  /// Creates AND filters based on selected filter combinations
  List<Filter> createAndFilters(
      List<List<String>> selectedFilterArray, List<String> selectedTypeName) {
    return selectedFilterArray.map((element) {
      List<Filter> singleDetailTypeFilter = [];
      for (int i = 0; i < selectedTypeName.length; i++) {
        singleDetailTypeFilter.add(Filter('detailTypes.${selectedTypeName[i]}',
            isEqualTo: element[i]));
      }
      return singleDetailTypeFilter
          .reduce((value, element) => Filter.and(value, element));
    }).toList();
  }

  /// Determines the breaking point for large filter queries
  int calculateBreakNumber(int selectedTypeCount, int filterArrayLength) {
    int breakNumber = 0;
    int filterCount = selectedTypeCount * filterArrayLength;

    if (filterCount > 99) {
      for (var i = 1; i <= filterArrayLength; i++) {
        if ((selectedTypeCount * i) > 99) {
          breakNumber = i - 1;
          break;
        }
      }
    }
    return breakNumber;
  }

  /// Splits filters into chunks of 30 for Firestore queries
  List<List<Filter>> splitFiltersIntoGroups(List<Filter> multipleAndsFilter) {
    List<List<Filter>> subMultipleFilter = [];
    int noOfLoops = (multipleAndsFilter.length / 30).ceil();

    if (multipleAndsFilter.length > 30) {
      for (var i = 0; i < noOfLoops; i++) {
        subMultipleFilter.add(multipleAndsFilter.sublist(i * 30,
            i == noOfLoops - 1 ? multipleAndsFilter.length : (i * 30) + 30));
      }
    }
    return subMultipleFilter;
  }

  /// Fetches filtered products from Firestore
  Future<List<QuerySnapshot>> fetchFilteredResults(
      List<Filter> multipleAndsFilter,
      int filterCount,
      int breakNumber,
      List<List<Filter>> subMultipleFilter,
      List<String> selectedTypeName) async {
    List<QuerySnapshot> results = [];

    if (multipleAndsFilter.length < 2) {
      results.add(
          await FBFireStore.variants.where(multipleAndsFilter.first).get());
    } else if (multipleAndsFilter.length <= 30) {
      if (filterCount < 100) {
        results.add(await FBFireStore.variants
            .where(multipleAndsFilter.reduce((a, b) => Filter.or(a, b)))
            .get());
      } else {
        results.addAll(await fetchLargeQueries(
            multipleAndsFilter, filterCount, breakNumber));
      }
    } else {
      for (var group in subMultipleFilter) {
        int groupFilterCount = selectedTypeName.length * group.length;
        int groupBreakNumber =
            calculateBreakNumber(selectedTypeName.length, group.length);

        if (groupFilterCount < 100) {
          results.add(await FBFireStore.variants
              .where(group.reduce((a, b) => Filter.or(a, b)))
              .get());
        } else {
          results.addAll(await fetchLargeQueries(
              group, groupFilterCount, groupBreakNumber));
        }
      }
    }
    return results;
  }

  /// Fetches large queries by breaking them into smaller chunks
  Future<List<QuerySnapshot>> fetchLargeQueries(
      List<Filter> filters, int filterCount, int breakNumber) async {
    List<QuerySnapshot> results = [];
    int breakCount = (filterCount / 99).ceil();

    for (var i = 0; i < breakCount; i++) {
      results.add(await FBFireStore.variants
          .where(filters
              .sublist(
                  breakNumber * i,
                  i == (breakCount - 1)
                      ? filters.length
                      : (breakNumber * i) + breakNumber)
              .reduce((a, b) => Filter.or(a, b)))
          .get());
    }
    return results;
  }

  /// Processes Firestore query results and populates `filteredProducts`
  Future<void> processResults(List<QuerySnapshot> results) async {
    filteredProducts.clear();

    for (var element in results) {
      for (var oneDoc in element.docs) {
        filteredProducts.add(ProductDisplayModel(
            varientId: oneDoc.id,
            productId: oneDoc['productId'],
            productName: oneDoc['lowerName'],
            image: (oneDoc['images'] as List).first));
      }
    }
  }
}

class ProductDisplayWid extends StatelessWidget {
  const ProductDisplayWid({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RecommendedSetController>(
      builder: (rCtrl) {
        final product = rCtrl.selectedProduct;

        final variants = rCtrl.selProductVariants;
        final selectedVariant = rCtrl.selectedVaraintId != null
            ? variants.firstWhereOrNull(
                (element) => element.docId == rCtrl.selectedVaraintId)
            : variants.firstWhereOrNull((element) => element.defaultt);

        return variants.isNotEmpty
            ? SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(vertical: 20, horizontal: 18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InkWell(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            rCtrl.selectedProduct = null;
                            rCtrl.update();
                          },
                          child: const Icon(
                            CupertinoIcons.arrow_left,
                            size: 23,
                          ),
                        ),
                        const SizedBox(width: 13),
                        Expanded(
                          child: Text(
                            capilatlizeFirstLetter(product?.name ?? ''),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: GoogleFonts.zcoolXiaoWei(
                                fontSize: 23, color: themeColor),
                          ),
                        )
                      ],
                    ),
                    const SizedBox(height: 20),
                    SizedBox(
                      height: 250,
                      width: double.maxFinite,
                      child: true
                          ? Image.network(
                              selectedVariant?.images.first ?? '',
                              height: double.maxFinite,
                              width: double.maxFinite,
                              fit: BoxFit.cover,
                            )
                          : ImageNetwork(
                              key: ValueKey(DateTime.now()),
                              image: selectedVariant?.images.first ?? '',
                              height: double.maxFinite,
                              width: double.maxFinite,
                              // fitWeb: BoxFitWeb.cover,
                            ),
                    ),
                    const SizedBox(height: 20),
                    Text('${selectedVariant?.detailTypes.values.join(', ')}'),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              overlayColor: Colors.transparent,
                              foregroundColor: Colors.white,
                              backgroundColor: themeColor,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(7)),
                            ),
                            onPressed: () {
                              bool alreadyExist = rCtrl.selectedVariants
                                  .map((e) => e.selectedVaraint.docId)
                                  .contains(selectedVariant?.docId);
                              if (!alreadyExist) {
                                rCtrl.selectedVariants.add(StylerProdSelection(
                                    isSelected: true,
                                    selectedVaraint: selectedVariant!));
                                rCtrl.update();
                              } else {
                                showErrorAppSnackBar(context, 'Already exist');
                              }
                            },
                            child: const Text("Add to Set"),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Wrap(
                      spacing: 15,
                      runSpacing: 15,
                      runAlignment: WrapAlignment.start,
                      alignment: WrapAlignment.start,
                      children: [
                        ...List.generate(
                          variants.length,
                          (index) {
                            bool isSelected =
                                selectedVariant?.docId == variants[index].docId;
                            return InkWell(
                              hoverColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () {
                                rCtrl.selectedVaraintId = variants[index].docId;
                                rCtrl.update();
                              },
                              child: Container(
                                height: 150,
                                width: 100,
                                decoration: BoxDecoration(
                                  border: isSelected
                                      ? Border.all(color: themeColor)
                                      : null,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  children: [
                                    Container(
                                      height: 90,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: const BoxDecoration(
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(8),
                                              topRight: Radius.circular(8))),
                                      child: Image.network(
                                        variants[index].images.first,
                                        width: double.maxFinite,
                                        height: double.maxFinite,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 3.0, vertical: 3),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              variants[index]
                                                  .detailTypes
                                                  .values
                                                  .join(', '),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            );
                          },
                        )
                      ],
                    ),
                  ],
                ),
              )
            : const Center(
                child: CircularProgressIndicator(),
              );
      },
    );
  }
}

Widget filterChip(bool selected, String text, Function()? ontap) {
  return InkWell(
    borderRadius: BorderRadius.circular(20),
    onTap: ontap,
    child: Container(
      padding: const EdgeInsets.symmetric(vertical: 7, horizontal: 10),
      decoration: BoxDecoration(
        color: selected ? const Color(0xffFFE6DF) : Colors.white,
        border: Border.all(color: const Color(0xffBDBDBD)),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            text,
            style: GoogleFonts.mulish(
                color: const Color(0xff828282),
                fontSize: 12,
                height: 0,
                fontWeight: FontWeight.w400),
          ),
          if (selected) ...[
            const SizedBox(width: 5),
            const Center(
              child: Icon(
                CupertinoIcons.xmark,
                size: 14,
                color: Color(0xff747474),
              ),
            ),
          ]
        ],
      ),
    ),
  );
}

List<List<String>> generateCombination(List<AllProductFilter> filterData) {
  List<List<String>> selectedFilterArrayNew = [];
  bool firstTime = true;

  for (var i = 0; i < filterData.length; i++) {
// iterating in filter types

    List<List<String>> temp = [];

    if (filterData[i].slelectedFilters.isNotEmpty) {
// checking if the selected Filters in filterType is Not empty

      for (var value in filterData[i].slelectedFilters) {
// iterating to the selectedFilters of a type

        if (firstTime) {
          selectedFilterArrayNew.add([value]);
        } else {
          for (var j = 0; j < selectedFilterArrayNew.length; j++) {
            List<String> x = [];

            for (var e in selectedFilterArrayNew[j]) {
              x.add(e);
            }
            x.add(value);
            temp.add(x);
          }
        }
      }

      if (!firstTime) {
        selectedFilterArrayNew = temp;
      }

      firstTime = false;
    }
  }
  return selectedFilterArrayNew;
}

double getWidth(double height, double ratio) {
  double width = 0;
  width = ratio * height;
  return width;
}
