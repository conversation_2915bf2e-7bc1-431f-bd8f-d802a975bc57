import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/models/teammodel.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/shared/router.dart';
import 'package:wedding_super_admin/shared/theme.dart';

class SessionParticipantCard extends StatelessWidget {
  const SessionParticipantCard(
      {super.key, required this.teamMember, required this.onChange});
  final TeamModel? teamMember;
  final Function() onChange;
  @override
  Widget build(BuildContext context) {
    return ListTile(
        splashColor: Colors.transparent,
        hoverColor: Colors.transparent,
        onTap: () {
          context.push('${Routes.teamMember}/${teamMember?.docId}');
          /* 
        showDialog(
          context: context,
          builder: (context) {
            return AlertDialog(
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              title: const Text('Member Details'),
              content: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                            text: 'Name: ',
                            style: GoogleFonts.mulish(
                                fontSize: 15, fontWeight: FontWeight.w600)),
                        TextSpan(
                            text: capilatlizeFirstLetter(
                                teamMember?.name ?? "-")),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                            text: 'Mobile: ',
                            style: GoogleFonts.mulish(
                                fontSize: 15, fontWeight: FontWeight.w600)),
                        TextSpan(text: teamMember?.phone),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                            text: 'Email: ',
                            style: GoogleFonts.mulish(
                                fontSize: 15, fontWeight: FontWeight.w600)),
                        TextSpan(text: teamMember?.email),
                      ],
                    ),
                  ),
                  if (teamMember?.userType == UserTypes.styler) ...[
                    const SizedBox(height: 8),
                    Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                              text: 'Speciallity: ',
                              style: GoogleFonts.mulish(
                                  fontSize: 15, fontWeight: FontWeight.w600)),
                          TextSpan(text: teamMember?.speciality),
                        ],
                      ),
                    ),
                  ],
                  const SizedBox(height: 8),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                            text: 'Type: ',
                            style: GoogleFonts.mulish(
                                fontSize: 15, fontWeight: FontWeight.w600)),
                        TextSpan(text: teamMember?.userType),
                      ],
                    ),
                  ),
                ],
              ),
              actions: [
                ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('Done'))
              ],
            );
          },
        );
       */
        },
        // tileColor: dashboardSelectedColor.withOpacity(.25),
        // tileColor: selected ? dashboardSelectedColor.withOpacity(.25) : null,
        minVerticalPadding: 13,
        // shape: selected ? RoundedRectangleBorder() : null,
        leading: CircleAvatar(
          radius: 15,
          backgroundColor: const Color.fromARGB(216, 216, 143, 119),
          child: Text(
            capilatlizeFirstLetter(teamMember!.name.substring(0, 1)),
            style: const TextStyle(color: Colors.white),
          ),
        ),
        title: Text(
          capilatlizeFirstLetter(teamMember!.name),
          style: GoogleFonts.aBeeZee(
              fontSize: 15.5,
              letterSpacing: 1,
              fontWeight: FontWeight.w400,
              color: Colors.black),
        ),
        subtitle: Text(
          capilatlizeFirstLetter(teamMember!.userType),
          maxLines: 1,
          style: GoogleFonts.livvic(fontSize: 13),
          overflow: TextOverflow.ellipsis,
        ),
        trailing: InkWell(
          hoverColor: Colors.transparent,
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          onTap: onChange,
          child: Container(
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                color: dashboardSelectedColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                CupertinoIcons.refresh,
                size: 16,
              )),
        ));
  }
}
