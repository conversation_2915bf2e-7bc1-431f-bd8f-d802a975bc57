import 'package:flutter/material.dart';

class MessageBubbleClipper extends CustomClipper<Path> {
  final bool isSender;

  MessageBubbleClipper({required this.isSender});
  @override
  Path getClip(Size size) {
    Path path = Path();
    const radius = 15.0;

    if (!isSender) {
      // Sender's bubble: rounded rectangle with a tail on the left

      path.lineTo(10, 10);
      path.lineTo(10, size.height - radius);
      path.quadraticBezierTo(10, size.height, 10 + radius, size.height);
      path.lineTo(size.width - radius, size.height);
      path.quadraticBezierTo(
          size.width, size.height, size.width, size.height - radius);
      path.lineTo(size.width, radius);
      path.quadraticBezierTo(size.width, 0, size.width - radius, 0);
      path.lineTo(size.width, 0);
    } else {
      // Receiver's bubble: rounded rectangle with a tail on the right

      path.moveTo(radius, 0);

      path.lineTo(size.width, 0);
      path.lineTo(size.width - 10, 10);
      path.lineTo(size.width - 10, size.height - radius);
      path.quadraticBezierTo(
          size.width - 10, size.height, size.width - radius - 10, size.height);
      path.lineTo(radius, size.height);
      path.quadraticBezierTo(0, size.height, 0, size.height - radius);
      path.lineTo(0, radius);
      path.quadraticBezierTo(0, 0, radius, 0);
    }

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
