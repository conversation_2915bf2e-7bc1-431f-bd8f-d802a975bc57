import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/session_model.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';

import '../../../shared/router.dart';
import '../../../shared/theme.dart';

class SessionTile extends StatefulWidget {
  const SessionTile(
      {super.key,
      required this.sessionModel,
      required this.index,
      required this.isLast});
  final SessionModel sessionModel;
  final int index;
  final bool isLast;
  @override
  State<SessionTile> createState() => _SessionTileState();
}

class _SessionTileState extends State<SessionTile> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    return Container(
      decoration: BoxDecoration(
          border: const Border(
              bottom: BorderSide(color: dividerColor),
              left: BorderSide(color: dividerColor),
              right: BorderSide(color: dividerColor)),
          borderRadius: widget.isLast
              ? const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8))
              : null),
      child: InkWell(
        hoverColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          context.push('${Routes.session}/${widget.sessionModel.docId}');
        },
        child: Row(
          children: [
            SizedBox(
              width: 80,
              child: Text(
                '${widget.index + 1}',
                textAlign: TextAlign.center,
                // style: TextStyle(
                //   fontWeight: FontWeight.w600,
                //   letterSpacing: 1.2,
                // ),
              ),
            ),
            const SizedBox(width: 5),
            Expanded(child: Text(widget.sessionModel.inquiryId)),
            const SizedBox(width: 5),
            Expanded(
                child: Text(capilatlizeFirstLetter(
                    widget.sessionModel.userName.trim().isNotEmpty
                        ? widget.sessionModel.userName.trim()
                        : '-'))),
            const SizedBox(width: 5),

            Expanded(
                child: Text(capilatlizeFirstLetter(ctrl.teamMembers
                        .firstWhereOrNull((element) =>
                            element.docId == widget.sessionModel.consultantId)
                        ?.name ??
                    ""))),
            const SizedBox(width: 5),
            Expanded(
                child: Text(capilatlizeFirstLetter(ctrl.teamMembers
                        .firstWhereOrNull((element) =>
                            element.docId == widget.sessionModel.stylerId)
                        ?.name ??
                    ""))),
            const SizedBox(width: 5),
            Expanded(
                child: Text(widget.sessionModel.isActive ? 'Active' : 'Ended')),
            const SizedBox(width: 5),
            Expanded(
                child: Text(
                    '${widget.sessionModel.createdAt.goodDayDate()} - ${widget.sessionModel.createdAt.goodTime()}')),
            const SizedBox(width: 5),
            // IgnorePointer(
            //   ignoring: true,
            //   child: SizedBox(
            //       width: 60,
            //       child: Transform.scale(
            //         scale: .65,
            //         child: CupertinoSwitch(
            //           value: true,
            //           onChanged: (value) {},
            //         ),
            //       )),
            // ),
            SizedBox(
              width: 60,
              child: IconButton(
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onPressed: () async {
                  context
                      .push('${Routes.session}/${widget.sessionModel.docId}');
                },
                icon: const Icon(
                  Icons.edit,
                  size: 22,
                ),
              ),
            ),
            /*  IgnorePointer(
              ignoring: true,
              child: SizedBox(
                width: 60,
                child: IconButton(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  onPressed: () {},
                  icon: const Icon(
                    Icons.delete,
                    color: themeColor,
                  ),
                ),
              ),
            ), */
          ],
        ),
      ),
    );
  }
}
