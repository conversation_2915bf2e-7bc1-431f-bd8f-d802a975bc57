import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_network/image_network.dart';

import '../../../models/package_model.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';

class VarinatCard extends StatelessWidget {
  const VarinatCard({
    super.key,
    required this.image,
    required this.isCheck,
    required this.onChanged,
    required this.displayName,
    required this.description,
    required this.onTap,
  });
  final String image;
  final String displayName;
  final String description;
  // final SearchModel currentSearchedVarinat;
  final bool isCheck;
  final Function(bool?) onChanged;
  final Function() onTap;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      child: Container(
          height: 210,
          clipBehavior: Clip.antiAlias,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xffF3F3F3),
            borderRadius: BorderRadius.circular(7),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                children: [
                  Container(
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(10)),
                    clipBehavior: Clip.antiAlias,
                    child: ImageNetwork(
                      image: image,
                      height: 130,
                      width: double.maxFinite,
                      fitWeb: BoxFitWeb.cover,
                    ),
                  ),
                  Align(
                    alignment: Alignment.topRight,
                    child: Padding(
                      padding: const EdgeInsets.all(7),
                      child: Checkbox(
                          side: const BorderSide(color: Colors.grey),
                          checkColor: const Color(0xffD88F77),
                          activeColor: Colors.white,
                          fillColor: const WidgetStatePropertyAll(Colors.white),
                          hoverColor: Colors.transparent,
                          // focusColor: Colors.transparent,
                          overlayColor:
                              const WidgetStatePropertyAll(Colors.transparent),
                          value: isCheck,
                          onChanged: onChanged),
                    ),
                  )
                ],
              ),
              const SizedBox(height: 4),
              Text(
                capilatlizeFirstLetter(displayName),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.mulish(
                    fontSize: 15, color: const Color(0xff3E3E3E)),
              ),
              const SizedBox(height: 4),
              Expanded(
                child: Text(
                  description,
                  overflow: TextOverflow.clip,
                  style: GoogleFonts.mulish(
                      fontSize: 12, color: const Color(0xff6C6C6C)),
                ),
              ),
            ],
          )),
    );
  }
}

class SessionPackageCard extends StatelessWidget {
  const SessionPackageCard({
    super.key,
    required this.packageModel,
    required this.isSelected,
    this.onChanged,
  });
  final PackageModel packageModel;
  final bool isSelected;
  final Function(bool?)? onChanged;
  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 200,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      decoration: BoxDecoration(
          border: Border.all(color: const Color(0xffCBCBCB)),
          borderRadius: BorderRadius.circular(7),
          color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                capilatlizeFirstLetter(packageModel.packageName),
              ),
              const SizedBox(width: 10),
              Checkbox(
                  side: const BorderSide(color: Colors.grey),
                  checkColor: Colors.white,
                  activeColor: themeColor,
                  hoverColor: Colors.transparent,
                  // focusColor: Colors.transparent,
                  overlayColor:
                      const WidgetStatePropertyAll(Colors.transparent),
                  value: isSelected,
                  onChanged: onChanged),
            ],
          ),
        ],
      ),
    );
  }
}
