import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_network/image_network.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/display_order_details_model.dart';
import 'package:wedding_super_admin/models/order_model.dart';
import 'package:wedding_super_admin/models/package_model.dart';
import 'package:wedding_super_admin/models/session_model.dart';
import 'package:wedding_super_admin/models/variants.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/shared/router.dart';
import 'package:wedding_super_admin/shared/theme.dart';

import '../../../models/product.dart';
import '../../../models/subcategories.dart';

Widget getMessageUi(
    {required BuildContext context,
    required String messageType,
    required MessagesModel currentMessage}) {
  switch (messageType) {
    case MessageTypes.text:
      return Container(
        constraints: const BoxConstraints(maxWidth: 360),
        padding: const EdgeInsets.symmetric(horizontal: 5),
        child: Text(
          currentMessage.data[messageType],
          style: GoogleFonts.aBeeZee(
            fontSize: 13.5,
          ),
        ),
      );
    case MessageTypes.payment:
      return FutureBuilder<TransactionModel?>(
        future: FBFireStore.transaction
            .doc(currentMessage.data['transactionId'])
            .get()
            .then((value) {
          if (value.exists) {
            return TransactionModel.fromDocSnap(value);
          } else {
            return null;
          }
        }),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return SizedBox(
              height: 25,
              width: 25,
              child: Center(
                child: CircularProgressIndicator(strokeWidth: 3.5),
              ),
            );
          }
          if (snapshot.hasError) {
            debugPrint(snapshot.error.toString());
            return Container(
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(5)),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 13),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Transaction Details",
                    style: GoogleFonts.zcoolXiaoWei(
                        fontSize: 20, color: const Color(0xff3E3E3E)),
                  ),
                  const SizedBox(height: 12),
                  const Text("Something went wrong"),
                ],
              ),
            );
          }
          if (snapshot.hasData) {
            final transactionData = snapshot.data;

            return transactionData != null
                ? InkWell(
                    hoverColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return Dialog(
                            backgroundColor: Colors.white,
                            surfaceTintColor: Colors.white,

                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10)),
                            // insetPadding: EdgeInsets.all(5),
                            child: Container(
                              clipBehavior: Clip.antiAlias,
                              constraints: const BoxConstraints(
                                  maxWidth: 500, maxHeight: 500),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10)),
                              child: SingleChildScrollView(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 15, vertical: 8),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  // mainAxisSize:
                                  //     MainAxisSize.min,
                                  children: [
                                    const SizedBox(height: 10),
                                    Row(
                                      children: [
                                        Text(
                                          'Transaction',
                                          style: GoogleFonts.zcoolXiaoWei(
                                              fontSize: 25, color: themeColor),
                                        ),
                                        Spacer(),
                                      ],
                                    ),
                                    const SizedBox(height: 20),
                                    Row(
                                      children: [
                                        Text.rich(
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                text: 'Order Id',
                                                style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 13.5,
                                                    color: Color(0xff3F3F3F)),
                                              ),
                                              TextSpan(
                                                  text: ': ',
                                                  style: TextStyle(
                                                      fontSize: 13,
                                                      fontWeight:
                                                          FontWeight.bold)),
                                              TextSpan(
                                                text: transactionData.orderId,
                                                style: GoogleFonts.livvic(
                                                    fontSize: 14,
                                                    color: Color(0xff3C3C3C)),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 20),
                                        Spacer(),
                                        Text.rich(TextSpan(children: [
                                          TextSpan(
                                              text: 'Requested on',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 13.5,
                                                  color: Color(0xff3F3F3F))),
                                          TextSpan(
                                              text: ': ',
                                              style: TextStyle(
                                                  fontSize: 13,
                                                  fontWeight: FontWeight.bold)),
                                          TextSpan(
                                            text:
                                                '${transactionData.createdAt.goodDayDate()} ${transactionData.createdAt.goodTime()}',
                                            style: GoogleFonts.livvic(
                                                fontSize: 14,
                                                color: Color(0xff3C3C3C)),
                                          ),
                                        ])),
                                      ],
                                    ),
                                    if (transactionData.isPaid)
                                      SizedBox(height: 8),
                                    if (transactionData.isPaid)
                                      Row(
                                        children: [
                                          Text.rich(
                                            TextSpan(
                                              children: [
                                                TextSpan(
                                                  text: 'Mode',
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 13.5,
                                                      color: Color(0xff3F3F3F)),
                                                ),
                                                TextSpan(
                                                    text: ': ',
                                                    style: TextStyle(
                                                        fontSize: 13,
                                                        fontWeight:
                                                            FontWeight.bold)),
                                                TextSpan(
                                                  text:
                                                      transactionData.method ??
                                                          '-',
                                                  style: GoogleFonts.livvic(
                                                      fontSize: 14,
                                                      color: Color(0xff3C3C3C)),
                                                ),
                                              ],
                                            ),
                                          ),
                                          SizedBox(width: 20),
                                          Spacer(),
                                          Text.rich(TextSpan(children: [
                                            TextSpan(
                                                text: 'Paid on',
                                                style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 13.5,
                                                    color: Color(0xff3F3F3F))),
                                            TextSpan(
                                                text: ': ',
                                                style: TextStyle(
                                                    fontSize: 13,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                            TextSpan(
                                              text:
                                                  '${transactionData.paymentTime?.goodDayDate() ?? '-'} ${transactionData.paymentTime?.goodTime() ?? ''}',
                                              style: GoogleFonts.livvic(
                                                  fontSize: 14,
                                                  color: Color(0xff3C3C3C)),
                                            ),
                                          ])),
                                        ],
                                      ),
                                    SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Text.rich(
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                text: 'Amount',
                                                style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 13.5,
                                                    color: Color(0xff3F3F3F)),
                                              ),
                                              TextSpan(
                                                  text: ': ',
                                                  style: TextStyle(
                                                      fontSize: 13,
                                                      fontWeight:
                                                          FontWeight.bold)),
                                              TextSpan(
                                                text:
                                                    '\$ ${transactionData.amount.toStringAsFixed(2)}',
                                                style: GoogleFonts.livvic(
                                                    fontSize: 14,
                                                    color: Color(0xff3C3C3C)),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 20),
                                        Spacer(),
                                        Text.rich(
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                text: 'Status',
                                                style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 13.5,
                                                    color: Color(0xff3F3F3F)),
                                              ),
                                              TextSpan(
                                                  text: ': ',
                                                  style: TextStyle(
                                                      fontSize: 13,
                                                      fontWeight:
                                                          FontWeight.bold)),
                                              TextSpan(
                                                text: transactionData.isPaid
                                                    ? 'Paid'
                                                    : 'Pending',
                                                style: GoogleFonts.livvic(
                                                    fontSize: 14,
                                                    color: Color(0xff3C3C3C)),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      'Payment Link:',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 13.5,
                                          color: Color(0xff3F3F3F)),
                                    ),
                                    SizedBox(height: 3),
                                    Text(
                                      transactionData.paymentLink,
                                      style: GoogleFonts.livvic(
                                          fontSize: 14,
                                          color: Color(0xff3C3C3C)),
                                    ),
                                    SizedBox(height: 20),
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 40),
                                            backgroundColor: themeColor,
                                            overlayColor: Colors.transparent,
                                            shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8)),
                                          ),
                                          onPressed: () {
                                            Navigator.of(context).pop();
                                          },
                                          child: Text(
                                            'Done',
                                            style: GoogleFonts.livvic(
                                                color: Colors.white,
                                                // fontSize: 15,
                                                fontWeight: FontWeight.w500),
                                          )),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5)),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 13),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            capilatlizeFirstLetter(
                                currentMessage.data['note'].toString()),
                            style: GoogleFonts.mulish(fontSize: 18),
                          ),
                          const SizedBox(height: 5),
                          Text(
                            '\$${currentMessage.data['amount'].toString()}',
                            style: GoogleFonts.livvic(fontSize: 15),
                          ),
                          const SizedBox(height: 7),
                          Text(
                            currentMessage.data['paymentLink'].toString(),
                            style: GoogleFonts.livvic(fontSize: 15),
                          ),
                          const SizedBox(height: 7),
                          Row(
                            children: [
                              Text(
                                transactionData.isPaid ? 'Paid' : 'Unpaid',
                                style: GoogleFonts.livvic(fontSize: 11.5),
                              ),
                              const SizedBox(width: 5),
                              Text(
                                currentMessage.sendAt.goodTime(),
                                style: GoogleFonts.livvic(fontSize: 11.5),
                              ),
                            ],
                          ),
                          /* if (!(transactionData?.isPaid ?? false)) ...[
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          // Expanded(
                          //   child: OutlinedButton(
                          //     style: OutlinedButton.styleFrom(
                          //         elevation: 0,
                          //         side: const BorderSide(color: themeColor),
                          //         foregroundColor: themeColor),
                          //     onPressed: () {},
                          //     child: const Text('Decline'),
                          //   ),
                          // ),
                          // const SizedBox(width: 10),
                          Expanded(
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                  overlayColor: Colors.transparent,
                                  surfaceTintColor: Colors.white,
                                  shadowColor: Colors.transparent,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4)),
                                  backgroundColor: Colors.transparent,
                                  side: BorderSide(color: themeColor),
                                  foregroundColor: Colors.white),
                              onPressed: () async {
                                // await FBFireStore.transaction
                                //     .doc(currentMessage.data['transactionId'])
                                //     .update({
                                //   'isPaid': true,
                                //   'paymentTime':
                                //       DateTime.now().millisecondsSinceEpoch,
                                // });
                              },
                              child: Text(
                                'Pay',
                                style: GoogleFonts.livvic(
                                    color: themeColor, letterSpacing: .7),
                              ),
                            ),
                          ),
                        ],
                      )
                    ] */
                        ],
                      ),
                    ),
                  )
                : Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5)),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 13),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "Transaction Details",
                          style: GoogleFonts.zcoolXiaoWei(
                              fontSize: 20, color: const Color(0xff3E3E3E)),
                        ),
                        const SizedBox(height: 12),
                        const Text("No Details found"),
                      ],
                    ),
                  );
          }
          return Container(
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(5)),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 13),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "Transaction Details",
                  style: GoogleFonts.zcoolXiaoWei(
                      fontSize: 20, color: const Color(0xff3E3E3E)),
                ),
                const SizedBox(height: 12),
                const Text("No Details found"),
              ],
            ),
          );
        },
      );
    case MessageTypes.info:
      return Text(
        currentMessage.data['text'],
        style: GoogleFonts.mulish(
          fontSize: 12,
          color: const Color(0xff3E3E3E),
          fontWeight: FontWeight.w600,
        ),
      );
    case MessageTypes.booking:
      return Container(
        constraints: const BoxConstraints(maxWidth: 340),

        // width: double.maxFinite,
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 13),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(8)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 5),
            SizedBox(
                width: 250,
                child: Center(child: Image.asset('assets/logo.png'))),
            const SizedBox(height: 10),
            Text(
              "Getting Started with Wedding Ease",
              style: GoogleFonts.zcoolXiaoWei(
                fontSize: 25,
                color: const Color(0xff3E3E3E),
              ),
            ),
            const SizedBox(height: 15),
            IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(
                    CupertinoIcons.clock,
                    size: 23,
                    color: Color(0xff6c6c6c),
                  ),
                  const SizedBox(width: 7),
                  Text(
                    DateTime.fromMillisecondsSinceEpoch(
                            currentMessage.data['bookingstartTime'])
                        .goodTime(),
                    style: GoogleFonts.leagueSpartan(
                      fontSize: 17,
                      color: const Color(0xff6C6C6C),
                    ),
                  ),
                  const SizedBox(width: 3),
                  const VerticalDivider(
                    color: Color(0xff6C6C6C),
                    thickness: 1.5,
                    indent: 4,
                    endIndent: 4,
                    // width: 10,
                  ),
                  const SizedBox(width: 3),
                  Text(
                    '${currentMessage.data['slotgap']} min',
                    style: GoogleFonts.leagueSpartan(
                      fontSize: 17,
                      color: const Color(0xff6C6C6C),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                const Icon(
                  CupertinoIcons.calendar,
                  size: 23,
                  color: Color(0xff6c6c6c),
                ),
                const SizedBox(width: 7),
                Text(
                  DateTime.fromMillisecondsSinceEpoch(
                          currentMessage.data['date'])
                      .goodDayDate(),
                  style: GoogleFonts.leagueSpartan(
                    fontSize: 17,
                    color: const Color(0xff6C6C6C),
                  ),
                ),
              ],
            )
          ],
        ),
      );
    case MessageTypes.file:
      return Container(
        constraints: const BoxConstraints(maxWidth: 360),

        // width: double.maxFinite,
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 13),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(8)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Icon(
              CupertinoIcons.doc,
              color: themeColor,
              size: 28,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '${currentMessage.data['fileName']}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.mulish(
                  fontWeight: FontWeight.w500,
                  color: const Color(0xff3E3E3E),
                  fontSize: 13,
                ),
              ),
            ),
            const SizedBox(width: 12),
            InkWell(
              onTap: () async {
                await FileSaver.instance.saveFile(
                  name: currentMessage.data['fileName'],
                  link: LinkDetails(
                      link: currentMessage.data['fileUrl'] as String),
                );
              },
              child: const Icon(
                Icons.file_download_outlined,
                size: 24,
                color: Color(0xff3E3E3E),
              ),
            ),
          ],
        ),
      );
    case MessageTypes.images:
      List<String> allimages = List.from(currentMessage.data['images']);
      List<String> displayimages =
          allimages.length > 4 ? allimages.sublist(0, 3) : allimages;
      return InkWell(
        onTap: () {
          showDialog(
            context: context,
            builder: (context) {
              return Dialog(
                backgroundColor: Colors.white,
                surfaceTintColor: Colors.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10)),
                child: Container(
                  constraints:
                      const BoxConstraints(maxWidth: 900, maxHeight: 800),
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(10)),
                  child: Stack(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 30),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const SizedBox(height: 20),
                              Row(
                                // mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Wrap(
                                      spacing: 15,
                                      runSpacing: 15,
                                      runAlignment: WrapAlignment.start,
                                      alignment: WrapAlignment.start,
                                      children: [
                                        ...List.generate(
                                          allimages.length,
                                          (index) {
                                            return InkWell(
                                              onTap: () {
                                                showDialog(
                                                  context: context,
                                                  builder: (context) {
                                                    return Dialog(
                                                      backgroundColor:
                                                          Colors.white,
                                                      surfaceTintColor:
                                                          Colors.white,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          10)),
                                                      // insetPadding: EdgeInsets.all(5),
                                                      child: Container(
                                                        clipBehavior:
                                                            Clip.antiAlias,
                                                        constraints:
                                                            const BoxConstraints(
                                                                maxWidth: 500,
                                                                maxHeight: 500),
                                                        decoration: BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        10)),
                                                        child: Stack(
                                                          children: [
                                                            InteractiveViewer(
                                                              maxScale: 1.5,
                                                              minScale: .9,
                                                              child:
                                                                  AspectRatio(
                                                                aspectRatio: 1,
                                                                child:
                                                                    CachedNetworkImage(
                                                                  imageUrl:
                                                                      allimages[
                                                                          index],
                                                                  width: double
                                                                      .maxFinite,
                                                                  fit: BoxFit
                                                                      .cover,
                                                                ),
                                                              ),
                                                            ),
                                                            Align(
                                                              alignment:
                                                                  Alignment
                                                                      .topRight,
                                                              child: Padding(
                                                                padding:
                                                                    const EdgeInsets
                                                                        .only(
                                                                        right:
                                                                            8.0,
                                                                        top: 8),
                                                                child: Column(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    InkWell(
                                                                      onTap:
                                                                          () {
                                                                        Navigator.of(context)
                                                                            .pop();
                                                                      },
                                                                      child:
                                                                          Container(
                                                                        padding: const EdgeInsets
                                                                            .all(
                                                                            3),
                                                                        decoration: const BoxDecoration(
                                                                            color:
                                                                                Colors.white,
                                                                            shape: BoxShape.circle,
                                                                            boxShadow: [
                                                                              BoxShadow(
                                                                                  color: Colors.black26,
                                                                                  blurRadius: 2,
                                                                                  // spreadRadius: 0,
                                                                                  spreadRadius: 1,
                                                                                  offset: Offset(0, 2)),
                                                                            ]),
                                                                        child:
                                                                            const Icon(
                                                                          CupertinoIcons
                                                                              .xmark,
                                                                          size:
                                                                              18,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                    const SizedBox(
                                                                        height:
                                                                            10),
                                                                    InkWell(
                                                                      onTap:
                                                                          () async {
                                                                        await FileSaver
                                                                            .instance
                                                                            .saveFile(
                                                                          ext:
                                                                              'png',
                                                                          name:
                                                                              'Image',
                                                                          link:
                                                                              LinkDetails(link: allimages[index]),
                                                                        );
                                                                      },
                                                                      child:
                                                                          Container(
                                                                        padding: const EdgeInsets
                                                                            .all(
                                                                            3),
                                                                        decoration: const BoxDecoration(
                                                                            color:
                                                                                Colors.white,
                                                                            shape: BoxShape.circle,
                                                                            boxShadow: [
                                                                              BoxShadow(
                                                                                  color: Colors.black26,
                                                                                  blurRadius: 2,
                                                                                  // spreadRadius: 0,
                                                                                  spreadRadius: 1,
                                                                                  offset: Offset(0, 2)),
                                                                            ]),
                                                                        child:
                                                                            const Icon(
                                                                          CupertinoIcons
                                                                              .down_arrow,
                                                                          size:
                                                                              18,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                              child: Container(
                                                height: 230,
                                                width: 180,
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                                child: Stack(
                                                  children: [
                                                    CachedNetworkImage(
                                                      imageUrl:
                                                          allimages[index],
                                                      fit: BoxFit.cover,
                                                      width: double.maxFinite,
                                                      height: double.maxFinite,
                                                    ),
                                                    Align(
                                                      alignment:
                                                          Alignment.topRight,
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(7),
                                                        child: InkWell(
                                                          onTap: () async {
                                                            await FileSaver
                                                                .instance
                                                                .saveFile(
                                                              ext: 'png',
                                                              name: 'Image',
                                                              link: LinkDetails(
                                                                  link: allimages[
                                                                      index]),
                                                            );
                                                          },
                                                          child: Container(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(3),
                                                            decoration:
                                                                const BoxDecoration(
                                                                    color: Colors
                                                                        .white,
                                                                    shape: BoxShape
                                                                        .circle,
                                                                    boxShadow: [
                                                                  BoxShadow(
                                                                      color: Colors
                                                                          .black26,
                                                                      blurRadius:
                                                                          2,
                                                                      // spreadRadius: 0,
                                                                      spreadRadius:
                                                                          1,
                                                                      offset:
                                                                          Offset(
                                                                              0,
                                                                              2)),
                                                                ]),
                                                            child: const Icon(
                                                              CupertinoIcons
                                                                  .down_arrow,
                                                              size: 18,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              ElevatedButton.icon(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xffD88F77),
                                  foregroundColor: Colors.white,
                                ),
                                icon: const Icon(CupertinoIcons.cloud_download),
                                onPressed: () async {
                                  for (var image in allimages) {
                                    await FileSaver.instance.saveFile(
                                      ext: 'png',
                                      name: 'Image',
                                      link: LinkDetails(link: image),
                                    );
                                  }
                                },
                                label: const Text("Download All"),
                              ),
                              const SizedBox(height: 20),
                            ],
                          ),
                        ),
                      ),
                      // const SizedBox(width: 15),
                      Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            InkWell(
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                padding: const EdgeInsets.all(6),
                                decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                          color: Colors.black12,
                                          offset: Offset(0, 3),
                                          blurRadius: 7)
                                    ]),
                                child: const Icon(
                                  CupertinoIcons.xmark,
                                  size: 17,
                                  color: Color(0xff3E3E3E),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
        child: Container(
          // width: double.maxFinite,
          // height: 150,
          // padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 13),
          constraints: const BoxConstraints(maxWidth: 340),

          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(8)),
          child: Center(
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              alignment: WrapAlignment.start,
              runAlignment: WrapAlignment.start,
              children: [
                ...List.generate(
                  displayimages.length,
                  (index) {
                    final image = displayimages[index];
                    return Container(
                      height: 150,
                      width: 150,
                      clipBehavior: Clip.antiAlias,
                      decoration:
                          BoxDecoration(borderRadius: BorderRadius.circular(7)),
                      child: CachedNetworkImage(
                        imageUrl: image,
                        fit: BoxFit.cover,
                      ),
                    );
                  },
                ),
                if (allimages.length > 4)
                  Stack(
                    children: [
                      Container(
                        height: 150,
                        width: 150,
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7)),
                        child: CachedNetworkImage(
                          imageUrl: allimages.last,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Container(
                        height: 150,
                        width: 150,
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            color: Colors.black54,
                            borderRadius: BorderRadius.circular(7)),
                      ),
                      Container(
                        height: 150,
                        width: 150,
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7)),
                        child: Center(
                          child: Text(
                            "+${allimages.length - 3}",
                            style: GoogleFonts.livvic(
                                fontSize: 22, color: Colors.white),
                          ),
                        ),
                      ),
                    ],
                  )
              ],
            ),
          ),
        ),
      );
    case MessageTypes.set:
      List<SubCategory> selectedSubCategories = [];

      for (var i = 0;
          i <=
              ((currentMessage.data['sets'] as Map).keys.toList().length - 1 >=
                      2
                  ? 2
                  : (currentMessage.data['sets'] as Map).keys.toList().length -
                      1);
          i++) {
        final foundSubCat = Get.find<HomeCtrl>().subcategories.firstWhereOrNull(
            (element) => (currentMessage.data['sets'] as Map)
                .keys
                .toList()[i]
                .contains(element.docId));
        selectedSubCategories.addIf(foundSubCat != null, foundSubCat!);
        /*    selectedSubCategories = Get.find<HomeCtrl>()
            .subcategories
            .where((element) => (currentMessage.data['sets'] as Map)
                .keys
                .toList()
                .contains(element.docId))
            .toList(); */
      }

      return Container(
          // width: double.maxFinite,
          // padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 13),
          constraints: const BoxConstraints(maxWidth: 451),
          padding: const EdgeInsets.all(13),
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(8)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Recommended set",
                style: GoogleFonts.zcoolXiaoWei(
                    fontSize: 20, color: const Color(0xff3E3E3E)),
              ),
              const SizedBox(height: 10),
              if (selectedSubCategories.isNotEmpty)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      // spacing: 7,
                      // runAlignment: WrapAlignment.start,
                      // runSpacing: 7,
                      // alignment: WrapAlignment.start,
                      children: [
                        ...List.generate(
                          selectedSubCategories.length,
                          (index) {
                            return Padding(
                              padding:
                                  EdgeInsets.only(left: index == 0 ? 0 : 10.0),
                              child: SizedBox(
                                height: 180,
                                child: Stack(
                                  children: [
                                    Container(
                                      width: 135,
                                      height: double.maxFinite,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(15)),
                                      child: ImageNetwork(
                                        image:
                                            selectedSubCategories[index].image,
                                        height: 180,
                                        width: 135,
                                        // fit: BoxFit.cover,
                                      ),
                                    ),
                                    Container(
                                      width: 135,
                                      height: double.maxFinite,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(15),
                                          gradient: const LinearGradient(
                                            begin: Alignment.bottomCenter,
                                            end: Alignment.topCenter,
                                            stops: [0.1, 0.15, 0.3, 1],
                                            colors: [
                                              Colors.black87,
                                              Colors.black38,
                                              Colors.black12,
                                              Colors.transparent,
                                            ],
                                          )),
                                    ),
                                    Align(
                                      alignment: Alignment.bottomLeft,
                                      child: Padding(
                                        padding: const EdgeInsets.all(10),
                                        child: Text(
                                          capilatlizeFirstLetter(
                                              selectedSubCategories[index]
                                                  .name),
                                          style: GoogleFonts.mulish(
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white,
                                              fontSize: 12),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            );
                          },
                        )
                      ],
                    ),
                    const SizedBox(height: 15),
                    Text(
                      selectedSubCategories
                          .map((e) => capilatlizeFirstLetter(e.name))
                          .join(', '),
                      style: GoogleFonts.poppins(fontSize: 15),
                    ),
                    const SizedBox(height: 15),
                    Text(
                      currentMessage.data['desc'],
                      style: GoogleFonts.mulish(
                          fontSize: 13, color: const Color(0xff6C6C6C)),
                    ),
                  ],
                ),
              if (selectedSubCategories.isEmpty) Text("No Data Exist")
            ],
          ));
    case MessageTypes.package:
      List<PackageModel> selectedPackages = [];
      selectedPackages.addAll(Get.find<HomeCtrl>().allPackages.where(
          (element) => (currentMessage.data['packageIds'] as List)
              .contains(element.docId)));
      // selectedPackages.addAll(Get.find<HomeCtrl>().miniPackagesList.where(
      //     (element) => (currentMessage.data['packageIds'] as List)
      //         .contains(element.docId)));
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 10),
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          // mainAxisSize: MainAxisSize.max,
          children: [
            Text(
              "Suggested Package",
              style: GoogleFonts.zcoolXiaoWei(
                  fontSize: 20, color: const Color(0xff3E3E3E)),
            ),
            const SizedBox(height: 10),
            Wrap(
              spacing: 10,
              runAlignment: WrapAlignment.start,
              runSpacing: 10,
              alignment: WrapAlignment.start,
              children: [
                ...List.generate(
                  selectedPackages.length,
                  (index) {
                    return Container(
                      width: 180,
                      height: 180,
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xffCBCBCB)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(7),
                            decoration: BoxDecoration(
                                color: const Color(0xffFFD8CB),
                                borderRadius: BorderRadius.circular(8)),
                            child: Text(
                              selectedPackages[index].packageName,
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: const Color(0xff6C6C6C),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "₹${selectedPackages[index].price}",
                            style: GoogleFonts.zcoolXiaoWei(
                                fontSize: 17, color: const Color(0xff3E3E3E)),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  selectedPackages[index].description,
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                  style: GoogleFonts.zcoolXiaoWei(
                                      color: const Color(0xff3E3E3E)),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                )
              ],
            )
          ],
        ),
      );
    case MessageTypes.orderAccRej:
      getOrderData() async {
        final orderSnap = await FBFireStore.orders
            .doc(currentMessage.data['orderDocId'])
            .get();
        return OrderModel.fromDocSnap(orderSnap);
      }
      return FutureBuilder(
          future: getOrderData(),
          builder: (context, snapshot) {
            if (snapshot.hasError) {
              debugPrint(snapshot.error.toString());
              return Icon(CupertinoIcons.exclamationmark_circle);
            }
            if (snapshot.hasData) {
              final orderData = snapshot.data;
              return orderData != null
                  ? InkWell(
                      hoverColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        context.push('${Routes.order}/${orderData.docId}');
                        // TODO: commented order dialog
                        /*    List<DisplayProductsDetails> displayOrderDetails = [];
                        displayOrderDetails.clear();
                        final sessionSnap = await FBFireStore.sessions
                            .doc(orderData.sessionId)
                            .get();
                        final sessionModel = SessionModel.fromSnap(sessionSnap);
                        for (var orderData in orderData.orderProductData) {
                          final productSnap = await FBFireStore.products
                              .doc(orderData.productId)
                              .get();
                          final productData =
                              ProductModel.fromDocSnap(productSnap);
                          final variantSnap = await FBFireStore.variants
                              .doc(orderData.variantId)
                              .get();
                          final variantData =
                              NewVariantModel.fromDocSnap(variantSnap);
                          final vendorSnap = await FBFireStore.vendors
                              .doc(productData.vendorDocId)
                              .get();
                          final vendorData =
                              VendorModel.fromDocSnap(vendorSnap);
                          displayOrderDetails.add(DisplayProductsDetails(
                              product: productData,
                              variant: variantData,
                              vendorData: vendorData,
                              orderData: orderData));
                        }
                        double cellWidth = 200;
                        double cellPadding = 10;
                        Color tableColor = Colors.grey;
                        showDialog(
                          context: context,
                          builder: (context) {
                            bool isLoading = false;
                            List<ChargeModel> chargesList = [];
                            chargesList.clear();
                            chargesList.addAll(orderData.charges);
                            // SessionModel? sessionData;
                            bool isPaid = false;

                            return StatefulBuilder(
                              builder: (context, setState2) {
                                return Dialog(
                                  backgroundColor: Colors.white,
                                  surfaceTintColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20)),
                                  child: ConstrainedBox(
                                    constraints: const BoxConstraints(
                                        maxHeight: double.maxFinite,
                                        maxWidth: double.maxFinite),
                                    child: SingleChildScrollView(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 23, vertical: 18),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "Order",
                                                style: GoogleFonts.zcoolXiaoWei(
                                                    fontSize: 32,
                                                    color: const Color(
                                                        0xffD88F77)),
                                              ),
                                              InkWell(
                                                hoverColor: Colors.transparent,
                                                highlightColor:
                                                    Colors.transparent,
                                                onTap: () {
                                                  Navigator.of(context).pop();
                                                },
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(3),
                                                  decoration:
                                                      const BoxDecoration(
                                                          color: Colors.white,
                                                          shape:
                                                              BoxShape.circle,
                                                          boxShadow: [
                                                        BoxShadow(
                                                            color:
                                                                Colors.black26,
                                                            blurRadius: 2,
                                                            // spreadRadius: 0,
                                                            spreadRadius: 1,
                                                            offset:
                                                                Offset(0, 2)),
                                                      ]),
                                                  child: const Icon(
                                                    CupertinoIcons.xmark,
                                                    size: 18,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 25),
                                          Row(
                                            children: [
                                              Text(
                                                'Selected products',
                                                style: GoogleFonts.livvic(
                                                    fontSize: 17,
                                                    color: const Color(
                                                        0xffD88F77)),
                                              ),
                                              Spacer(),
                                              addNewProductButton(
                                                  sessionModel,
                                                  context,
                                                  displayOrderDetails,
                                                  setState2),
                                              /*   ElevatedButton(
                                                        onPressed: () {
                                                          showDialog(
                                                            context: context,
                                                            builder: (context) {
                                                              Timer? debounce;
                                                              List<NewVariantModel>
                                                                  searchedList =
                                                                  [];
                                                              getSearchVariant(
                                                                  String
                                                                      searchedStr) async {
                                                                searchedList
                                                                    .clear();
                                                                final searchedSnap = await FBFireStore
                                                                    .variants
                                                                    .where(
                                                                        'lowerName',
                                                                        isGreaterThanOrEqualTo:
                                                                            searchedStr)
                                                                    .where(
                                                                        'lowerName',
                                                                        isLessThanOrEqualTo:
                                                                            "$searchedStr\uf7ff")
                                                                    .limit(10)
                                                                    .get();
                                                                searchedList.addAll(
                                                                    searchedSnap
                                                                        .docs
                                                                        .map((e) =>
                                                                            NewVariantModel.fromSnap(e)));
                                                              }

                                                              return Dialog(
                                                                backgroundColor:
                                                                    Colors
                                                                        .white,
                                                                surfaceTintColor:
                                                                    Colors
                                                                        .white,
                                                                child: StatefulBuilder(
                                                                    builder:
                                                                        (context,
                                                                            searchSetState) {
                                                                  return Container(
                                                                    constraints:
                                                                        const BoxConstraints(
                                                                            maxWidth:
                                                                                800),
                                                                    padding:
                                                                        const EdgeInsets
                                                                            .all(
                                                                            20),
                                                                    child:
                                                                        Column(
                                                                      mainAxisSize:
                                                                          MainAxisSize
                                                                              .min,
                                                                      crossAxisAlignment:
                                                                          CrossAxisAlignment
                                                                              .start,
                                                                      children: [
                                                                        Text(
                                                                            'Search Product',
                                                                            style:
                                                                                GoogleFonts.zcoolXiaoWei(fontSize: 25, color: themeColor)),
                                                                        const SizedBox(
                                                                            height:
                                                                                15),
                                                                        TextFormField(
                                                                          decoration:
                                                                              InputDecoration(
                                                                            hintText:
                                                                                ' search product name',
                                                                            border:
                                                                                OutlineInputBorder(
                                                                              borderRadius: BorderRadius.circular(5),
                                                                              borderSide: BorderSide(color: Colors.grey.shade400),
                                                                            ),
                                                                            enabledBorder:
                                                                                OutlineInputBorder(
                                                                              borderRadius: BorderRadius.circular(5),
                                                                              borderSide: BorderSide(color: Colors.grey.shade400),
                                                                            ),
                                                                            focusedBorder:
                                                                                OutlineInputBorder(
                                                                              borderRadius: BorderRadius.circular(5),
                                                                              borderSide: BorderSide(color: Colors.grey.shade400),
                                                                            ),
                                                                          ),
                                                                          onChanged:
                                                                              (value) async {
                                                                            if (debounce?.isActive ??
                                                                                false) {
                                                                              debounce?.cancel();
                                                                            }
                                                                            debounce =
                                                                                Timer(const Duration(milliseconds: 500), () async {
                                                                              if (value.trim().isNotEmpty) {
                                                                                // variantDataList.clear();
                                                                                await getSearchVariant(value.toLowerCase().trim());
                                                                                searchSetState(() {});
                                                                              } else {
                                                                                searchedList.clear();
                                                                                searchSetState(() {});
                                                                              }
                                                                            });
                                                                          },
                                                                        ),
                                                                        if (searchedList
                                                                            .isNotEmpty) ...[
                                                                          const SizedBox(
                                                                              height: 10),
                                                                          const Row(
                                                                            children: [
                                                                              SizedBox(width: 50, child: Text('Sr No')),
                                                                              SizedBox(width: 10),
                                                                              SizedBox(width: 100, child: Center(child: Text('Image'))),
                                                                              SizedBox(width: 10),
                                                                              Expanded(child: Text('Product Name')),
                                                                              SizedBox(width: 10),
                                                                            ],
                                                                          ),
                                                                          const SizedBox(
                                                                              height: 10),
                                                                          ...List
                                                                              .generate(
                                                                            searchedList.length,
                                                                            (index) {
                                                                              final variant = searchedList[index];
                                                                              return Padding(
                                                                                padding: EdgeInsets.only(top: index == 0 ? 0 : 8),
                                                                                child: InkWell(
                                                                                  hoverColor: Colors.transparent,
                                                                                  highlightColor: Colors.transparent,
                                                                                  onTap: () async {
                                                                                    if (orderProductData.map((e) => e.variantId).contains(variant.docId)) {
                                                                                      showAppSnackBar(context, 'Already in order');
                                                                                      return;
                                                                                    }
                                                                                    final productSnap = await FBFireStore.products.doc(variant.productId).get();
                                                                                    final productData = ProductModel.fromDocSnap(productSnap);
                                                                                    selectedProducts.add(productData);
                                                                                    selectedUserVarinats.add(variant);

                                                                                    orderProductData.add(OrderProductData(vendorId: productData.vendorDocId, id: getRandomId(6), productId: variant.productId, variantId: variant.docId!, qty: 1, currentStatus: null, purchasePrice: variant.fixedprice, sellingPrice: 0, userNote: '', adminNote: '', productSKu: productData.sku, deliveryDate: null));
                                                                                    Navigator.of(context).pop();
                                                                                    setState2(() {});
                                                                                  },
                                                                                  child: Row(
                                                                                    children: [
                                                                                      SizedBox(width: 50, child: Text((index + 1).toString())),
                                                                                      const SizedBox(width: 10),
                                                                                      SizedBox(
                                                                                        width: 100,
                                                                                        child: Center(
                                                                                          child: Container(height: 50, width: 50, clipBehavior: Clip.antiAlias, decoration: const BoxDecoration(shape: BoxShape.circle), child: Image.network(variant.images.first)),
                                                                                        ),
                                                                                      ),
                                                                                      const SizedBox(width: 10),
                                                                                      Expanded(child: Text(capilatlizeFirstLetter(variant.lowerName))),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              );
                                                                            },
                                                                          )
                                                                        ],
                                                                      ],
                                                                    ),
                                                                  );
                                                                }),
                                                              );
                                                            },
                                                          );
                                                        },
                                                        child:
                                                            Text('Add Product')) */
                                            ],
                                          ),
                                          const SizedBox(height: 15),
                                          SingleChildScrollView(
                                            scrollDirection: Axis.horizontal,
                                            child: Column(
                                              children: [
                                                Row(
                                                  children: [
                                                    Container(
                                                      padding: EdgeInsets.all(
                                                          cellPadding),
                                                      width: cellWidth,
                                                      decoration: BoxDecoration(
                                                          border: Border.all(
                                                              color:
                                                                  tableColor)),
                                                      child: const Text(
                                                        'Sr No',
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xff3E3E3E),
                                                            letterSpacing: 1,
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w600),
                                                      ),
                                                    ),
                                                    // SizedBox(width: 15),
                                                    Container(
                                                      padding: EdgeInsets.all(
                                                          cellPadding),

                                                      width: cellWidth,
                                                      decoration: BoxDecoration(
                                                          border: Border(
                                                              bottom: BorderSide(
                                                                  color:
                                                                      tableColor),
                                                              top: BorderSide(
                                                                  color:
                                                                      tableColor),
                                                              right: BorderSide(
                                                                  color:
                                                                      tableColor))),
                                                      // clipBehavior: Clip.antiAlias,
                                                      // decoration:
                                                      //     const BoxDecoration(shape: BoxShape.circle),
                                                      child: const Text(
                                                        'Image',
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xff3E3E3E),
                                                            letterSpacing: 1,
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w600),
                                                      ),
                                                    ),
                                                    Container(
                                                      padding: EdgeInsets.all(
                                                          cellPadding),
                                                      width: cellWidth,
                                                      decoration: BoxDecoration(
                                                          border: Border(
                                                              bottom: BorderSide(
                                                                  color:
                                                                      tableColor),
                                                              top: BorderSide(
                                                                  color:
                                                                      tableColor),
                                                              right: BorderSide(
                                                                  color:
                                                                      tableColor))),
                                                      child: const Text(
                                                        'Product SKU',
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xff3E3E3E),
                                                            letterSpacing: 1,
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w600),
                                                      ),
                                                    ),
                                                    Container(
                                                      padding: EdgeInsets.all(
                                                          cellPadding),
                                                      width: cellWidth,
                                                      decoration: BoxDecoration(
                                                          border: Border(
                                                              bottom: BorderSide(
                                                                  color:
                                                                      tableColor),
                                                              top: BorderSide(
                                                                  color:
                                                                      tableColor),
                                                              right: BorderSide(
                                                                  color:
                                                                      tableColor))),
                                                      child: const Text(
                                                        'Product Name',
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xff3E3E3E),
                                                            letterSpacing: 1,
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w600),
                                                      ),
                                                    ),
                                                    Container(
                                                        padding: EdgeInsets.all(
                                                            cellPadding),
                                                        width: cellWidth,
                                                        decoration: BoxDecoration(
                                                            border: Border(
                                                                bottom: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                top: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                right: BorderSide(
                                                                    color:
                                                                        tableColor))),
                                                        child: const Text(
                                                          'Qty',
                                                          style: TextStyle(
                                                              color: Color(
                                                                  0xff3E3E3E),
                                                              letterSpacing: 1,
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        )),
                                                    Container(
                                                        padding: EdgeInsets.all(
                                                            cellPadding),
                                                        width: cellWidth,
                                                        decoration: BoxDecoration(
                                                            border: Border(
                                                                bottom: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                top: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                right: BorderSide(
                                                                    color:
                                                                        tableColor))),
                                                        child: const Text(
                                                          'Purchase Price',
                                                          style: TextStyle(
                                                              color: Color(
                                                                  0xff3E3E3E),
                                                              letterSpacing: 1,
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        )),
                                                    Container(
                                                        padding: EdgeInsets.all(
                                                            cellPadding),
                                                        width: cellWidth,
                                                        decoration: BoxDecoration(
                                                            border: Border(
                                                                bottom: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                top: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                right: BorderSide(
                                                                    color:
                                                                        tableColor))),
                                                        child: const Text(
                                                          'Selling Price',
                                                          style: TextStyle(
                                                              color: Color(
                                                                  0xff3E3E3E),
                                                              letterSpacing: 1,
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        )),
                                                    Container(
                                                        padding: EdgeInsets.all(
                                                            cellPadding),
                                                        width: cellWidth,
                                                        decoration: BoxDecoration(
                                                            border: Border(
                                                                bottom: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                top: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                right: BorderSide(
                                                                    color:
                                                                        tableColor))),
                                                        child: const Text(
                                                          'Vendor Name',
                                                          style: TextStyle(
                                                              color: Color(
                                                                  0xff3E3E3E),
                                                              letterSpacing: 1,
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        )),
                                                    Container(
                                                        padding: EdgeInsets.all(
                                                            cellPadding),
                                                        width: cellWidth,
                                                        decoration: BoxDecoration(
                                                            border: Border(
                                                                bottom: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                top: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                right: BorderSide(
                                                                    color:
                                                                        tableColor))),
                                                        child: const Text(
                                                          'Customization Note',
                                                          style: TextStyle(
                                                              color: Color(
                                                                  0xff3E3E3E),
                                                              letterSpacing: 1,
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        )),
                                                    Container(
                                                        padding: EdgeInsets.all(
                                                            cellPadding),
                                                        width: cellWidth,
                                                        decoration: BoxDecoration(
                                                            border: Border(
                                                                bottom: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                top: BorderSide(
                                                                    color:
                                                                        tableColor),
                                                                right: BorderSide(
                                                                    color:
                                                                        tableColor))),
                                                        child: const Text(
                                                          'Team Member Note',
                                                          style: TextStyle(
                                                              color: Color(
                                                                  0xff3E3E3E),
                                                              letterSpacing: 1,
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        )),
                                                  ],
                                                ),
                                                ...List.generate(
                                                  displayOrderDetails.length,
                                                  (index) {
                                                    final currentorderProductData =
                                                        displayOrderDetails[
                                                            index];
                                                    // final variant = selectedUserVarinats
                                                    //     .firstWhere((element) =>
                                                    //         element
                                                    //             .docId ==
                                                    //         orderProductData[
                                                    //                 index]
                                                    //             .variantId);

                                                    // final product = selectedProducts
                                                    //     .firstWhereOrNull(
                                                    //         (element) =>
                                                    //             element
                                                    //                 .docId ==
                                                    //             variant
                                                    //                 .productId);
                                                    // final vendorName = hCtrl
                                                    //     .vendors
                                                    //     .firstWhereOrNull(
                                                    //         (element) =>
                                                    //             element
                                                    //                 .docId ==
                                                    //             product
                                                    //                 ?.vendorDocId)
                                                    //     ?.name;
                                                    return Row(
                                                      children: [
                                                        Container(
                                                          width: cellWidth,
                                                          // padding: EdgeInsets.all(cellPadding),
                                                          decoration: BoxDecoration(
                                                              border: Border(
                                                                  bottom: BorderSide(
                                                                      color:
                                                                          tableColor),
                                                                  left: BorderSide(
                                                                      color:
                                                                          tableColor),
                                                                  right: BorderSide(
                                                                      color:
                                                                          tableColor))),
                                                          child: TextFormField(
                                                            controller:
                                                                TextEditingController(
                                                                    text:
                                                                        '${(index + 1)}'),
                                                            style: const TextStyle(
                                                                fontSize: 14,
                                                                color: Color(
                                                                    0xff6C6C6C)),
                                                            enabled: false,
                                                            decoration: const InputDecoration(
                                                                border: OutlineInputBorder(
                                                                    borderSide:
                                                                        BorderSide
                                                                            .none)),
                                                          ),
                                                        ),
                                                        Container(
                                                          // padding: EdgeInsets.all(cellPadding),
                                                          width: cellWidth,
                                                          decoration: BoxDecoration(
                                                              border: Border(
                                                                  bottom: BorderSide(
                                                                      color:
                                                                          tableColor),
                                                                  right: BorderSide(
                                                                      color:
                                                                          tableColor))),
                                                          child: Container(
                                                            height: 48,
                                                            width: 48,
                                                            clipBehavior:
                                                                Clip.antiAlias,
                                                            decoration:
                                                                const BoxDecoration(
                                                                    shape: BoxShape
                                                                        .circle),
                                                            child:
                                                                CachedNetworkImage(
                                                              imageUrl:
                                                                  currentorderProductData
                                                                      .variant
                                                                      .images
                                                                      .first,
                                                              fit: BoxFit
                                                                  .contain,
                                                            ),
                                                          ),
                                                        ),
                                                        Container(
                                                          // padding: EdgeInsets.all(cellPadding),
                                                          width: cellWidth,
                                                          decoration: BoxDecoration(
                                                              border: Border(
                                                                  bottom: BorderSide(
                                                                      color:
                                                                          tableColor),
                                                                  right: BorderSide(
                                                                      color:
                                                                          tableColor))),
                                                          child: TextFormField(
                                                            controller:
                                                                TextEditingController(
                                                                    text: currentorderProductData
                                                                        .product
                                                                        .sku),
                                                            onChanged: (value) {
                                                              // orderProductData[index].qty =
                                                              //     int.tryParse(value) ?? 1;
                                                            },
                                                            enabled: false,
                                                            style: const TextStyle(
                                                                fontSize: 14,
                                                                color: Color(
                                                                    0xff6C6C6C)),
                                                            decoration: const InputDecoration(
                                                                border: OutlineInputBorder(
                                                                    borderSide:
                                                                        BorderSide
                                                                            .none)),
                                                          ),
                                                        ),
                                                        Container(
                                                          // padding: EdgeInsets.all(cellPadding),
                                                          width: cellWidth,
                                                          decoration: BoxDecoration(
                                                              border: Border(
                                                                  bottom: BorderSide(
                                                                      color:
                                                                          tableColor),
                                                                  right: BorderSide(
                                                                      color:
                                                                          tableColor))),

                                                          child: TextFormField(
                                                            controller: TextEditingController(
                                                                text: capilatlizeFirstLetter(
                                                                    currentorderProductData
                                                                        .variant
                                                                        .lowerName)),
                                                            onChanged: (value) {
                                                              // orderProductData[index].qty =
                                                              //     int.tryParse(value) ?? 1;
                                                            },
                                                            enabled: false,
                                                            style: const TextStyle(
                                                                fontSize: 14,
                                                                color: Color(
                                                                    0xff6C6C6C)),
                                                            decoration: const InputDecoration(
                                                                border: OutlineInputBorder(
                                                                    borderSide:
                                                                        BorderSide
                                                                            .none)),
                                                          ),
                                                        ),
                                                        Container(
                                                            // padding: EdgeInsets.all(cellPadding),
                                                            width: cellWidth,
                                                            decoration: BoxDecoration(
                                                                border: Border(
                                                                    bottom: BorderSide(
                                                                        color:
                                                                            tableColor),
                                                                    right: BorderSide(
                                                                        color:
                                                                            tableColor))),
                                                            child:
                                                                TextFormField(
                                                              controller: TextEditingController(
                                                                  text: currentorderProductData
                                                                      .orderData
                                                                      .qty
                                                                      .toString()),
                                                              onChanged:
                                                                  (value) {
                                                                currentorderProductData
                                                                    .orderData
                                                                    .qty = int
                                                                        .tryParse(
                                                                            value) ??
                                                                    currentorderProductData
                                                                        .orderData
                                                                        .qty;
                                                              },
                                                              style: const TextStyle(
                                                                  fontSize: 14,
                                                                  color: Color(
                                                                      0xff6C6C6C)),
                                                              decoration: const InputDecoration(
                                                                  border: OutlineInputBorder(
                                                                      borderSide:
                                                                          BorderSide
                                                                              .none)),
                                                            )),
                                                        Container(
                                                            // padding: EdgeInsets.all(cellPadding),
                                                            width: cellWidth,
                                                            decoration: BoxDecoration(
                                                                border: Border(
                                                                    bottom: BorderSide(
                                                                        color:
                                                                            tableColor),
                                                                    right: BorderSide(
                                                                        color:
                                                                            tableColor))),
                                                            child:
                                                                TextFormField(
                                                              controller: TextEditingController(
                                                                  text: currentorderProductData
                                                                      .orderData
                                                                      .purchasePrice
                                                                      .toString()),
                                                              onChanged:
                                                                  (value) {
                                                                // orderProductData[index].qty =
                                                                //     int.tryParse(value) ?? 1;
                                                              },
                                                              enabled: false,
                                                              style: const TextStyle(
                                                                  fontSize: 14,
                                                                  color: Color(
                                                                      0xff6C6C6C)),
                                                              decoration: const InputDecoration(
                                                                  border: OutlineInputBorder(
                                                                      borderSide:
                                                                          BorderSide
                                                                              .none)),
                                                            )),
                                                        Container(
                                                          // padding: EdgeInsets.all(cellPadding),
                                                          width: cellWidth,
                                                          decoration: BoxDecoration(
                                                              border: Border(
                                                                  bottom: BorderSide(
                                                                      color:
                                                                          tableColor),
                                                                  right: BorderSide(
                                                                      color:
                                                                          tableColor))),
                                                          child: TextFormField(
                                                            controller: TextEditingController(
                                                                text: (currentorderProductData
                                                                        .orderData
                                                                        .sellingPrice)
                                                                    .toString()),
                                                            onChanged: (value) {
                                                              currentorderProductData
                                                                  .orderData
                                                                  .sellingPrice = int
                                                                      .tryParse(
                                                                          value) ??
                                                                  currentorderProductData
                                                                      .orderData
                                                                      .sellingPrice;
                                                            },
                                                            style: const TextStyle(
                                                                fontSize: 14,
                                                                color: Color(
                                                                    0xff6C6C6C)),
                                                            decoration: const InputDecoration(
                                                                border: OutlineInputBorder(
                                                                    borderSide:
                                                                        BorderSide
                                                                            .none)),
                                                          ),
                                                        ),
                                                        Container(
                                                          // padding: EdgeInsets.all(cellPadding),
                                                          width: cellWidth,
                                                          decoration: BoxDecoration(
                                                              border: Border(
                                                                  bottom: BorderSide(
                                                                      color:
                                                                          tableColor),
                                                                  right: BorderSide(
                                                                      color:
                                                                          tableColor))),
                                                          child: TextFormField(
                                                            controller: TextEditingController(
                                                                text: capilatlizeFirstLetter(
                                                                    currentorderProductData
                                                                        .vendorData
                                                                        .name)),
                                                            onChanged: (value) {
                                                              // orderProductData[index].qty =
                                                              //     int.tryParse(value) ?? 1;
                                                            },
                                                            enabled: false,
                                                            style: const TextStyle(
                                                                fontSize: 14,
                                                                color: Color(
                                                                    0xff6C6C6C)),
                                                            decoration: const InputDecoration(
                                                                border: OutlineInputBorder(
                                                                    borderSide:
                                                                        BorderSide
                                                                            .none)),
                                                          ),
                                                        ),
                                                        Container(
                                                          // padding: EdgeInsets.all(cellPadding),
                                                          width: cellWidth,
                                                          decoration: BoxDecoration(
                                                              border: Border(
                                                                  bottom: BorderSide(
                                                                      color:
                                                                          tableColor),
                                                                  right: BorderSide(
                                                                      color:
                                                                          tableColor))),
                                                          child: TextFormField(
                                                            controller: TextEditingController(
                                                                text: currentorderProductData
                                                                    .orderData
                                                                    .userNote),
                                                            // enabled: false,
                                                            onChanged: (value) {
                                                              // orderProductData[index].qty =
                                                              //     int.tryParse(value) ?? 1;
                                                            },
                                                            style: const TextStyle(
                                                                fontSize: 14,
                                                                color: Color(
                                                                    0xff6C6C6C)),
                                                            decoration: const InputDecoration(
                                                                border: OutlineInputBorder(
                                                                    borderSide:
                                                                        BorderSide
                                                                            .none)),
                                                          ),
                                                        ),
                                                        Container(
                                                          // padding: EdgeInsets.all(cellPadding),
                                                          width: cellWidth,
                                                          decoration: BoxDecoration(
                                                              border: Border(
                                                                  bottom: BorderSide(
                                                                      color:
                                                                          tableColor),
                                                                  right: BorderSide(
                                                                      color:
                                                                          tableColor))),
                                                          child: TextFormField(
                                                            controller: TextEditingController(
                                                                text: currentorderProductData
                                                                    .orderData
                                                                    .adminNote),
                                                            onChanged: (value) {
                                                              currentorderProductData
                                                                      .orderData
                                                                      .adminNote =
                                                                  value;
                                                            },
                                                            style: const TextStyle(
                                                                fontSize: 14,
                                                                color: Color(
                                                                    0xff6C6C6C)),
                                                            decoration: const InputDecoration(
                                                                border: OutlineInputBorder(
                                                                    borderSide:
                                                                        BorderSide
                                                                            .none)),
                                                          ),
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 15),
                                          Row(
                                            children: [
                                              Text(
                                                "Charges",
                                                style: GoogleFonts.livvic(
                                                    fontSize: 17,
                                                    color: const Color(
                                                        0xffD88F77)),
                                              ),
                                              const SizedBox(width: 20),
                                              IconButton(
                                                onPressed: () {
                                                  chargesList.add(ChargeModel(
                                                      chargeName: '',
                                                      price: ''));
                                                  setState2(() {});
                                                },
                                                icon: const Icon(
                                                    CupertinoIcons.add),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 12),
                                          Column(
                                            // maxCrossAxisExtent: 250,
                                            // crossAxisSpacing: 10,
                                            // mainAxisSpacing: 10,
                                            children: [
                                              ...List.generate(
                                                chargesList.length,
                                                (index) {
                                                  final chargeModel =
                                                      chargesList[index];
                                                  return Padding(
                                                    padding: EdgeInsets.only(
                                                        top: index == 0
                                                            ? 0
                                                            : 18),
                                                    child: Row(
                                                      children: [
                                                        Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(10),
                                                          decoration:
                                                              const BoxDecoration(
                                                            color:
                                                                Color.fromARGB(
                                                                    255,
                                                                    240,
                                                                    240,
                                                                    240),
                                                            shape:
                                                                BoxShape.circle,
                                                          ),
                                                          child: Text(
                                                            (index + 1)
                                                                .toString(),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            width: 15),
                                                        Expanded(
                                                          child: TextFormField(
                                                            controller: TextEditingController(
                                                                text: chargeModel
                                                                    .chargeName),
                                                            onChanged: (value) {
                                                              chargeModel
                                                                      .chargeName =
                                                                  value;
                                                            },
                                                            decoration: inpDecor()
                                                                .copyWith(
                                                                    labelText:
                                                                        'Name',
                                                                    hintText:
                                                                        'Name'),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            width: 5),
                                                        const Text("-"),
                                                        const SizedBox(
                                                            width: 5),
                                                        Expanded(
                                                          child: TextFormField(
                                                            controller:
                                                                TextEditingController(
                                                                    text: chargeModel
                                                                        .price),
                                                            onChanged: (value) {
                                                              chargeModel
                                                                      .price =
                                                                  value;
                                                            },
                                                            decoration: inpDecor()
                                                                .copyWith(
                                                                    labelText:
                                                                        'Charge',
                                                                    hintText:
                                                                        'Charge'),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            width: 5),
                                                        IconButton(
                                                          onPressed: () async {
                                                            chargesList
                                                                .removeAt(
                                                                    index);
                                                            setState2(() {});
                                                          },
                                                          icon: const Icon(
                                                              CupertinoIcons
                                                                  .delete),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                          /*           if (orderData?.userConfirmed ?? false) ...[
                        const SizedBox(height: 15),
                        Container(
                          clipBehavior: Clip.antiAlias,
                          height: 48,
                          padding: const EdgeInsets.only(left: 3),
                          decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(7),
                              // color: const Color.fromARGB(9, 0, 0, 0),
                              color: Colors.transparent),
                          child: Row(
                            children: [
                              Checkbox(
                                side: const BorderSide(color: Colors.grey),
                                checkColor: Colors.white,
                                activeColor: themeColor,
                                hoverColor: Colors.transparent,
                                // focusColor: Colors.transparent,
                                overlayColor: const WidgetStatePropertyAll(
                                    Colors.transparent),
                                value: isPaid,
                                onChanged: (value) async {
                                  isPaid = value!;
                                  setState2(() {});
                                },
                              ),
                              const SizedBox(width: 5),
                              const Text(
                                "Is Paid",
                                style: TextStyle(color: Colors.black),
                              ),
                            ],
                          ),
                        ),
                      ],
                      const SizedBox(height: 25), */
                                          SizedBox(height: 20),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: isLoading
                                                ? [
                                                    const SizedBox(
                                                        height: 25,
                                                        width: 25,
                                                        child: Center(
                                                            child:
                                                                CircularProgressIndicator(
                                                                    strokeWidth:
                                                                        3.5)))
                                                  ]
                                                : [
                                                    TextButton(
                                                        onPressed: () {
                                                          Navigator.of(context)
                                                              .pop();
                                                        },
                                                        child: const Text(
                                                            'Cancel')),
                                                    const SizedBox(width: 20),
                                                    ElevatedButton(
                                                        onPressed: () async {
                                                          if (isLoading) {
                                                            return;
                                                          }
                                                          setState2(() {
                                                            isLoading = true;
                                                          });

                                                          num subTotal = 0;
                                                          num totalPayableAmount =
                                                              0;

                                                          for (var product
                                                              in displayOrderDetails) {
                                                            subTotal += (product
                                                                    .orderData
                                                                    .sellingPrice *
                                                                product
                                                                    .orderData
                                                                    .qty);
                                                          }

                                                          totalPayableAmount =
                                                              subTotal;
                                                          for (var charge
                                                              in chargesList) {
                                                            totalPayableAmount +=
                                                                (num.tryParse(charge
                                                                        .price) ??
                                                                    0);
                                                          }

                                                          final finalChargeMap =
                                                              {};
                                                          for (var charge
                                                              in chargesList) {
                                                            finalChargeMap
                                                                .addEntries({
                                                              MapEntry(
                                                                  charge
                                                                      .chargeName,
                                                                  charge.price)
                                                            });
                                                          }
                                                          final orderStatus = (orderData
                                                                      .userConfirmed ??
                                                                  false)
                                                              ? OrderStatus
                                                                  .paymentPending
                                                              : OrderStatus
                                                                  .userConfirmation;
                                                          try {
                                                            final data = {
                                                              'orderId':
                                                                  orderData
                                                                      .orderId,
                                                              'createdAt': orderData
                                                                  .createdAt
                                                                  .millisecondsSinceEpoch,
                                                              'createdByDocId':
                                                                  orderData
                                                                      .createdByDocId,
                                                              'uid':
                                                                  orderData.uid,
                                                              'userConfirmed':
                                                                  null,
                                                              'isPaid': isPaid,
                                                              'paidOn': null,
                                                              'statusUpdatedBy':
                                                                  orderData
                                                                      .statusUpdatedBy,
                                                              'statusUpdatedAt': orderData !=
                                                                      null
                                                                  ? orderData
                                                                      .statusUpdatedAt
                                                                      ?.millisecondsSinceEpoch
                                                                  : DateTime
                                                                          .now()
                                                                      .millisecondsSinceEpoch,
                                                              'charges':
                                                                  finalChargeMap,
                                                              'orderProductData':
                                                                  displayOrderDetails
                                                                      .map((e) => e
                                                                          .orderData
                                                                          .toJson())
                                                                      .toList(),
                                                              'isDirectOrder':
                                                                  false,
                                                              'userConfirmedOn':
                                                                  orderData
                                                                      .userConfirmedOn
                                                                      ?.millisecondsSinceEpoch,
                                                              'paidAmount': 0,
                                                              'address':
                                                                  orderData
                                                                      .address,
                                                              'subTotal':
                                                                  subTotal,
                                                              'totalAmount':
                                                                  totalPayableAmount,
                                                              'orderStatus':
                                                                  orderStatus,
                                                              'sessionId':
                                                                  orderData
                                                                      .sessionId,
                                                              'previousDocIds':
                                                                  orderData !=
                                                                          null
                                                                      ? FieldValue
                                                                          .arrayUnion([
                                                                          orderData
                                                                              .docId
                                                                        ])
                                                                      : [],
                                                              'rejectionReason':
                                                                  null,
                                                            };
                                                            final orderRef =
                                                                // orderData != null
                                                                //     ? FBFireStore.orders
                                                                //         .doc(orderData?.docId)
                                                                //     :
                                                                FBFireStore
                                                                    .orders
                                                                    .doc();
                                                            // final infoRef = FBFireStore.sessions
                                                            //     .doc(orderData?.sessionId)
                                                            //     .collection('messages')
                                                            //     .doc();
                                                            final messageRef =
                                                                FBFireStore
                                                                    .sessions
                                                                    .doc(orderData
                                                                        .sessionId)
                                                                    .collection(
                                                                        'messages')
                                                                    .doc();
                                                            final infoData = {
                                                              'type':
                                                                  MessageTypes
                                                                      .info,
                                                              'sessionId':
                                                                  orderData
                                                                      .sessionId,
                                                              'loadingText':
                                                                  'info',
                                                              'data': {
                                                                'text': orderData !=
                                                                        null
                                                                    ? 'Order Updated'
                                                                    : 'Order Created'
                                                              },
                                                              'senderId': FBAuth
                                                                  .auth
                                                                  .currentUser
                                                                  ?.uid,
                                                              'sendAt': DateTime
                                                                      .now()
                                                                  .millisecondsSinceEpoch,
                                                            };
                                                            final messageData =
                                                                {
                                                              'type': MessageTypes
                                                                  .orderAccRej,
                                                              'sessionId':
                                                                  orderData
                                                                      .sessionId,
                                                              'loadingText':
                                                                  'order accept reject',
                                                              'data': {
                                                                'orderDocId':
                                                                    orderRef.id,
                                                              },
                                                              'senderId': FBAuth
                                                                  .auth
                                                                  .currentUser
                                                                  ?.uid,
                                                              'sendAt': DateTime
                                                                      .now()
                                                                  .millisecondsSinceEpoch,
                                                            };

                                                            final batch =
                                                                FBFireStore.fb
                                                                    .batch();
                                                            batch.set(
                                                                orderRef, data);
                                                            batch.update(
                                                              FBFireStore.orders
                                                                  .doc(orderData
                                                                      .docId),
                                                              {
                                                                'orderStatus':
                                                                    OrderStatus
                                                                        .cancelled,
                                                                'statusUpdatedBy':
                                                                    FBAuth
                                                                        .auth
                                                                        .currentUser
                                                                        ?.uid,
                                                                'statusUpdatedAt':
                                                                    DateTime.now()
                                                                        .millisecondsSinceEpoch,
                                                              },
                                                            );
                                                            // batch.set(infoRef, infoData);
                                                            batch.set(
                                                                messageRef,
                                                                orderData !=
                                                                        null
                                                                    ? infoData
                                                                    : messageData);
                                                            batch.update(
                                                                FBFireStore
                                                                    .sessions
                                                                    .doc(orderData
                                                                        .sessionId),
                                                                {
                                                                  'lastMessage':
                                                                      infoData
                                                                });
                                                            await batch
                                                                .commit();
                                                            setState2(() {
                                                              isLoading = false;
                                                            });
                                                            Navigator.of(
                                                                    context)
                                                                .pop();
                                                            showAppSnackBar(
                                                                context,
                                                                orderData !=
                                                                        null
                                                                    ? 'Order updated'
                                                                    : 'Order created');
                                                          } catch (e) {
                                                            setState2(() {
                                                              isLoading = false;
                                                            });
                                                            showErrorAppSnackBar(
                                                                context,
                                                                'Error creating order');
                                                            debugPrint(
                                                                e.toString());
                                                          }
                                                        },
                                                        child:
                                                            const Text("Save")),
                                                  ],
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        );
                       */
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 13, horizontal: 10),
                        constraints: const BoxConstraints(maxWidth: 400),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          // mainAxisSize: MainAxisSize.max,
                          children: [
                            Text(
                              "Order Details",
                              style: GoogleFonts.zcoolXiaoWei(
                                  fontSize: 20, color: const Color(0xff3E3E3E)),
                            ),
                            const SizedBox(height: 10),
                            Text(
                              orderData.orderId,
                              style: GoogleFonts.livvic(
                                  fontSize: 12, color: const Color(0xff6C6C6C)),
                            ),
                            const SizedBox(height: 10),
                            Text.rich(TextSpan(children: [
                              TextSpan(
                                text: '\$ ',
                                style: GoogleFonts.livvic(
                                    fontSize: 10,
                                    color: const Color(0xff6C6C6C)),
                              ),
                              TextSpan(
                                text: orderData.totalAmount.toString(),
                                style: GoogleFonts.livvic(
                                    fontSize: 12,
                                    color: const Color(0xff6C6C6C)),
                              )
                            ])),
                            const SizedBox(height: 10),
                            Text(
                              orderData.orderStatus,
                              style: GoogleFonts.livvic(
                                  fontSize: 12, color: const Color(0xff6C6C6C)),
                            ),

                            // Wrap(
                            //   spacing: 10,
                            //   runAlignment: WrapAlignment.start,
                            //   runSpacing: 10,
                            //   alignment: WrapAlignment.start,
                            //   children: [
                            //     ...List.generate(
                            //       selectedPackages.length,
                            //       (index) {
                            //         return Container(
                            //           width: 180,
                            //           height: 180,
                            //           padding: const EdgeInsets.all(10),
                            //           decoration: BoxDecoration(
                            //             color: Colors.white,
                            //             borderRadius: BorderRadius.circular(8),
                            //             border: Border.all(
                            //                 color: const Color(0xffCBCBCB)),
                            //           ),
                            //           child: Column(
                            //             crossAxisAlignment:
                            //                 CrossAxisAlignment.start,
                            //             children: [
                            //               Container(
                            //                 padding: const EdgeInsets.all(7),
                            //                 decoration: BoxDecoration(
                            //                     color: const Color(0xffFFD8CB),
                            //                     borderRadius:
                            //                         BorderRadius.circular(8)),
                            //                 child: Text(
                            //                   selectedPackages[index].packageName,
                            //                   style: GoogleFonts.poppins(
                            //                     fontSize: 12,
                            //                     color: const Color(0xff6C6C6C),
                            //                   ),
                            //                 ),
                            //               ),
                            //               const SizedBox(height: 8),
                            //               Text(
                            //                 "₹${selectedPackages[index].price}",
                            //                 style: GoogleFonts.zcoolXiaoWei(
                            //                     fontSize: 17,
                            //                     color: const Color(0xff3E3E3E)),
                            //               ),
                            //               const SizedBox(height: 12),
                            //               Row(
                            //                 children: [
                            //                   Expanded(
                            //                     child: Text(
                            //                       selectedPackages[index]
                            //                           .description,
                            //                       maxLines: 3,
                            //                       overflow: TextOverflow.ellipsis,
                            //                       style: GoogleFonts.zcoolXiaoWei(
                            //                           color: const Color(
                            //                               0xff3E3E3E)),
                            //                     ),
                            //                   ),
                            //                 ],
                            //               ),
                            //             ],
                            //           ),
                            //         );
                            //       },
                            //     )
                            //   ],
                            // )
                          ],
                        ),
                      ),
                    )
                  : const SizedBox();
            }
            return const SizedBox();
          });
    case MessageTypes.deliveryDetails:
      return FutureBuilder(
        future: FBFireStore.deliveryDetails
            .doc(currentMessage.data['deliveryDocId'])
            .get()
            .then((value) {
          if (value.exists) {
            return DeliveryDetailsModel.fromDocSnap(value);
          } else {
            return null;
          }
        }),
        builder: (context, snapshot) {
          final deliveryDetails = snapshot.data;
          if (snapshot.connectionState == ConnectionState.waiting) {
            return SizedBox(
              height: 25,
              width: 25,
              child: Center(
                child: CircularProgressIndicator(strokeWidth: 3.5),
              ),
            );
          }
          if (snapshot.hasError) {
            debugPrint(snapshot.error.toString());
            return Container(
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(5)),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 13),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Delivery Details",
                    style: GoogleFonts.zcoolXiaoWei(
                        fontSize: 20, color: const Color(0xff3E3E3E)),
                  ),
                  const SizedBox(height: 12),
                  const Text("Something went wrong"),
                ],
              ),
            );
          }
          if (snapshot.hasData) {
            if (deliveryDetails != null) {
              return InkWell(
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () async {
                  final orderSnap = await FBFireStore.orders
                      .where('orderId', isEqualTo: deliveryDetails.orderId)
                      .where('orderStatus', isNotEqualTo: OrderStatus.cancelled)
                      .get();
                  if (orderSnap.docs.isNotEmpty) {
                    final orderData =
                        OrderModel.fromDocSnap(orderSnap.docs.first);

                    showDialog(
                      context: context,
                      builder: (context) {
                        return Dialog(
                          backgroundColor: Colors.white,
                          surfaceTintColor: Colors.white,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10)),
                          // insetPadding: EdgeInsets.all(5),
                          child: Container(
                            clipBehavior: Clip.antiAlias,
                            constraints: const BoxConstraints(
                                maxWidth: 700, maxHeight: 800),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10)),
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 15, vertical: 8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                // mainAxisSize:
                                //     MainAxisSize.min,
                                children: [
                                  const SizedBox(height: 10),
                                  Row(
                                    children: [
                                      Text(
                                        'Delivery Details',
                                        style: GoogleFonts.zcoolXiaoWei(
                                            color: themeColor, fontSize: 25),
                                      ),
                                      Spacer(),
                                    ],
                                  ),
                                  const SizedBox(height: 20),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text.rich(
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                  text: 'Delivery partner',
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 13.5,
                                                      color:
                                                          Color(0xff3F3F3F))),
                                              TextSpan(
                                                  text: ': ',
                                                  style: TextStyle(
                                                      fontSize: 13,
                                                      fontWeight:
                                                          FontWeight.bold)),
                                              TextSpan(
                                                text: deliveryDetails
                                                    .deliveryPartner,
                                                style: GoogleFonts.livvic(
                                                    fontSize: 14,
                                                    color: Color(0xff3C3C3C)),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 20),
                                      // Spacer(),
                                      Text.rich(TextSpan(children: [
                                        TextSpan(
                                            text: 'Dispatched on',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 13.5,
                                                color: Color(0xff3F3F3F))),
                                        TextSpan(
                                            text: ': ',
                                            style: TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold)),
                                        TextSpan(
                                            style: GoogleFonts.livvic(
                                                fontSize: 14,
                                                color: Color(0xff3C3C3C)),
                                            text:
                                                '${deliveryDetails.dispatchedOn?.goodDayDate() ?? '-'} ${deliveryDetails.dispatchedOn?.goodTime() ?? ''}'),
                                      ])),
                                    ],
                                  ),
                                  SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Text.rich(TextSpan(children: [
                                        TextSpan(
                                            text: 'Order ID',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 13.5,
                                                color: Color(0xff3F3F3F))),
                                        TextSpan(
                                            text: ': ',
                                            style: TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold)),
                                        TextSpan(
                                            text: deliveryDetails.orderId,
                                            style: GoogleFonts.livvic(
                                                fontSize: 14,
                                                color: Color(0xff3C3C3C)))
                                      ])),
                                      SizedBox(width: 20),
                                      Spacer(),
                                      Text.rich(TextSpan(children: [
                                        TextSpan(
                                            text: 'Charges',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 13.5,
                                                color: Color(0xff3F3F3F))),
                                        TextSpan(
                                            text: ': ',
                                            style: TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold)),
                                        TextSpan(
                                            style: GoogleFonts.livvic(
                                                fontSize: 14,
                                                color: Color(0xff3C3C3C)),
                                            text: deliveryDetails.charges
                                                .toStringAsFixed(2)),
                                      ])),
                                    ],
                                  ),
                                  SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Text.rich(TextSpan(children: [
                                        TextSpan(
                                            text: 'Status',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 13.5,
                                                color: Color(0xff3F3F3F))),
                                        TextSpan(
                                            text: ': ',
                                            style: TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold)),
                                        TextSpan(
                                            style: GoogleFonts.livvic(
                                                fontSize: 14,
                                                color: Color(0xff3C3C3C)),
                                            text: deliveryDetails.isDelivered
                                                ? 'Delivered'
                                                : 'Out for Delivery'),
                                      ])),
                                      SizedBox(width: 20),
                                      Spacer(),
                                      Text.rich(TextSpan(children: [
                                        TextSpan(
                                            text: 'Delivered on',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 13.5,
                                                color: Color(0xff3F3F3F))),
                                        TextSpan(
                                            text: ': ',
                                            style: TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold)),
                                        TextSpan(
                                            style: GoogleFonts.livvic(
                                                fontSize: 14,
                                                color: Color(0xff3C3C3C)),
                                            text:
                                                '${deliveryDetails.deliveredOn?.goodDayDate() ?? '-'} ${deliveryDetails.deliveredOn?.goodTime() ?? ''}'),
                                      ])),
                                    ],
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Address:',
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 13.5,
                                        color: Color(0xff3F3F3F)),
                                  ),
                                  SizedBox(height: 3),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          '${deliveryDetails.userAddress?.flatHouse ?? '-'}, ${deliveryDetails.userAddress?.area ?? ''}, ${deliveryDetails.userAddress?.city ?? ''}, ${deliveryDetails.userAddress?.state ?? ''}, ${deliveryDetails.userAddress?.pincode ?? ''}',
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: GoogleFonts.livvic(
                                              fontSize: 14,
                                              color: Color(0xff3C3C3C)),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Tracking Link:',
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 13.5,
                                        color: Color(0xff3F3F3F)),
                                  ),
                                  SizedBox(height: 3),
                                  Text(
                                    deliveryDetails.trackingLink,
                                    style: GoogleFonts.livvic(
                                        fontSize: 14, color: Color(0xff3C3C3C)),
                                  ),
                                  SizedBox(height: 8),
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: 50,
                                        child: Text('Sr No.'),
                                      ),
                                      SizedBox(width: 8),
                                      SizedBox(
                                        width: 60,
                                        child: Text('Image'),
                                      ),
                                      SizedBox(width: 12),
                                      Expanded(child: Text("Code")),
                                      SizedBox(width: 8),
                                      Expanded(
                                          flex: 2, child: Text("Product Name")),
                                      SizedBox(width: 8),
                                      SizedBox(
                                          width: 100,
                                          child: Text("Qty Dispatched")),
                                    ],
                                  ),
                                  Divider(),
                                  ...List.generate(
                                    deliveryDetails.dispatchedProduct.length,
                                    (index) {
                                      final deliveryProductData =
                                          deliveryDetails
                                              .dispatchedProduct[index];
                                      final productData = orderData
                                          .orderProductData
                                          .firstWhereOrNull((element) =>
                                              (element.productId ==
                                                      deliveryProductData
                                                          .productId &&
                                                  element.variantId ==
                                                      deliveryProductData
                                                          .variantId));
                                      return Row(
                                        children: [
                                          SizedBox(
                                            width: 50,
                                            child: Text((index + 1).toString()),
                                          ),
                                          SizedBox(width: 8),
                                          Container(
                                            width: 60,
                                            clipBehavior: Clip.antiAlias,
                                            decoration: BoxDecoration(
                                                shape: BoxShape.circle),
                                            child: ImageNetwork(
                                              image: productData
                                                      ?.orderProductExtraData
                                                      ?.variantImage ??
                                                  '',
                                              height: 50,
                                              width: 50,
                                              fitWeb: BoxFitWeb.cover,
                                              onError:
                                                  Icon(CupertinoIcons.camera),
                                              onLoading: Container(
                                                height: 50,
                                                width: 50,
                                                color: Colors.grey,
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 12),
                                          Expanded(
                                              child: Text(productData
                                                      ?.orderProductExtraData
                                                      ?.productSku ??
                                                  '-')),
                                          SizedBox(width: 8),
                                          Expanded(
                                              flex: 2,
                                              child: Text(
                                                productData
                                                        ?.orderProductExtraData
                                                        ?.productName ??
                                                    '-',
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              )),
                                          SizedBox(width: 8),
                                          SizedBox(
                                              width: 100,
                                              child: Text(deliveryProductData
                                                  .qty
                                                  .toString())),
                                        ],
                                      );
                                    },
                                  ),
                                  SizedBox(height: 20),
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 40),
                                          backgroundColor: themeColor,
                                          overlayColor: Colors.transparent,
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8)),
                                        ),
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                        },
                                        child: Text(
                                          'Done',
                                          style: GoogleFonts.livvic(
                                              color: Colors.white,
                                              // fontSize: 15,
                                              fontWeight: FontWeight.w500),
                                        )),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  } else {
                    showErrorAppSnackBar(context, 'Order details not found');
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(5)),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 13),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "Delivery Details",
                        style: GoogleFonts.zcoolXiaoWei(
                            fontSize: 20, color: const Color(0xff3E3E3E)),
                      ),
                      const SizedBox(height: 12),
                      Text(deliveryDetails.deliveryPartner),
                      const SizedBox(height: 4),
                      Text(deliveryDetails.trackingId),
                      const SizedBox(height: 4),
                      Text(
                          'Dispatched Items: ${deliveryDetails.dispatchedProduct.length.toString()}'),
                    ],
                  ),
                ),
              );
            } else {
              return Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(5)),
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 13),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Delivery Details",
                      style: GoogleFonts.zcoolXiaoWei(
                          fontSize: 20, color: const Color(0xff3E3E3E)),
                    ),
                    const SizedBox(height: 12),
                    const Text("No details found"),
                  ],
                ),
              );
            }
          }
          return Container(
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(5)),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 13),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "Delivery Details",
                  style: GoogleFonts.zcoolXiaoWei(
                      fontSize: 20, color: const Color(0xff3E3E3E)),
                ),
                const SizedBox(height: 12),
                const Text("No details found"),
              ],
            ),
          );
        },
      );
  }
  return Text(
    '',
    style: GoogleFonts.aBeeZee(
      fontSize: 13.5,
    ),
  );
}

InkWell addNewProductButton(SessionModel sessionModel, BuildContext context,
    List<DisplayProductsDetails> displayOrderDetails, StateSetter setState2) {
  return InkWell(
    hoverColor: Colors.transparent,
    splashColor: Colors.transparent,
    highlightColor: Colors.transparent,
    onTap: () async {
      final userSessionProductSnap = await FBFireStore.variants
          .where(FieldPath.documentId,
              whereIn:
                  sessionModel.finalVariantIds.map((e) => e.variantId).toList())
          .get();
      final userSessionProduct = userSessionProductSnap.docs
          .map((e) => NewVariantModel.fromSnap(e))
          .toList();
      showDialog(
        context: context,
        builder: (context) {
          bool isLoading = false;
          List<NewVariantModel> tempOrderedVariants = [];
          List<NewVariantModel> productFromSearch = [];
          tempOrderedVariants.addAll(displayOrderDetails.map((e) => e.variant));
          return StatefulBuilder(builder: (context, setState3) {
            return Dialog(
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
              child: Container(
                constraints: const BoxConstraints(
                  maxHeight: 800,
                  maxWidth: 1000,
                ),
                child: SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    // mainAxisSize:
                    //     MainAxisSize.min,
                    children: [
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          Text(
                            'Products',
                            style: GoogleFonts.zcoolXiaoWei(fontSize: 25),
                          ),
                          Spacer(),
                          InkWell(
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onTap: () {
                                addProductBySearch(context, tempOrderedVariants,
                                    productFromSearch, setState3);
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 5),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Color(0xffe2e8f0)),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  children: [
                                    const Icon(CupertinoIcons.search, size: 18),
                                    SizedBox(width: 4),
                                    Text(
                                      "Search",
                                      style: TextStyle(
                                          fontSize: 13.5,
                                          letterSpacing: 1,
                                          fontWeight: FontWeight.w500),
                                    )
                                  ],
                                ),
                              ))
                        ],
                      ),
                      const SizedBox(height: 20),
                      if (userSessionProduct.isNotEmpty)
                        Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "From User",
                                style: GoogleFonts.mulish(
                                  fontSize: 15,
                                  color: themeColor,
                                ),
                              ),
                              SizedBox(height: 7),
                              Divider(height: 0, color: dividerColor),
                              SizedBox(height: 15),
                              StaggeredGrid.extent(
                                maxCrossAxisExtent: 200,
                                mainAxisSpacing: 15,
                                crossAxisSpacing: 10,
                                children: [
                                  ...List.generate(
                                    userSessionProduct.length,
                                    (index) {
                                      bool isSelected = tempOrderedVariants
                                          .map((e) => e.docId)
                                          .contains(
                                              userSessionProduct[index].docId);
                                      return Container(
                                        height: 250,
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(
                                            color: const Color.fromARGB(
                                                255, 248, 248, 248),
                                            borderRadius:
                                                BorderRadius.circular(8)),
                                        padding: const EdgeInsets.all(10),
                                        child: Stack(
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                  height: 180,
                                                  width: double.maxFinite,
                                                  clipBehavior: Clip.antiAlias,
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              7)),
                                                  child: Image.network(
                                                    userSessionProduct[index]
                                                        .images
                                                        .first,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                                const SizedBox(height: 5),
                                                Expanded(
                                                  child: Text(
                                                    capilatlizeFirstLetter(
                                                        userSessionProduct[
                                                                index]
                                                            .lowerName),
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: GoogleFonts.mulish(
                                                        fontSize: 14,
                                                        color: const Color(
                                                            0xff3E3E3E)),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Align(
                                              alignment: Alignment.topRight,
                                              child: Checkbox(
                                                side: const BorderSide(
                                                    color: Colors.grey),
                                                checkColor: themeColor,
                                                fillColor:
                                                    const WidgetStatePropertyAll(
                                                        Colors.white),
                                                activeColor: Colors.white,
                                                hoverColor: Colors.transparent,
                                                // focusColor: Colors.transparent,
                                                overlayColor:
                                                    const WidgetStatePropertyAll(
                                                        Colors.transparent),
                                                value: isSelected,
                                                onChanged: (value) async {
                                                  if (value == true) {
                                                    tempOrderedVariants.add(
                                                        userSessionProduct[
                                                            index]);
                                                    setState3(() {});
                                                  }
                                                  if (value == false) {
                                                    tempOrderedVariants
                                                        .removeWhere((element) =>
                                                            element.docId ==
                                                            userSessionProduct[
                                                                    index]
                                                                .docId);
                                                    setState3(() {});
                                                  }
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 15),
                            ]),
                      if (productFromSearch.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "From Search",
                              style: GoogleFonts.mulish(
                                fontSize: 15,
                                color: themeColor,
                              ),
                            ),
                            SizedBox(height: 7),
                            Divider(height: 0, color: dividerColor),
                            SizedBox(height: 15),
                            StaggeredGrid.extent(
                              maxCrossAxisExtent: 200,
                              mainAxisSpacing: 15,
                              crossAxisSpacing: 10,
                              children: [
                                ...List.generate(
                                  productFromSearch.length,
                                  (index) {
                                    bool isSelected = tempOrderedVariants
                                        .map((e) => e.docId)
                                        .contains(
                                            productFromSearch[index].docId);
                                    return Container(
                                      height: 250,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: BoxDecoration(
                                          color: const Color.fromARGB(
                                              255, 248, 248, 248),
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                      padding: const EdgeInsets.all(10),
                                      child: Stack(
                                        children: [
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Container(
                                                height: 180,
                                                width: double.maxFinite,
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            7)),
                                                child: Image.network(
                                                  productFromSearch[index]
                                                      .images
                                                      .first,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                              const SizedBox(height: 5),
                                              Expanded(
                                                child: Text(
                                                  capilatlizeFirstLetter(
                                                      productFromSearch[index]
                                                          .lowerName),
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: GoogleFonts.mulish(
                                                      fontSize: 14,
                                                      color: const Color(
                                                          0xff3E3E3E)),
                                                ),
                                              ),
                                            ],
                                          ),
                                          Align(
                                            alignment: Alignment.topRight,
                                            child: Checkbox(
                                              side: const BorderSide(
                                                  color: Colors.grey),
                                              checkColor: themeColor,
                                              fillColor:
                                                  const WidgetStatePropertyAll(
                                                      Colors.white),
                                              activeColor: Colors.white,
                                              hoverColor: Colors.transparent,
                                              // focusColor: Colors.transparent,
                                              overlayColor:
                                                  const WidgetStatePropertyAll(
                                                      Colors.transparent),
                                              value: isSelected,
                                              onChanged: (value) async {
                                                if (value == true) {
                                                  tempOrderedVariants.add(
                                                      productFromSearch[index]);
                                                  setState3(() {});
                                                }
                                                if (value == false) {
                                                  tempOrderedVariants
                                                      .removeWhere((element) =>
                                                          element.docId ==
                                                          productFromSearch[
                                                                  index]
                                                              .docId);
                                                  setState3(() {});
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      const SizedBox(height: 15),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: isLoading
                            ? [
                                const SizedBox(
                                  height: 28,
                                  width: 28,
                                  child: Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                )
                              ]
                            : [
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: const Text("Cancel"),
                                ),
                                const SizedBox(width: 10),
                                ElevatedButton(
                                    onPressed: () async {
                                      if (isLoading) return;

                                      isLoading = true;
                                      setState3(() {});

                                      try {
                                        for (var variant
                                            in tempOrderedVariants) {
                                          // final selectedVariantIds =
                                          //     displayOrderDetails
                                          //         .map((e) => e.variant.docId)
                                          //         .toList();
                                          // final productExistInOrder =
                                          //     displayOrderDetails
                                          //         .firstWhereOrNull((element) {
                                          //   return selectedVariantIds
                                          //       .contains(variant.docId);
                                          // });
                                          final productExistInOrder =
                                              displayOrderDetails
                                                  .firstWhereOrNull((element) =>
                                                      element.variant.docId ==
                                                      variant.docId);
                                          if (productExistInOrder == null) {
                                            final productSnap =
                                                await FBFireStore.products
                                                    .doc(variant.productId)
                                                    .get();
                                            final productData =
                                                ProductModel.fromDocSnap(
                                                    productSnap);

                                            final vendorSnap = await FBFireStore
                                                .vendors
                                                .doc(productData.vendorDocId)
                                                .get();
                                            final vendorData =
                                                VendorModel.fromDocSnap(
                                                    vendorSnap);
                                            displayOrderDetails
                                                .add(DisplayProductsDetails(
                                                    product: productData,
                                                    variant: variant,
                                                    vendorData: vendorData,
                                                    orderData: OrderProductData(
                                                      vendorId:
                                                          vendorData.docId,
                                                      id: getRandomId(6),
                                                      productId:
                                                          productData.docId!,
                                                      variantId: variant.docId!,
                                                      qty: 0,
                                                      currentStatus: null,
                                                      purchasePrice:
                                                          variant.fixedprice,
                                                      sellingPrice: 0,
                                                      userNote: '',
                                                      adminNote: '',
                                                      productSKu:
                                                          productData.sku,
                                                      deliveryDate: null,
                                                      orderProductExtraData: OrderProductExtraData(
                                                          vendorDocId: productData
                                                              .vendorDocId,
                                                          vendorName: '',
                                                          vendorEmail: '',
                                                          vendorPhone: '',
                                                          productDocId:
                                                              productData.docId ??
                                                                  '',
                                                          productSku:
                                                              productData.sku,
                                                          productName:
                                                              productData.name,
                                                          productDesc:
                                                              productData
                                                                  .description,
                                                          variantDocId:
                                                              variant.docId ??
                                                                  '',
                                                          variantImage: variant
                                                              .images.first,
                                                          variantPurchasePrice:
                                                              variant
                                                                  .fixedprice,
                                                          variantDescription:
                                                              variant
                                                                  .description,
                                                          variantDetailTypes:
                                                              variant
                                                                  .detailTypes),
                                                    )));
                                          }
                                        }

                                        isLoading = false;
                                        setState3(() {});
                                        Navigator.of(context).pop();
                                        setState2(() {});
                                      } on Exception catch (e) {
                                        debugPrint(e.toString());
                                        isLoading = false;
                                        setState3(() {});
                                      }
                                    },
                                    child: const Text('Save'))
                              ],
                      )
                    ],
                  ),
                ),
              ),
            );
          });
        },
      );
    },
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffe2e8f0)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const Icon(CupertinoIcons.add, size: 18),
          SizedBox(width: 4),
          Text(
            "Product",
            style: TextStyle(
                fontSize: 13.5, letterSpacing: 1, fontWeight: FontWeight.w500),
          )
        ],
      ),
    ),
  );
}

/* Widget addNewProductButton(BuildContext context) {
  return InkWell(
    hoverColor: Colors.transparent,
    splashColor: Colors.transparent,
    highlightColor: Colors.transparent,
    onTap: () async {
      if (sessionModel == null) return;
      final userSessionProductSnap = await FBFireStore.variants
          .where(FieldPath.documentId,
              whereIn: sessionModel?.finalVariantIds
                      .map((e) => e.variantId)
                      .toList() ??
                  [])
          .get();
      final userSessionProduct = userSessionProductSnap.docs
          .map((e) => NewVariantModel.fromSnap(e))
          .toList();
      showDialog(
        context: context,
        builder: (context) {
          bool isLoading = false;
          List<NewVariantModel> tempOrderedVariants = [];
          List<NewVariantModel> productFromSearch = [];
          tempOrderedVariants.addAll(displayOrderDetails.map((e) => e.variant));
          return StatefulBuilder(builder: (context, setState2) {
            return Dialog(
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
              child: Container(
                constraints: const BoxConstraints(
                  maxHeight: 800,
                  maxWidth: 1000,
                ),
                child: SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    // mainAxisSize:
                    //     MainAxisSize.min,
                    children: [
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          Text(
                            'Products',
                            style: GoogleFonts.zcoolXiaoWei(fontSize: 25),
                          ),
                          Spacer(),
                          InkWell(
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onTap: () {
                                addProductBySearch(context, tempOrderedVariants,
                                    productFromSearch, setState2);
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 5),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Color(0xffe2e8f0)),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  children: [
                                    const Icon(CupertinoIcons.search, size: 18),
                                    SizedBox(width: 4),
                                    Text(
                                      "Search",
                                      style: TextStyle(
                                          fontSize: 13.5,
                                          letterSpacing: 1,
                                          fontWeight: FontWeight.w500),
                                    )
                                  ],
                                ),
                              ))
                        ],
                      ),
                      const SizedBox(height: 20),
                      if (userSessionProduct.isNotEmpty)
                        Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "From User",
                                style: GoogleFonts.mulish(
                                  fontSize: 15,
                                  color: themeColor,
                                ),
                              ),
                              SizedBox(height: 7),
                              Divider(height: 0, color: dividerColor),
                              SizedBox(height: 15),
                              StaggeredGrid.extent(
                                maxCrossAxisExtent: 200,
                                mainAxisSpacing: 15,
                                crossAxisSpacing: 10,
                                children: [
                                  ...List.generate(
                                    userSessionProduct.length,
                                    (index) {
                                      bool isSelected = tempOrderedVariants
                                          .map((e) => e.docId)
                                          .contains(
                                              userSessionProduct[index].docId);
                                      return Container(
                                        height: 250,
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(
                                            color: const Color.fromARGB(
                                                255, 248, 248, 248),
                                            borderRadius:
                                                BorderRadius.circular(8)),
                                        padding: const EdgeInsets.all(10),
                                        child: Stack(
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                  height: 180,
                                                  width: double.maxFinite,
                                                  clipBehavior: Clip.antiAlias,
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              7)),
                                                  child: Image.network(
                                                    userSessionProduct[index]
                                                        .images
                                                        .first,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                                const SizedBox(height: 5),
                                                Expanded(
                                                  child: Text(
                                                    capilatlizeFirstLetter(
                                                        userSessionProduct[
                                                                index]
                                                            .lowerName),
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: GoogleFonts.mulish(
                                                        fontSize: 14,
                                                        color: const Color(
                                                            0xff3E3E3E)),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Align(
                                              alignment: Alignment.topRight,
                                              child: Checkbox(
                                                side: const BorderSide(
                                                    color: Colors.grey),
                                                checkColor: themeColor,
                                                fillColor:
                                                    const WidgetStatePropertyAll(
                                                        Colors.white),
                                                activeColor: Colors.white,
                                                hoverColor: Colors.transparent,
                                                // focusColor: Colors.transparent,
                                                overlayColor:
                                                    const WidgetStatePropertyAll(
                                                        Colors.transparent),
                                                value: isSelected,
                                                onChanged: (value) async {
                                                  if (value == true) {
                                                    tempOrderedVariants.add(
                                                        userSessionProduct[
                                                            index]);
                                                    setState2(() {});
                                                  }
                                                  if (value == false) {
                                                    tempOrderedVariants
                                                        .removeWhere((element) =>
                                                            element.docId ==
                                                            userSessionProduct[
                                                                    index]
                                                                .docId);
                                                    setState2(() {});
                                                  }
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 15),
                            ]),
                      if (productFromSearch.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "From Search",
                              style: GoogleFonts.mulish(
                                fontSize: 15,
                                color: themeColor,
                              ),
                            ),
                            SizedBox(height: 7),
                            Divider(height: 0, color: dividerColor),
                            SizedBox(height: 15),
                            StaggeredGrid.extent(
                              maxCrossAxisExtent: 200,
                              mainAxisSpacing: 15,
                              crossAxisSpacing: 10,
                              children: [
                                ...List.generate(
                                  productFromSearch.length,
                                  (index) {
                                    bool isSelected = tempOrderedVariants
                                        .map((e) => e.docId)
                                        .contains(
                                            productFromSearch[index].docId);
                                    return Container(
                                      height: 250,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: BoxDecoration(
                                          color: const Color.fromARGB(
                                              255, 248, 248, 248),
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                      padding: const EdgeInsets.all(10),
                                      child: Stack(
                                        children: [
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Container(
                                                height: 180,
                                                width: double.maxFinite,
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            7)),
                                                child: Image.network(
                                                  productFromSearch[index]
                                                      .images
                                                      .first,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                              const SizedBox(height: 5),
                                              Expanded(
                                                child: Text(
                                                  capilatlizeFirstLetter(
                                                      productFromSearch[index]
                                                          .lowerName),
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: GoogleFonts.mulish(
                                                      fontSize: 14,
                                                      color: const Color(
                                                          0xff3E3E3E)),
                                                ),
                                              ),
                                            ],
                                          ),
                                          Align(
                                            alignment: Alignment.topRight,
                                            child: Checkbox(
                                              side: const BorderSide(
                                                  color: Colors.grey),
                                              checkColor: themeColor,
                                              fillColor:
                                                  const WidgetStatePropertyAll(
                                                      Colors.white),
                                              activeColor: Colors.white,
                                              hoverColor: Colors.transparent,
                                              // focusColor: Colors.transparent,
                                              overlayColor:
                                                  const WidgetStatePropertyAll(
                                                      Colors.transparent),
                                              value: isSelected,
                                              onChanged: (value) async {
                                                if (value == true) {
                                                  tempOrderedVariants.add(
                                                      productFromSearch[index]);
                                                  setState2(() {});
                                                }
                                                if (value == false) {
                                                  tempOrderedVariants
                                                      .removeWhere((element) =>
                                                          element.docId ==
                                                          productFromSearch[
                                                                  index]
                                                              .docId);
                                                  setState2(() {});
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      const SizedBox(height: 15),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: isLoading
                            ? [
                                const SizedBox(
                                  height: 28,
                                  width: 28,
                                  child: Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                )
                              ]
                            : [
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: const Text("Cancel"),
                                ),
                                const SizedBox(width: 10),
                                ElevatedButton(
                                    onPressed: () async {
                                      isLoading = true;
                                      setState2(() {});

                                      try {
                                        for (var variant
                                            in tempOrderedVariants) {
                                          final selectedVariantIds =
                                              displayOrderDetails
                                                  .map((e) => e.variant.docId)
                                                  .toList();
                                          final existingorderData =
                                              displayOrderDetails
                                                  .firstWhereOrNull((element) {
                                            return selectedVariantIds
                                                .contains(variant.docId);
                                          });

                                          if (existingorderData == null) {
                                            final productSnap =
                                                await FBFireStore.products
                                                    .doc(variant.productId)
                                                    .get();
                                            final productData =
                                                ProductModel.fromDocSnap(
                                                    productSnap);

                                            final vendorSnap = await FBFireStore
                                                .vendors
                                                .doc(productData.vendorDocId)
                                                .get();
                                            final vendorData =
                                                VendorModel.fromDocSnap(
                                                    vendorSnap);
                                            displayOrderDetails.add(
                                                DisplayProductsDetails(
                                                    product: productData,
                                                    variant: variant,
                                                    vendorData: vendorData,
                                                    orderData: OrderProductData(
                                                        vendorId:
                                                            vendorData.docId,
                                                        id: getRandomId(6),
                                                        productId:
                                                            productData.docId,
                                                        variantId:
                                                            variant.docId!,
                                                        qty: 0,
                                                        currentStatus: null,
                                                        purchasePrice:
                                                            variant.fixedprice,
                                                        sellingPrice: 0,
                                                        userNote: '',
                                                        adminNote: '',
                                                        productSKu:
                                                            productData.sku,
                                                        deliveryDate: null)));
                                          }
                                        }

                                        isLoading = false;
                                        setState2(() {});
                                        Navigator.of(context).pop();
                                        setState(() {});
                                      } on Exception catch (e) {
                                        debugPrint(e.toString());
                                        isLoading = false;
                                        setState2(() {});
                                      }
                                    },
                                    child: const Text('Save'))
                              ],
                      )
                    ],
                  ),
                ),
              ),
            );
          });
        },
      );
    },
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffe2e8f0)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const Icon(CupertinoIcons.add, size: 18),
          SizedBox(width: 4),
          Text(
            "Product",
            style: TextStyle(
                fontSize: 13.5, letterSpacing: 1, fontWeight: FontWeight.w500),
          )
        ],
      ),
    ),
  );
}
 */
Future<dynamic> addProductBySearch(
    BuildContext context,
    List<NewVariantModel> tempOrderedVariants,
    List<NewVariantModel> productFromSearch,
    StateSetter setState2) {
  return showDialog(
    context: context,
    builder: (context) {
      Timer? debounce;
      List<NewVariantModel> searchedList = [];
      getSearchVariant(String searchedStr) async {
        searchedList.clear();
        final searchedSnap = await FBFireStore.variants
            .where('lowerName', isGreaterThanOrEqualTo: searchedStr)
            .where('lowerName', isLessThanOrEqualTo: "$searchedStr\uf7ff")
            .limit(10)
            .get();
        searchedList
            .addAll(searchedSnap.docs.map((e) => NewVariantModel.fromSnap(e)));
      }

      return Dialog(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        child: StatefulBuilder(builder: (context, searchSetState) {
          return Container(
            constraints: const BoxConstraints(maxWidth: 800),
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Search Product',
                    style: GoogleFonts.zcoolXiaoWei(
                        fontSize: 25, color: themeColor)),
                const SizedBox(height: 15),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: ' search product name',
                    hintStyle: TextStyle(
                        color: Color(0xff737373),
                        fontSize: 14.5,
                        fontWeight: FontWeight.w500),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                  ),
                  onChanged: (value) async {
                    if (debounce?.isActive ?? false) {
                      debounce?.cancel();
                    }
                    debounce =
                        Timer(const Duration(milliseconds: 500), () async {
                      if (value.trim().isNotEmpty) {
                        await getSearchVariant(value.toLowerCase().trim());
                        searchSetState(() {});
                      } else {
                        searchedList.clear();
                        searchSetState(() {});
                      }
                    });
                  },
                ),
                if (searchedList.isNotEmpty) ...[
                  const SizedBox(height: 10),
                  const Row(
                    children: [
                      SizedBox(width: 50, child: Text('Sr No')),
                      SizedBox(width: 10),
                      SizedBox(width: 100, child: Center(child: Text('Image'))),
                      SizedBox(width: 10),
                      Expanded(child: Text('Product Name')),
                      SizedBox(width: 10),
                    ],
                  ),
                  const SizedBox(height: 10),
                  ...List.generate(
                    searchedList.length,
                    (index) {
                      final variant = searchedList[index];
                      return Padding(
                        padding: EdgeInsets.only(top: index == 0 ? 0 : 8),
                        child: InkWell(
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () {
                            if (tempOrderedVariants
                                    .map((e) => e.docId)
                                    .contains(variant.docId) ||
                                productFromSearch
                                    .map((e) => e.docId)
                                    .contains(variant.docId)) {
                              showAppSnackBar(context, 'Already in cart');
                              return;
                            }

                            productFromSearch.add(variant);
                            tempOrderedVariants.add(variant);
                            // selectedVariantList
                            //     .add(variant);
                            Navigator.of(context).pop();
                            setState2(() {});
                          },
                          child: Row(
                            children: [
                              SizedBox(
                                  width: 50,
                                  child: Text((index + 1).toString())),
                              const SizedBox(width: 10),
                              SizedBox(
                                width: 100,
                                child: Center(
                                  child: Container(
                                      height: 50,
                                      width: 50,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: const BoxDecoration(
                                          shape: BoxShape.circle),
                                      child:
                                          Image.network(variant.images.first)),
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                  child: Text(capilatlizeFirstLetter(
                                      variant.lowerName))),
                            ],
                          ),
                        ),
                      );
                    },
                  )
                ],
              ],
            ),
          );
        }),
      );
    },
  );
}
