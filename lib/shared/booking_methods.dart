import 'package:intl/intl.dart';
import 'package:wedding_super_admin/models/bookings_model.dart';

List<String> generateTimeSlotsWithBlocked(DateTime startDate, DateTime endDate,
    int slotGap, List<BlockedSlotModel> blockedList) {
  List<String> slots = [];
  DateFormat dateFormat = DateFormat('yyyy-MM-dd');
  DateFormat timeFormat = DateFormat('hh:mm a');

  DateTime current = startDate;
  DateTime now = DateTime.now();

  while (current.isBefore(endDate)) {
    String currentDateStr = dateFormat.format(current);

    bool isFullDayBlocked = blockedList.any((blocked) {
      DateTime blockedDateTime = blocked.blockedDateTime;
      return dateFormat.format(blockedDateTime) == currentDateStr &&
          blocked.dateBlock;
    });

    if (isFullDayBlocked) {
      current = DateTime(current.year, current.month, current.day + 1);
      continue;
    }
    bool isBlocked = false;

    if (currentDateStr == dateFormat.format(now) && current.isBefore(now)) {
      current = current.add(Duration(minutes: slotGap));
      continue;
    }
    for (var blocked in blockedList) {
      DateTime blockedDateTime = blocked.blockedDateTime;
      String blockedDateStr = dateFormat.format(blockedDateTime);

      if (blockedDateStr == currentDateStr &&
          current.isAtSameMomentAs(blockedDateTime)) {
        current = current.add(Duration(minutes: blocked.slotGap));
        isBlocked = true;
        break;
      }
    }

    if (!isBlocked) {
      DateTime nextSlot = current.add(Duration(minutes: slotGap));
      if (nextSlot.isAfter(endDate)) break;

      slots.add(
          '${timeFormat.format(current)} - ${timeFormat.format(nextSlot)}');
      current = nextSlot;
    }
  }

  return slots;
}

Map<String, DateTime> stringToDateTime(String slot) {
  List<String> times = slot.split(' - ');
  if (times.length != 2) {
    throw const FormatException('Invalid time range format');
  }

  String startTime = times[0].trim();
  String endTime = times[1].trim();

  DateFormat format12 = DateFormat('hh:mm a');
  DateFormat format24 = DateFormat('HH:mm');

  DateTime parsedStart = parseTime(startTime, format12, format24);

  DateTime parsedEnd = parseTime(endTime, format12, format24);

  return {
    'startTime': parsedStart,
    'endTime': parsedEnd,
  };
}

String dateTimeToSlotString(DateTime startTime, DateTime endTime) {
  final DateFormat format12 = DateFormat('hh:mm a');
  String start = format12.format(startTime);
  String end = format12.format(endTime);
  return '$start - $end';
}

DateTime parseTime(String time, DateFormat format12, DateFormat format24) {
  if (time.contains('AM') || time.contains('PM')) {
    return format12.parse(time);
  } else {
    return format24.parse(time);
  }
}

// String to12HourFormat(DateTime startTime, DateTime endTime) {
//   DateFormat format12 = DateFormat('hh:mm a');

//   String formattedStart = format12.format(startTime);
//   String formattedEnd = format12.format(endTime);

//   return '$formattedStart - $formattedEnd';
// }
