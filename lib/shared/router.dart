import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import 'package:wedding_super_admin/views/blogs/blogs.dart';
import 'package:wedding_super_admin/views/bookings/bookings.dart';
import 'package:wedding_super_admin/views/bookings/widget/booking_form.dart';
import 'package:wedding_super_admin/views/gallery/gallery.dart';
import 'package:wedding_super_admin/views/offerings/widgets/subcategory_form.dart';
import 'package:wedding_super_admin/views/packages/package_orders.dart';
import 'package:wedding_super_admin/views/questionaire/questionaire.dart';
import 'package:wedding_super_admin/views/questionaire/widgets/questionare_form.dart';
import 'package:wedding_super_admin/views/sessions/session_page.dart';
import 'package:wedding_super_admin/views/sessions/sessions.dart';
import 'package:wedding_super_admin/views/teamMember/team_member.dart';
import 'package:wedding_super_admin/views/teamMember/widgets/team_booking_list.dart';
import 'package:wedding_super_admin/views/teamMember/widgets/team_member_details.dart';
import 'package:wedding_super_admin/views/teamMember/widgets/team_session_list.dart';
import 'package:wedding_super_admin/views/Api/ui.dart';
import 'package:wedding_super_admin/views/testimonials/testimonials.dart';
import 'package:wedding_super_admin/views/users/users.dart';
import 'package:wedding_super_admin/views/users/widgets/user_details_page.dart';
import 'package:wedding_super_admin/views/vendor%20order/widget/vendor_order_details.dart';
import '../controller/home_ctrl.dart';
import '../controller/recommended_set_controller.dart';
import '../models/session_model.dart';
import '../views/authentication/signin.dart';
import '../views/blogs/widgets/blog_form.dart';
import '../views/bookings/widget/booking_setting_page.dart';
import '../views/dashboard/dashboard.dart';
import '../views/offerings/offerings.dart';
import '../views/orders/widgets/order_detail_page.dart';
import '../views/packages/packages.dart';
import '../views/sessions/widgets/recommended_set.dart';
import '../views/vendor order/vendororder.dart';
import '../views/vendors/widgets/product/allproducts.dart';
import '../views/offerings/widgets/offering_form.dart';
import '../views/orders/orders.dart';
import '../views/settings/settings.dart';
import '../views/vendors/vendors.dart';
import '../views/vendors/widgets/product/product_form.dart';
import '../views/vendors/widgets/vendor_form.dart';
import '../views/wrapper/wrapper.dart';
import 'error_page.dart';
import 'methods.dart';

const homeRoute = Routes.orders;

final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: Routes.signin,
  routes: _routes,
  redirect: redirector,
  errorBuilder: (context, state) => const ErrorPage(),
);

FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
  // return "/temp";
  // routeHistory.add(state.uri.path);
  // if (isLoggedIn() && state.fullPath == Routes.auth) {
  //   return routeHistory.reversed.elementAt(1);
  //   // return Routes.home;
  // }
  if (isLoggedIn()) {
    if (state.fullPath == Routes.signin) {
      if (Get.isRegistered<HomeCtrl>()) {
        Future.delayed(const Duration(milliseconds: 10))
            .then((value) => Get.find<HomeCtrl>().update());
      }
      return homeRoute;
    } else {
      if (Get.isRegistered<HomeCtrl>()) Get.find<HomeCtrl>().update();
      return null;
    }
  } else {
    return Routes.signin;
  }
}

List<RouteBase> get _routes {
  return <RouteBase>[
    // GoRoute(
    //   path: "/temp",
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       const NoTransitionPage(child: SimplePage()),
    // ),
    // GoRoute(
    //   path: '${Routes.category}/:name',
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       NoTransitionPage(
    //           child: CategoryWid(
    //     name: state.pathParameters['name'] ?? "",
    //   )),
    // ),
    ShellRoute(
      builder: (context, state, child) {
        if (!Get.isRegistered<HomeCtrl>()) Get.put(HomeCtrl());
        return DashboardScreen(child: child);
        // return SimplePage();
      },
      routes: [
        GoRoute(
          path: Routes.categories,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: OfferingsPage()),
        ),
        GoRoute(
            path: '${Routes.category}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) =>
                NoTransitionPage(
                    child: CategoriesForm(
                  categoryId: state.pathParameters['id'] as String,
                ))),
        GoRoute(
          path: Routes.vendors,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: VendorsPage()),
          // NoTransitionPage(
          //     child: Shopify(
          //         vendor: Get.find<HomeCtrl>()
          //             .vendors
          //             .firstWhere((element) => element.name == "shopify"))),
        ),
        GoRoute(
            path: '${Routes.vendor}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) =>
                NoTransitionPage(
                    child: VendorForm(
                  vendorId: state.pathParameters['id'] as String,
                ))),
        GoRoute(
            path: '${Routes.user}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) =>
                NoTransitionPage(
                    child: UserDetailsPage(
                  userDocId: state.pathParameters['id'] as String,
                ))),
        GoRoute(
          path: Routes.users,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: UsersPage()),
        ),
        GoRoute(
          path: Routes.teamMembers,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: TeamMember()),
        ),
        GoRoute(
            path: '${Routes.subcategory}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) {
              final vendorId = state.extra as String?;
              return NoTransitionPage(
                  child: SubCatAllProducts(
                vendorId: vendorId,
                subcatid: state.pathParameters['id'] as String,
              ));
            }),
        GoRoute(
            path: '${Routes.product}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) =>
                NoTransitionPage(
                    child: ProductForm(
                  productId: state.pathParameters['id'] != 'null'
                      ? state.pathParameters['id']
                      : null,
                  extraIds: state.extra as List<String?>,
                ))),
        GoRoute(
          path: Routes.testimonials,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: Testimonials()),
        ),
        GoRoute(
          path: Routes.vendorapi,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              NoTransitionPage(
                  child: PlatformApiPage(vendor: state.extra as VendorModel)),
        ),
        GoRoute(
          path: Routes.blogs,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: BlogsPage()),
        ),
        GoRoute(
            path: '${Routes.blog}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(
                  child: BlogForm(
                blogId: state.pathParameters['id'] != 'null'
                    ? state.pathParameters['id']
                    : null,
              ));
            }),
        GoRoute(
            path: '${Routes.subcategoryF}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) {
              final mainCatId = state.extra as String;
              return NoTransitionPage(
                  child: SubcategoryForm(
                mainCatId: mainCatId,
                subCatId: state.pathParameters['id'] != 'null'
                    ? state.pathParameters['id']
                    : null,
              ));
            }),
        GoRoute(
            path: '${Routes.questionaire}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(
                  child: QuestionareForm(
                offeringId: state.pathParameters['id'] != 'null'
                    ? state.pathParameters['id']
                    : null,
              ));
            }),
        GoRoute(
            path: '${Routes.booking}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(
                  child: BookingForm(
                bookingId: state.pathParameters['id'] != 'null'
                    ? state.pathParameters['id']
                    : null,
              ));
            }),
        GoRoute(
            path: '${Routes.order}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(
                  child: OrderDetailPage(
                orderDocId: state.pathParameters['id'] as String,
              ));
            }),
        GoRoute(
            path: '${Routes.session}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) {
              if (!Get.isRegistered<RecommendedSetController>()) {
                Get.put(RecommendedSetController());
              }
              return NoTransitionPage(
                  child: SingleSessionPage(
                sessionId: state.pathParameters['id']!,
              ));
            }),
        GoRoute(
          path: Routes.bookings,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: BookingsPage()),
        ),
        GoRoute(
          path: Routes.sessions,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: AllActiceSessionsPage()),
        ),
        GoRoute(
          path: Routes.gallery,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: Gallery()),
        ),
        GoRoute(
          path: Routes.orders,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: OrdersPage()),
        ),
        GoRoute(
          path: Routes.bookingSetting,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: BookingSettings()),
        ),
        GoRoute(
          path: Routes.packages,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: Packages()),
        ),
        GoRoute(
            path: '${Routes.vendorOrder}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) =>
                NoTransitionPage(
                    child: VendorOrderDetails(
                  vendorOrderId: state.pathParameters['id'] as String,
                ))),
        GoRoute(
            path: '${Routes.teamMember}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) =>
                NoTransitionPage(
                    child: TeamMemberDetails(
                  teamMemeberId: state.pathParameters['id'] as String,
                ))),
        GoRoute(
            path: '${Routes.teamSessions}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) =>
                NoTransitionPage(
                    child: TeamSessionList(
                        teamMemberId: state.pathParameters['id'] as String))),
        GoRoute(
            path: '${Routes.teamBookings}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) =>
                NoTransitionPage(
                    child: TeamBookingList(
                        teamMemberId: state.pathParameters['id'] as String))),
        GoRoute(
          path: Routes.settings,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: SettingsPage()),
        ),
        GoRoute(
            path: '${Routes.sessionset}/:id',
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(
                  child: RecommendedSetSelection(
                sessionId: state.pathParameters['id']!,
                stylerProducts: state.extra as StylerProducts?,
              ));
            }),
        GoRoute(
          path: Routes.questionaires,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: QuestionairePage()),
        ),
        GoRoute(
          path: Routes.packageOrders,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: PackageOrdersPage()),
        ),
        GoRoute(
          path: Routes.dashboard,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: DashBoardPage()),
        ),
        GoRoute(
          path: Routes.vendorOrders,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: VendorOrders()),
        ),
      ],
    ),
    GoRoute(
      path: Routes.signin,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: LoginScreen()),
    ),
    // GoRoute(
    //   path: Routes.dashboard,
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       const NoTransitionPage(child: DashboardScreen()),
    // ),
  ];
}

class Routes {
  static const signin = '/signin';
  static const dashboard = '/dashboard';
  static const vendors = '/vendors';
  static const users = '/users';
  static const vendorOrders = '/vendororders';
  static const vendorOrder = '/vendororder';
  static const vendor = '/vendor';
  static const user = '/user';
  static const teamMembers = '/teamMembers';
  static const teamMember = '/teamMember';
  static const teamBookings = '/teamBookings';
  static const teamSessions = '/teamSessions';
  static const subcategory = '/sub-category';
  static const subcategoryF = '/sub-category-form';
  static const categories = '/categories';
  static const category = '/category';
  static const products = '/products';
  static const sessions = '/sessions';
  static const bookings = '/bookings';
  static const packages = '/packages';
  static const booking = '/booking';
  static const product = '/product';
  static const questionaires = '/questionaires';
  static const questionaire = '/questionaire';
  static const orders = '/orders';
  static const packageOrders = '/packageOrders';
  static const order = '/order';
  static const settings = '/settings';
  static const testimonials = '/testimonials';
  static const gallery = '/gallery';
  static const blogs = '/blogs';
  static const blog = '/blog';
  static const bookingSetting = '/bookingsetting';
  static const session = '/session';
  static const vendorapi = '/vendorapi';
  static const sessionset = '/sessionset';

  // static const notification = '/notification';
  // static const food = '/food';
  // static const report = '/report';
}

  // return isLoggedIn()
  //     ? (state.uri.path == Routes.auth ? Routes.home : null)
  //     : Routes.auth;
