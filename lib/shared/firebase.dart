import 'package:cloud_functions/cloud_functions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fb = FirebaseFirestore.instance;
  static final teamMember = fb.collection('teamMember');
  static final users = fb.collection('users');
  static final blogs = fb.collection('blogs');
  static final offerings = fb.collection('offerings');
  static final subCategories = fb.collection('subcategories');
  static final tags = fb.collection('tags');
  static final orders = fb.collection('orders');
  static final packageOrders = fb.collection('packageOrders');

  static final products = fb.collection('products');
  static final testimonials = fb.collection('testimonials');
  static final gallery = fb.collection('gallery');
  static final vendors = fb.collection('vendors');
  static final questionaire = fb.collection('questionaire');
  static final variants = fb.collection('variants');
  static final packages = fb.collection('packages');
  static final transaction = fb.collection('transaction');
  static final deliveryDetails = fb.collection('deliveryDetails');
  static final settings = fb.collection('settings').doc('sets');
  static final blocked =
      fb.collection('settings').doc('sets').collection('blocked');
  static final bookings = fb.collection('bookings');
  static final sessions = fb.collection('sessions');
  static final vendorOrder = fb.collection('vendorOrder');

  // static final amenitiesMore = fb.collection('amenities&more');
  // static final cities = fb.collection('cities');
  // static final areas = fb.collection('areas');
  // static final settings = fb.collection('settings');
}

class FBStorage {
  static final fbstore = FirebaseStorage.instance;
  static final offering = fbstore.ref().child('category');
  static final gallery = fbstore.ref().child('gallery');
  static final subcategory = fbstore.ref().child('subcategory');
  static final products = fbstore.ref().child('products');
  static final sessions = fbstore.ref().child('sessions');
  static final blogs = fbstore.ref().child('blogs');
  static final misc = fbstore.ref().child('misc');
  // static final banners = fbstore.ref().child('banner');
  // static final amenity = fbstore.ref().child('amenity');
  // static final food = fbstore.ref().child('food');
  // static final otherCertis = fb.ref().child('otherCertis');
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}
