import 'package:flutter/material.dart';

// const themeColor = Color(0xfff04f5f);
const dashboardColor = Color.fromARGB(255, 251, 237, 237);
const dashboardSelectedColor = Color(0xfff8f3f2);
const dashboardSelectedColor2 = Color.fromARGB(255, 255, 209, 196);
const tableHeaderColor = Color(0xfff0c8bd);
const darkMainColour = Color(0xfff5b7a3);
const lightSkinColor = Color(0xffFEF7F3);
const brownColor = Color(0xffB58543);
// const dividerColor = Color(0xffF2D9E6);
const dividerColor = Color(0xffede2de);

// const sudarshanColor = Color(0xffB58543);
// const lightGreenColor = Color(0xffe2f7c7);
// const greenColor = Color.fromRGBO(171, 232, 94, 1);
// const greenColor2 = Color.fromARGB(255, 115, 164, 55);
const themeColor = Color(0xffd88f77);
const themeColor2 = Color(0xffd88f77);

// const themeColor = Color.fromARGB(255, 115, 164, 55);

final themeData = ThemeData(
    colorSchemeSeed: themeColor,
    useMaterial3: true,
    scaffoldBackgroundColor: Colors.white,
    // scaffoldBackgroundColor: const Color.fromARGB(255, 255, 250, 250),

    bottomSheetTheme:
        const BottomSheetThemeData(surfaceTintColor: Colors.white),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.white,
    ));
