import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:wedding_super_admin/models/questionaire_model.dart';
import 'package:wedding_super_admin/shared/theme.dart';

import '../services/image_picker.dart';
import 'firebase.dart';
import 'package:intl/intl.dart';

bool isLoggedIn() => FBAuth.auth.currentUser != null;
const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomMix(int length) {
  const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  final random = Random();
  return List.generate(
      length, (index) => characters[random.nextInt(characters.length)]).join();
}

String getRandomId(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

String generateInquiryId() {
  final Random random = Random();

  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const digits = '0123456789';

  String randomLetters =
      List.generate(4, (_) => letters[random.nextInt(letters.length)]).join();
  String randomDigits =
      List.generate(4, (_) => digits[random.nextInt(digits.length)]).join();

  return randomLetters + randomDigits;
}

extension MetaWid on DateTime {
  String goodDate() {
    try {
      return DateFormat.yMMMM().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodDayDate() {
    try {
      return DateFormat.yMMMd().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String convertToDDMMYY() {
    DateFormat formatter = DateFormat('dd-MM-yyyy');
    return formatter.format(this);
  }

  String goodTime() {
    try {
      return DateFormat('hh:mm a').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }
}

List<String> generateCombinations(String text) {
  final combinations = <String>{};
  final splitWords = <String>{};

  // Split the text by space and trim each word
  final words =
      text.toLowerCase().split(' ').map((word) => word.trim()).toList();

  // Add original split words and their first letter/two letters to the output set
  for (final word in words) {
    splitWords.add(word);
    splitWords.add(word[0]); // First letter
    if (word.length > 1) {
      splitWords.add(word.substring(0, 2)); // First two letters
    }
  }

  for (final word in words) {
    final minLength = word.length ~/ 2 + (word.length % 2 == 0 ? 0 : 1);
    for (int i = 0; i < word.length; i++) {
      for (int j = i; j < word.length; j++) {
        final comboLength = j - i + 1;
        if (comboLength > 2 && comboLength >= minLength) {
          combinations.add(word.substring(i, j + 1));

          // Generate combinations with one typo (swapping or missing character)
          if (comboLength > minLength) {
            final swapped = swapChars(word.substring(i, j + 1));
            final missing = removeChar(word.substring(i, j + 1));
            combinations.addAll([swapped, missing]);
          }
        }
      }
    }
  }
  return splitWords.union(combinations).toList();
}

String swapChars(String str) {
  if (str.length < 2) return str;
  final chars = str.split('');
  final temp = chars[0];
  chars[0] = chars[1];
  chars[1] = temp;
  return chars.join('');
}

String removeChar(String str) {
  if (str.length < 2) return str;
  final chars = str.split('');
  chars.removeAt(1);
  return chars.join('');
}

Color hexToColor(String hex) {
  hex = hex.replaceAll('#', '');
  if (hex.length == 6) {
    hex = 'FF$hex'; // Add full opacity
  }
  return Color(int.parse(hex, radix: 16));
}

InputDecoration inpDecor() {
  return InputDecoration(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(7),
      borderSide: BorderSide(color: Colors.grey.shade400),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(7),
      borderSide: BorderSide(color: Colors.grey.shade400),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(7),
      borderSide: BorderSide(color: Colors.grey.shade400),
    ),
    disabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(7),
      borderSide: BorderSide(color: Colors.grey.shade400),
    ),
    filled: true,
    fillColor: Colors.transparent,
    // fillColor: lightSkinColor,
    // fillColor: Colors.white,
  );
}

Future<String?> uploadCategoryFile(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.offering.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadProductImages(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.products.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadGalleryFiles(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.subcategory.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadSessionFile(
    SelectedImage imageFile, String sessionId) async {
  try {
    final imageRef = FBStorage.sessions.child(sessionId).child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<List<String>?> uploadSessionImages(
    List<SelectedImage> selctedImages, String sessionId) async {
  try {
    List<String> uploadedImages = [];
    for (var item in selctedImages) {
      final imageRef = FBStorage.sessions
          .child(sessionId)
          .child('${DateTime.now().millisecondsSinceEpoch}.${item.extention}');
      final task = await imageRef.putData(item.uInt8List);
      uploadedImages.add(await task.ref.getDownloadURL());
    }
    return uploadedImages;
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadSubCategoryFile(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.subcategory.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadMiscFile(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.misc.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadProductFile(SelectedImage imageFile, String docId) async {
  try {
    final imageRef = FBStorage.products.child(docId).child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<List<String>> uploadBlogFile(List<SelectedImage> imageFiles) async {
  try {
    List<String> uploadedImagesLink = [];
    for (var image in imageFiles) {
      final imageRef = FBStorage.blogs
          .child('${DateTime.now().millisecondsSinceEpoch}.${image.extention}');
      final task = await imageRef.putData(image.uInt8List);
      uploadedImagesLink.add(await task.ref.getDownloadURL());
    }
    return uploadedImagesLink;
  } on Exception catch (e) {
    debugPrint(e.toString());
    return [];
  }
}

capilatlizeFirstLetter(String text) {
  final splitList = text.trim().split(" ");
  List<String> capilatlizedString = [];
  for (var element in splitList) {
    capilatlizedString.addIf(
        element.capitalizeFirst != null, element.capitalize!);
  }
  return capilatlizedString.join(" ");
}

showAppSnackBar(BuildContext context, String message) {
  final snackBar = SnackBar(
    content: Text(message),
    duration: const Duration(seconds: 2),
  );
  ScaffoldMessenger.of(context).showSnackBar(snackBar);
}

showErrorAppSnackBar(BuildContext context, String message) {
  // ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
  //     content: ));

  final snackBar = SnackBar(
    content: Row(
      children: [
        const Icon(CupertinoIcons.exclamationmark_octagon,
            color: Colors.red, size: 25),
        const SizedBox(width: 7),
        Text(message),
      ],
    ),
    duration: const Duration(seconds: 2),
  );
  ScaffoldMessenger.of(context).showSnackBar(snackBar);
}

String? getQuestionType(String type) {
  switch (type) {
    case QuestionTypes.multiple:
      return QuestionTypes.multiple;
    case QuestionTypes.textfield:
      return QuestionTypes.textfield;
    case QuestionTypes.single:
      return QuestionTypes.single;
    default:
      return null;
  }
}

bool isValidEmail(String email) {
  final RegExp emailRegex =
      RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
  return emailRegex.hasMatch(email);
}

bool isValidIndianPhoneNumber(String phone) {
  final RegExp phoneRegex = RegExp(
      r"^(\+91[\s]?)?[6-9][0-9]{9}$"); // Allows optional +91 and ensures 10-digit format
  return phoneRegex.hasMatch(phone);
}

String sessionDateFormat(DateTime dateTime) {
  String day = DateFormat('EEEE').format(dateTime); // Full weekday name
  String month = DateFormat('MMMM').format(dateTime); // Full month name
  String dayNumber = DateFormat('d').format(dateTime); // Day of the month

  // Add suffix to the day
  String suffix = getDaySuffix(int.parse(dayNumber));

  return "$day, $month $dayNumber$suffix";
}

String formatToMMYY(DateTime date) {
  final month = date.month.toString().padLeft(2, '0');
  final year = date.year.toString().substring(2); // Get last 2 digits
  return '$month$year';
}

bool isTodayOrBefore(DateTime date) {
  final today = DateTime.now();
  final inputDate = DateTime(date.year, date.month, date.day);
  final currentDate = DateTime(today.year, today.month, today.day);
  return inputDate.isBefore(currentDate) ||
      inputDate.isAtSameMomentAs(currentDate);
}

String getDaySuffix(int day) {
  if (day >= 11 && day <= 13) {
    return 'th';
  }
  switch (day % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
}

// PdfColor dartColorToPdfRGB(Color color) {
//   return PdfColor(
//     color.red / 255.0,
//     color.green / 255.0,
//     color.blue / 255.0,
//   );
// }

DateTime getMonthlyStartDate(DateTime endDate) {
  int year = endDate.month == 1 ? endDate.year - 1 : endDate.year;
  int month = endDate.month == 1 ? 12 : endDate.month - 1;
  int day = endDate.day;

  // Get the last day of the previous month
  int lastDayOfPrevMonth = DateTime(year, month + 1, 0).day;

  // Clamp the day in case previous month has fewer days
  if (day > lastDayOfPrevMonth) {
    day = lastDayOfPrevMonth;
  }

  return DateTime(year, month, day);
}

List<Map<String, String>> getNewVariantCombinations({
  required List<Map<String, dynamic>> existingCombinations,
  required Map<String, String> userInput,
}) {
  // Normalize user input into trimmed, lowercased lists
  final inputLists = {
    for (var key in userInput.keys)
      key: userInput[key]!
          .split(RegExp(r'\s*,\s*'))
          .map((e) => e.trim().toLowerCase())
          // .where((e) => e.isNotEmpty)
          .toList()
  };

  // Generate all combinations
  List<Map<String, String>> generateAll(Map<String, List<String>> input) {
    List<Map<String, String>> results = [];

    void build(Map<String, String> current, List<String> keys) {
      if (keys.isEmpty) {
        results.add(Map.from(current));
        return;
      }

      final key = keys.first;
      for (var val in input[key]!) {
        current[key] = val;
        build(current, keys.sublist(1));
      }
    }

    build({}, input.keys.toList());
    return results;
  }

  // Normalize existing combinations into lowercase trimmed string maps
  final normalizedExisting = existingCombinations.map((combo) {
    return combo.map((k, v) => MapEntry(
        k.toString().toLowerCase(), v.toString().trim().toLowerCase()));
  }).toList();

  // Check for duplicates
  bool isDuplicate(Map<String, String> combo) {
    return normalizedExisting.any((existing) =>
        existing.length == combo.length &&
        existing.keys.every((key) => existing[key] == combo[key]));
  }

  // Generate and filter
  return generateAll(inputLists).where((c) => !isDuplicate(c)).toList();
}

Future<Uint8List> getImageBytesFromUrl(String imageUrl) async {
  final response = await http.get(Uri.parse(imageUrl));
  if (response.statusCode == 200) {
    return response.bodyBytes;
  } else {
    throw Exception('Failed to load image');
  }
}

double getWidth(double height) {
  double width = 0;
  width = (4 / 5) * height;
  return width;
}

/* List<Map<String, String>> getNewVariantCombinations({
  required List<Map<String, dynamic>> existingCombinations,
  required Map<String, String> userInput,
}) {
  print('start');
  // Step 1: Parse input with support for all comma formats
  final inputLists = {
    for (var key in userInput.keys)
      key: userInput[key]!
          .split(RegExp(r'\s*,\s*')) // handles ",", " ,", ", ", etc.
          .where((e) => e.isNotEmpty)
          .toList()
  };
  print('step1 cmpl');

  // Step 2: Generate all combinations
  List<Map<String, String>> generateAll(Map<String, List<String>> input) {
    List<Map<String, String>> results = [];

    void build(Map<String, String> current, List<String> keys) {
      if (keys.isEmpty) {
        results.add(Map.from(current));
        return;
      }

      final key = keys.first;
      for (var val in input[key]!) {
        current[key] = val;
        build(current, keys.sublist(1));
      }
    }

    build({}, input.keys.toList());
    return results;
  }

  print('step2 cmpl');

  // Step 3: Handle empty case
  if (existingCombinations.isEmpty) {
    return generateAll(inputLists);
  }
  print('step3 cmpl');

  // Step 4: Check if combination exists
  bool isDuplicate(Map<String, String> combo) {
    return existingCombinations.any((existingDynamic) {
      final existing = existingDynamic
          .map((key, value) => MapEntry(key, value?.toString() ?? ''));

      return existing.length == combo.length &&
          combo.keys.every((key) => existing[key] == combo[key]);
    });
  }

  print('step4 cmpl');

  // Step 5: Filter only new ones
  return generateAll(inputLists).where((c) => !isDuplicate(c)).toList();
}
 */
Color getBudgetBarColor(double value) {
  if (value > 1) return Colors.red;
  // if (value >= 0.7) return Colors.orange;
  return themeColor;
}
