import 'package:cloud_firestore/cloud_firestore.dart';

class GalleryModel {
  final String docId;
  final String image;
  final int priorityNo;

  GalleryModel({
    required this.docId,
    required this.image,
    required this.priorityNo,
  });

  // Converts a TestimonialsModel instance to a JSON map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'image': image,
      'priorityNo': priorityNo,
    };
  }

  // Creates a TestimonialsModel instance from a JSON map (useful when fetching data)
  factory GalleryModel.fromJson(Map<String, dynamic> json) {
    return GalleryModel(
      docId: json['docId'],
      image: json['image'],
      priorityNo: json['priorityNo'],
    );
  }

  // Creates a TestimonialsModel instance from a Firestore document snapshot
  factory GalleryModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> data) {
    return GalleryModel(
      docId: data.id,
      image: data['image'],
      priorityNo: data['priorityNo'],
    );
  }
}
