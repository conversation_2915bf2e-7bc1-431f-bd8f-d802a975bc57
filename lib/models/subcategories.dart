import 'package:cloud_firestore/cloud_firestore.dart';

class SubCategory {
  final String docId;
  final String name;
  final String image;
  final bool isActive;
  final List<String> combinationNames;
  final String offeringId;
  final int? minPrice;
  final int? maxPrice;
  final List<String> tags;
  final List<Map<String, dynamic>> detailTypes;
  final num marginPercentage;
  final Map<String, List<String>> allData;
  // final List<String> colors;
  // final List<String> sizes;
  // final List<String> materials;

  SubCategory({
    this.minPrice,
    this.maxPrice,
    required this.tags,
    required this.detailTypes,
    required this.docId,
    required this.name,
    required this.image,
    required this.isActive,
    required this.combinationNames,
    required this.offeringId,
    required this.marginPercentage,
    required this.allData,
    // required this.colors,
    // required this.sizes,
    // required this.materials,
  });

  // Converts a SubCategories instance to a JSON map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'image': image,
      'isActive': isActive,
      'combinationNames': combinationNames,
      'offeringId': offeringId,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'tags': tags,
      'detailTypes': detailTypes,
      'marginPercentage': marginPercentage,
      'allData': allData,
      // 'colors': colors,
      // 'sizes': sizes,
      // 'materials': materials,
    };
  }

  // Creates a SubCategories instance from a JSON map (useful when fetching data)
  factory SubCategory.fromJson(Map<String, dynamic> json) {
    return SubCategory(
      docId: json['docId'] ?? "",
      name: json['name'],
      image: json['image'],
      isActive: json['isActive'],
      combinationNames: List<String>.from(json['combinationNames']),
      offeringId: json['offeringId'],
      minPrice: json['minPrice'],
      maxPrice: json['maxPrice'],
      marginPercentage: json['marginPercentage'],
      tags: List<String>.from(json['tags']),
      detailTypes: List<Map<String, dynamic>>.from(json['detailTypes']),
      allData: json['allData'] != null
          ? (json['allData'] as Map<String, dynamic>).map(
              (key, value) => MapEntry(key, List<String>.from(value ?? [])),
            )
          : {},
      // colors: json.containsKey('colors') ? json['colors'] : <String>[],
      // sizes: json.containsKey('sizes') ? json['sizes'] : <String>[],
      // materials: json.containsKey('materials') ? json['materials'] : <String>[],
    );
  }

  // Creates a SubCategories instance from a Firestore document snapshot
  factory SubCategory.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return SubCategory(
      docId: json.id,
      name: json['name'],
      image: json['image'],
      isActive: json['isActive'],
      combinationNames: List<String>.from(json['combinationNames']),
      offeringId: json['offeringId'],
      marginPercentage: json.data().containsKey('marginPercentage')
          ? json['marginPercentage']
          : 0,

      minPrice: json.data().containsKey('minPrice') ? json['minPrice'] : 0,
      maxPrice: json.data().containsKey('maxPrice') ? json['maxPrice'] : 0,
      tags: json.data().containsKey('tags')
          ? List<String>.from(json['tags'])
          : [],
      detailTypes: json.data().containsKey('detailTypes')
          ? List<Map<String, dynamic>>.from(json['detailTypes'])
          : [],
      allData: json.data().containsKey('allData')
          ? (json['allData'] as Map<String, dynamic>).map(
              (key, value) => MapEntry(key, List<String>.from(value ?? [])),
            )
          : {},
      // colors: json.data().containsKey('colors')
      //     ? List<String>.from(json['colors'])
      //     : <String>[],
      // sizes: json.data().containsKey("sizes")
      //     ? List<String>.from(json['sizes'])
      //     : <String>[],
      // materials: json.data().containsKey("materials")
      //     ? List<String>.from(json['materials'])
      //     : <String>[],
    );
  }
}
