import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:wedding_super_admin/models/order_model.dart';

class VendorOrderModel {
  final String docId;
  final String orderId;
  final String orderDocId;
  final String vendorId;
  final List<OrderProductData> orderProducts;
  final DateTime createdAt;
  final DateTime statusUpdatedAt;
  final String createdBy;
  final String status;

  VendorOrderModel({
    required this.docId,
    required this.orderId,
    required this.orderDocId,
    required this.vendorId,
    required this.orderProducts,
    required this.createdAt,
    required this.statusUpdatedAt,
    required this.createdBy,
    required this.status,
  });

  // fromJson method
  factory VendorOrderModel.fromJson(Map<String, dynamic> json) {
    return VendorOrderModel(
      docId: json['docId'],
      orderId: json['orderId'],
      orderDocId: json.containsKey('orderDocId') ? json['orderDocId'] : '',
      vendorId: json['vendorId'],
      orderProducts: (json['orderProducts'] as List)
          .map((product) => OrderProductData.fromJson(product))
          .toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      statusUpdatedAt:
          DateTime.fromMillisecondsSinceEpoch(json['statusUpdatedAt']),
      createdBy: json['createdBy'],
      status: json['status'],
    );
  }

  // toJson method
  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'vendorId': vendorId,
      'orderDocId': orderDocId,
      'orderProducts':
          orderProducts.map((product) => product.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'statusUpdatedAt': statusUpdatedAt.toIso8601String(),
      'createdBy': createdBy,
      'status': status,
    };
  }

  // fromSnap method (for Firestore document snapshot)
  factory VendorOrderModel.fromSnap(QueryDocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;

    return VendorOrderModel(
      docId: snap.id,
      orderId: data['orderId'],
      orderDocId: data.containsKey('orderDocId') ? data['orderDocId'] : '',
      vendorId: data['vendorId'],
      orderProducts: (data['orderProducts'] as List)
          .map((product) => OrderProductData.fromJson(product))
          .toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
      statusUpdatedAt:
          DateTime.fromMillisecondsSinceEpoch(data['statusUpdatedAt']),
      createdBy: data['createdBy'],
      status: data['status'],
    );
  }

  // fromDocSnap method (to handle a document snapshot with custom processing)
  factory VendorOrderModel.fromDocSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return VendorOrderModel(
      docId: snap.id,
      orderDocId: data.containsKey('orderDocId') ? data['orderDocId'] : '',
      orderId: data['orderId'],
      vendorId: data['vendorId'],
      orderProducts: (data['orderProducts'] as List)
          .map((product) => OrderProductData.fromJson(product))
          .toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
      statusUpdatedAt:
          DateTime.fromMillisecondsSinceEpoch(data['statusUpdatedAt']),
      createdBy: data['createdBy'],
      status: data['status'],
    );
  }
}
