import 'package:cloud_firestore/cloud_firestore.dart';

class BlogModel {
  final String docId;
  final bool isVisible;
  final bool show;
  final String metaTitle;
  final String metaDesc;
  final String lowerTitle;
  final String markDown;
  final String htmlString;
  final List<String> tags;
  final String permalink;
  final Timestamp publishedOn;
  final Timestamp createdAt;
  final Timestamp updatedAt;
  final List<String> imageLinks;
  final List<String> otherBlogs;
  final String thumbnail;

  // bool? isVisible;
  // String? metaTitle;
  // String? lowerTitle;
  // String? description;
  // String? markdown;
  // String? htmlString;
  // List<String>? tags;
  // String? permalink;
  // List<String>? imagesLinks;
  // String? thumbnail;
  // List<BlogModel>? otherBlogs;
  // String? createdAt;
  // String? updatedAt;

  BlogModel({
    required this.docId,
    required this.createdAt,
    required this.updatedAt,
    required this.metaDesc,
    required this.show,
    required this.htmlString,
    required this.publishedOn,
    required this.imageLinks,
    required this.isVisible,
    required this.lowerTitle,
    required this.markDown,
    required this.metaTitle,
    required this.otherBlogs,
    required this.permalink,
    required this.tags,
    required this.thumbnail,
  });

  // Converts a BlogsModel instance to a JSON map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'htmlString': htmlString,
      'imageLinks': imageLinks,
      'isVisible': isVisible,
      'lowerTitle': lowerTitle,
      'publishedOn': publishedOn,
      'markDown': markDown,
      'metaTitle': metaTitle,
      'otherBlogs': otherBlogs,
      'permalink': permalink,
      'tags': tags,
      'thumbnail': thumbnail,
      'metaDesc': metaDesc,
      'show': show,
    };
  }

  // Creates a BlogsModel instance from a JSON map (useful when fetching data)
  factory BlogModel.fromJson(Map<String, dynamic> json) {
    return BlogModel(
      docId: json['docId'],
      createdAt: json['createdAt'] as Timestamp,
      updatedAt: json['updatedAt'] as Timestamp,
      htmlString: json['htmlString'],
      show: json['show'],
      imageLinks: List<String>.from(json['imageLinks']),
      isVisible: json['isVisible'],
      lowerTitle: json['lowerTitle'],
      markDown: json['markDown'],
      metaTitle: json['metaTitle'],
      metaDesc: json.containsKey('metaDesc') ? json['metaDesc'] : "",
      publishedOn: json.containsKey('publishedOn')
          ? (json['publishedOn'] as Timestamp)
          : (json['createdAt'] as Timestamp),
      otherBlogs: List<String>.from(json['otherBlogs']),
      permalink: json['permalink'],
      tags: List<String>.from(json['tags']),
      thumbnail: json['thumbnail'],
    );
  }

  // Creates a BlogsModel instance from a Firestore document snapshot
  factory BlogModel.fromSnap(QueryDocumentSnapshot<Map<String, dynamic>> data) {
    return BlogModel(
      docId: data.id,
      createdAt: data['createdAt'] as Timestamp,
      updatedAt: data['updatedAt'] as Timestamp,
      htmlString: data['htmlString'],
      show: data['show'],
      metaDesc: data.data().containsKey('metaDesc') ? data['metaDesc'] : "",
      publishedOn: data.data().containsKey('publishedOn')
          ? (data['publishedOn'] as Timestamp)
          : (data['createdAt'] as Timestamp),
      imageLinks: List<String>.from(data['imageLinks']),
      isVisible: data['isVisible'],
      lowerTitle: data['lowerTitle'],
      markDown: data['markDown'],
      metaTitle: data['metaTitle'],
      otherBlogs: List<String>.from(data['otherBlogs']),
      permalink: data['permalink'],
      tags: List<String>.from(data['tags']),
      thumbnail: data['thumbnail'],
    );
  }
}

// import 'dart:convert';

// class BlogModel {
//   String? id;
//   bool? isVisible;
//   String? metaTitle;
//   String? lowerTitle;
//   String? description;
//   String? markdown;
//   String? htmlString;
//   List<String>? tags;
//   String? permalink;
//   List<String>? imagesLinks;
//   String? thumbnail;
//   List<BlogModel>? otherBlogs;
//   String? createdAt;
//   String? updatedAt;

//   BlogModel({
//     this.id,
//     this.isVisible,
//     this.metaTitle,
//     this.lowerTitle,
//     this.description,
//     this.markdown,
//     this.htmlString,
//     this.tags,
//     this.permalink,
//     this.imagesLinks,
//     this.thumbnail,
//     this.otherBlogs,
//     this.createdAt,
//     this.updatedAt,
//   });

//   Map<String, dynamic> toMap() {
//     final result = <String, dynamic>{};

//     if (id != null) {
//       result.addAll({'id': id});
//     }
//     if (isVisible != null) {
//       result.addAll({'isVisible': isVisible});
//     }
//     if (metaTitle != null) {
//       result.addAll({'metaTitle': metaTitle});
//     }
//     if (lowerTitle != null) {
//       result.addAll({'lowerTitle': lowerTitle});
//     }
//     if (description != null) {
//       result.addAll({'description': description});
//     }
//     if (markdown != null) {
//       result.addAll({'markdown': markdown});
//     }
//     if (htmlString != null) {
//       result.addAll({'htmlString': htmlString});
//     }
//     if (tags != null) {
//       result.addAll({'tags': tags});
//     }
//     if (permalink != null) {
//       result.addAll({'permalink': permalink});
//     }
//     if (imagesLinks != null) {
//       result.addAll({'imagesLinks': imagesLinks});
//     }
//     if (thumbnail != null) {
//       result.addAll({'thumbnail': thumbnail});
//     }
//     if (otherBlogs != null) {
//       result.addAll({'otherBlogs': otherBlogs!.map((x) => x.toMap()).toList()});
//     }
//     if (createdAt != null) {
//       result.addAll({'createdAt': createdAt});
//     }
//     if (updatedAt != null) {
//       result.addAll({'updatedAt': updatedAt});
//     }

//     return result;
//   }

//   factory BlogModel.fromMap(Map<String, dynamic> map) {
//     return BlogModel(
//       id: map['id'],
//       isVisible: map['isVisible'] ?? true,
//       metaTitle: map['metaTitle'],
//       lowerTitle: map['lowerTitle'],
//       description: map['description'],
//       markdown: map['markdown'],
//       htmlString: map['htmlString'],
//       tags: map['tags'] != null ? List<String>.from(map['tags']) : [],
//       permalink: map['permalink'],
//       imagesLinks: map['imagesLinks'] != null
//           ? List<String>.from(map['imagesLinks'])
//           : null,
//       thumbnail: map['thumbnail'],
//       otherBlogs: map['otherBlogs'] != null
//           ? List<BlogModel>.from(
//               map['otherBlogs']?.map((x) => BlogModel.fromMap(x)))
//           : null,
//       createdAt: map['createdAt'],
//       updatedAt: map['updatedAt'],
//     );
//   }

//   String toJson() => json.encode(toMap());

//   factory BlogModel.fromJson(String source) =>
//       BlogModel.fromMap(json.decode(source));
// }

// class BlogImage {
//   String? imageUrl;
//   String? altText;
//   BlogImage({
//     this.imageUrl,
//     this.altText,
//   });

//   Map<String, dynamic> toMap() {
//     final result = <String, dynamic>{};

//     if (imageUrl != null) {
//       result.addAll({'imageUrl': imageUrl});
//     }
//     if (altText != null) {
//       result.addAll({'altText': altText});
//     }

//     return result;
//   }

//   factory BlogImage.fromMap(Map<String, dynamic> map) {
//     return BlogImage(
//       imageUrl: map['imageUrl'],
//       altText: map['altText'],
//     );
//   }

//   String toJson() => json.encode(toMap());

//   factory BlogImage.fromJson(String source) =>
//       BlogImage.fromMap(json.decode(source));
// }
