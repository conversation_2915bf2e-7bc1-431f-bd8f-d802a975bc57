import 'package:cloud_firestore/cloud_firestore.dart';

class VendorModel {
  final String docId;
  final String name;
  final String email;
  final String contactPerson;
  final String phone;
  final Timestamp createdAt;
  final Timestamp lastUpdatedOn;
  final String address;
  final List<String> subcatids;
  final List<SubCatMap> apiSubCatIds;
  final String city;
  final String state;
  final String password;
  final String level;
  final String serviceDescription;
  final String? websiteLink;
  final bool isActive;
  final Map<String, dynamic> apiRequiredData;

  VendorModel({
    required this.docId,
    required this.name,
    required this.email,
    required this.contactPerson,
    required this.phone,
    required this.createdAt,
    required this.address,
    required this.subcatids,
    required this.apiSubCatIds,
    required this.level,
    required this.lastUpdatedOn,
    required this.city,
    required this.state,
    required this.password,
    required this.websiteLink,
    required this.serviceDescription,
    required this.isActive,
    required this.apiRequiredData,
  });

  // Converts a VendorModel instance to a JSON map for Firestore
  Map<String, dynamic> to<PERSON>son() {
    return {
      'docId': docId,
      'name': name,
      'email': email,
      'contactPerson': contact<PERSON>erson,
      'phone': phone,
      'createdAt': createdAt,
      'level': level,
      'address': address,
      'lastUpdatedOn': lastUpdatedOn,
      'subcatids': subcatids,
      'apiSubCatIds': apiSubCatIds.map((e) => e.toJson()).toList(),
      'websiteLink': websiteLink,
      'city': city,
      'state': state,
      'isActive': isActive,
      'serviceDescription': serviceDescription,
      'apiRequiredData': apiRequiredData,
    };
  }

  // Creates a VendorModel instance from a JSON map (useful when fetching data)
  factory VendorModel.fromJson(Map<String, dynamic> json) {
    return VendorModel(
      docId: json['docId'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      contactPerson: json['contactPerson'],
      createdAt: json['createdAt'] as Timestamp,
      lastUpdatedOn: json['lastUpdatedOn'] as Timestamp,
      address: json['address'],
      level: json['level'],
      websiteLink: json['websiteLink'],
      city: json['city'],
      state: json['state'],
      password: json['password'],
      serviceDescription: json['serviceDescription'],
      isActive: json['isActive'],
      apiRequiredData:
          json.containsKey('apiRequiredData') ? json['apiRequiredData'] : {},
      subcatids: List<String>.from(json['subcatids']),
      apiSubCatIds: (json.containsKey('apiSubCatIds'))
          ? (json['apiSubCatIds'] as List<Map<String, dynamic>>)
              .map((e) => SubCatMap(
                  subCatApiId: e['subCatApiId'], subCatdocId: e['subCatdocId']))
              .toList()
          : [],
    );
  }

  // Creates a VendorModel instance from a Firestore document snapshot
  factory VendorModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> data) {
    return VendorModel(
      docId: data.id,
      name: data['name'],
      email: data['email'],
      phone: data['phone'],
      contactPerson:
          data.data().containsKey('contactPerson') ? data['contactPerson'] : '',
      createdAt: data['createdAt'] as Timestamp,
      lastUpdatedOn: data['lastUpdatedOn'] as Timestamp,
      address: data['address'],
      level: data['level'],
      websiteLink: data['websiteLink'],
      city: data['city'],
      isActive: data['isActive'],
      serviceDescription: data['serviceDescription'],
      state: data['state'],
      password: data['password'],
      apiRequiredData: data.data().containsKey('apiRequiredData')
          ? data['apiRequiredData']
          : {},
      subcatids: List<String>.from(data['subcatids']),
      apiSubCatIds: (data.data().containsKey('apiSubCatIds'))
          ? (data['apiSubCatIds'] as List)
              .map((e) => SubCatMap(
                  subCatApiId: e['subCatApiId'], subCatdocId: e['subCatdocId']))
              .toList()
          : [],
    );
  }
  factory VendorModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> data) {
    return VendorModel(
      docId: data.id,
      name: data['name'],
      email: data['email'],
      serviceDescription: data['serviceDescription'],
      phone: data['phone'],
      contactPerson: data.data()?.containsKey('contactPerson') ?? false
          ? data['contactPerson']
          : '',
      level: data['level'],
      websiteLink: data['websiteLink'],
      createdAt: data['createdAt'] as Timestamp,
      lastUpdatedOn: data['lastUpdatedOn'] as Timestamp,
      address: data['address'],
      city: data['city'],
      apiRequiredData: (data.data()?.containsKey('apiRequiredData') ?? false)
          ? data['apiRequiredData']
          : {},
      state: data['state'],
      isActive: data['isActive'],
      password: data['password'],
      subcatids: List<String>.from(data['subcatids']),
      apiSubCatIds: (data.data()?.containsKey('apiSubCatIds') ?? false)
          ? (data['apiSubCatIds'] as List)
              .map((e) => SubCatMap(
                  subCatApiId: e['subCatApiId'], subCatdocId: e['subCatdocId']))
              .toList()
          : [],
    );
  }
}

class SubCatMap {
  String subCatdocId;
  String subCatApiId;

  SubCatMap({
    required this.subCatApiId,
    required this.subCatdocId,
  });

  factory SubCatMap.fromJson(Map<String, dynamic> json) {
    return SubCatMap(
      subCatApiId: json['subCatApiId'] as String,
      subCatdocId: json['subCatdocId'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subCatApiId': subCatApiId,
      'subCatdocId': subCatdocId,
    };
  }
}
