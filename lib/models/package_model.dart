import 'package:cloud_firestore/cloud_firestore.dart';

class PackageModel {
  final String docId;
  final String packageName;
  final num price;
  final String description;
  final int validity;
  final DateTime createdAt;
  final String packageColor;
  final DateTime updatedAt;
  final List<PointsModel> points;
  // final bool isStandard;
  final bool isActive;

  PackageModel({
    required this.validity,
    required this.docId,
    required this.packageName,
    required this.price,
    required this.description,
    required this.createdAt,
    required this.packageColor,
    required this.updatedAt,
    required this.points,
    // required this.isStandard,
    required this.isActive,
  });

  // Convert JSON to PackageModel
  factory PackageModel.fromJson(Map<String, dynamic> json) {
    return PackageModel(
      docId: json['docId'] ?? '',
      packageName: json['packageName'] ?? '',
      price: json['price'] ?? 0,
      description: json['description'] ?? '',
      packageColor: json['packageColor'] ?? '',
      validity: json['validity'] ?? 0,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt'] ?? 0),
      points: (json['points'] as List<dynamic>)
          .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      // isStandard: json['isStandard'] ?? false,
      isActive: json['isActive'] ?? false,
    );
  }

  // Convert PackageModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'packageName': packageName,
      'price': price,
      'packageColor': packageColor,
      'validity': validity,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'points': points.map((e) => e.toJson()).toList(),
      // 'isStandard': isStandard,
      'isActive': isActive,
    };
  }

  // Create PackageModel from Firestore snapshot
  factory PackageModel.fromSnap(DocumentSnapshot<Map<String, dynamic>> snap) {
    var data = snap.data()!;
    return PackageModel(
      docId: snap.id,
      packageName: data['packageName'] ?? '',
      price: data['price'] ?? 0,
      validity: data['validity'] ?? 0,
      description: data['description'] ?? '',
      packageColor: data['packageColor'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(data['updatedAt'] ?? 0),
      points: (data['points'] as List<dynamic>? ?? [])
          .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      // isStandard: data['isStandard'] ?? false,
      isActive: data['isActive'] ?? false,
    );
  }

  // Create PackageModel from Firestore DocumentSnapshot
  factory PackageModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> docSnap) {
    var data = docSnap.data()!;
    return PackageModel(
      docId: docSnap.id,
      packageName: data['packageName'] ?? '',
      price: data['price'] ?? 0,
      validity: data['validity'] ?? 0,
      description: data['description'] ?? '',
      packageColor: data['packageColor'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(data['updatedAt'] ?? 0),
      points: (data['points'] as List<dynamic>)
          .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      // isStandard: data['isStandard'] ?? false,
      isActive: data['isActive'] ?? false,
    );
  }
}

// PointsModel for handling individual services
class PointsModel {
  String? serviceId;
  String serviceName;
  String? serviceUnit;
  num serviceQty;

  PointsModel({
    required this.serviceId,
    required this.serviceName,
    required this.serviceQty,
    required this.serviceUnit,
  });

  // Convert JSON to PointsModel
  factory PointsModel.fromJson(Map<String, dynamic> json) {
    return PointsModel(
      serviceId: json['serviceId'] ?? '',
      serviceName: json['serviceName'] ?? '',
      serviceQty: json['serviceQty'] ?? 0,
      serviceUnit: json['serviceUnit'] ?? '',
    );
  }

  // Convert PointsModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'serviceId': serviceId,
      'serviceName': serviceName,
      'serviceQty': serviceQty,
      'serviceUnit': serviceUnit,
    };
  }
}

class PackageServiceModel {
  final String? id;
  final String name;
  final String unit;
  final num price;
  final num qty;

  PackageServiceModel({
    required this.id,
    required this.name,
    required this.unit,
    required this.price,
    required this.qty,
  });

  factory PackageServiceModel.fromJson(String id, Map<String, dynamic> json) {
    return PackageServiceModel(
      id: id as String?,
      name: json['name'] as String,
      unit: json['unit'] as String,
      price: json['price'],
      qty: json['qty'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'unit': unit,
      'price': price,
      'qty': qty,
    };
  }
}
