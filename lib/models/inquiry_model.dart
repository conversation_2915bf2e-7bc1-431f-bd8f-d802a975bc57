// import 'package:cloud_firestore/cloud_firestore.dart';

// class InquiryModel {
//   final String docID;
//   final String uId;
//   final String name;
//   final String email;
//   final String comments;
//   final bool isNew;
//   final DateTime createdAt;
//   final List<String> variantIds;
//   final List<String> sessionIds;

//   InquiryModel({
//     required this.comments,
//     required this.createdAt,
//     required this.docID,
//     required this.email,
//     required this.isNew,
//     required this.name,
//     required this.sessionIds,
//     required this.variantIds,
//     required this.uId,
//   });

//   factory InquiryModel.fromJson(Map<String, dynamic> json) {
//     return InquiryModel(
//       docID: json['docID'],
//       uId: json['uId'],
//       name: json['name'],
//       email: json['email'],
//       comments: json['comments'],
//       isNew: json['isNew'],
//       createdAt: (json['createdAt'] as Timestamp).toDate(),
//       variantIds: List<String>.from(json['variantIds'] ?? []),
//       sessionIds: List<String>.from(json['sessionIds'] ?? []),
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'docID': docID,
//       'uId': uId,
//       'name': name,
//       'email': email,
//       'comments': comments,
//       'isNew': isNew,
//       'createdAt': Timestamp.fromDate(createdAt),
//       'variantIds': variantIds,
//       'sessionIds': sessionIds,
//     };
//   }

//   factory InquiryModel.fromSnap(DocumentSnapshot snap) {
//     return InquiryModel.fromJson(snap.data() as Map<String, dynamic>);
//   }
// }
