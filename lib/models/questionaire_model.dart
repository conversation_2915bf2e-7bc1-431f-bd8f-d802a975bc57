import 'package:cloud_firestore/cloud_firestore.dart';

class QuestionnaireModel {
  final String docId;
  final String offeringId;
  final List<OneQuestionModel> questions;

  QuestionnaireModel({
    required this.docId,
    required this.offeringId,
    required this.questions,
  });

  // Factory constructor to parse data from JSON
  factory QuestionnaireModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return QuestionnaireModel(
      docId: json.id,
      offeringId: json['offeringId'],
      questions: (json['questions'] as List<dynamic>)
          .map((question) => OneQuestionModel.fromJson(question))
          .toList(),
    );
  }
  factory QuestionnaireModel.fromJson(Map<String, dynamic> json) {
    return QuestionnaireModel(
      docId: json['docId'],
      offeringId: json['offeringId'],
      questions: (json['questions'] as List<dynamic>)
          .map((question) => OneQuestionModel.fromJson(question))
          .toList(),
    );
  }

  // Convert the model to JSON
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'offeringId': offeringId,
      'questions': questions.map((q) => q.toJson()).toList(),
    };
  }
}

class OneQuestionModel {
  final String id;
  final String question;
  final String type;
  final String? nextQuesId;
  final bool defaultt;
  final List<AnswerModel>? answer;
  final Timestamp createdAt;

  OneQuestionModel({
    required this.id,
    required this.question,
    required this.type,
    this.nextQuesId,
    required this.defaultt,
    this.answer,
    required this.createdAt,
  });

  // Factory constructor to parse data from JSON
  factory OneQuestionModel.fromJson(Map<String, dynamic> json) {
    return OneQuestionModel(
      id: json['id'],
      question: json['question'],
      type: json['type'],
      nextQuesId: json['nextQuesId'],
      defaultt: json['defaultt'],
      createdAt: json['createdAt'] as Timestamp,
      answer: json['answer'] == null
          ? null
          : (json['answer'] as List<dynamic>)
              .map((question) => AnswerModel.fromJson(question))
              .toList(),

      // (json['answer'] as Map<String, dynamic>).map(
      //     (key, value) => MapEntry(key, AnswerModel.fromJson(value)),
      //   ),
    );
  }

  // Convert the model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'type': type,
      'nextQuesId': nextQuesId,
      'createdAt': createdAt,
      'defaultt': defaultt,
      'answer': answer?.map((a) => a.toJson()).toList(),
    };
  }
}

class AnswerModel {
  final String optionString;
  final String? imageUrl;
  final String? nextQuestionId;
  final List<String> tags;

  AnswerModel({
    this.nextQuestionId,
    required this.optionString,
    this.imageUrl,
    required this.tags,
  });

  // Factory constructor to parse data from JSON
  factory AnswerModel.fromJson(Map<String, dynamic> json) {
    return AnswerModel(
      nextQuestionId: json['nextquestionid'],
      optionString: json['optionString'],
      imageUrl: json['imageUrl'],
      tags: List<String>.from(json['tags']),
    );
  }

  // Convert the model to JSON
  Map<String, dynamic> toJson() {
    return {
      'optionString': optionString,
      'imageUrl': imageUrl,
      'nextquestionid': nextQuestionId,
      'tags': tags,
    };
  }
}

class QuestionTypes {
  static const single = 'Single';
  static const multiple = 'Multiple';
  static const textfield = 'Textfield';
}
