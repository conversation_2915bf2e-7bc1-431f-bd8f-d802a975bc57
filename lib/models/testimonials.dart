import 'package:cloud_firestore/cloud_firestore.dart';

class TestimonialsModel {
  final String docId;
  final String title;
  final String videoLink;
  final DateTime createdAt;
  final bool isActive;

  TestimonialsModel({
    required this.docId,
    required this.title,
    required this.videoLink,
    required this.createdAt,
    required this.isActive,
  });

  // Converts a TestimonialsModel instance to a JSON map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'title': title,
      'videoLink': videoLink,
      'isActive': isActive,
      'createdAt': createdAt, // Convert DateTime to Timestamp
    };
  }

  // Creates a TestimonialsModel instance from a JSON map (useful when fetching data)
  factory TestimonialsModel.fromJson(Map<String, dynamic> json) {
    return TestimonialsModel(
      docId: json['docId'],
      title: json['title'],
      isActive: json['isActive'],
      videoLink: json['videoLink'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
    );
  }

  // Creates a TestimonialsModel instance from a Firestore document snapshot
  factory TestimonialsModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> data) {
    return TestimonialsModel(
      docId: data.id,
      title: data['title'],
      isActive: data['isActive'],
      videoLink: data['videoLink'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
    );
  }
}
