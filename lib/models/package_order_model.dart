import 'package:cloud_firestore/cloud_firestore.dart';

import 'package_model.dart';

class AddnData {
  String serviceId;
  String serviceName;
  num qty;
  num additionalPrice;

  AddnData(
      {required this.serviceId,
      required this.serviceName,
      required this.qty,
      required this.additionalPrice});
  factory AddnData.fromJson(Map<String, dynamic> json) {
    return AddnData(
      serviceId: json['serviceId'] ?? '',
      serviceName: json['serviceName'] ?? '',
      qty: json['qty'] ?? 0,
      additionalPrice: json['additionalPrice'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'serviceId': serviceId,
      'serviceName': serviceName,
      'qty': qty,
      'additionalPrice': additionalPrice,
    };
  }
}

class PackageOrderModel {
  final String docId;
  final PackageModel packageModel;
  final List<AddnData> extraServices;
  final num packageAmount;
  final num extraServicesAmount;
  final String uid;
  final String selectedAddressId;
  final String username;
  final DateTime createdAt;
  final bool isPaid;
  final DateTime? paidon;
  final num totalAmount;
  final num paidAmount;

  PackageOrderModel({
    required this.docId,
    required this.packageModel,
    required this.extraServices,
    required this.packageAmount,
    required this.extraServicesAmount,
    required this.uid,
    required this.selectedAddressId,
    required this.username,
    required this.createdAt,
    required this.isPaid,
    required this.paidon,
    required this.totalAmount,
    required this.paidAmount,
  });
  factory PackageOrderModel.fromJson(Map<String, dynamic> json) {
    return PackageOrderModel(
      docId: json['docId'] ?? '',
      packageModel:
          PackageModel.fromJson(json['packageModel'] ?? <String, dynamic>{}),
      extraServices: (json['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      packageAmount: json['packageAmount'] ?? 0,
      extraServicesAmount: json['extraServicesAmount'] ?? 0,
      uid: json['uid'] ?? '',
      selectedAddressId: json['selectedAddressId'] ?? '',
      username: json['username'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      isPaid: json['isPaid'] ?? false,
      paidon: DateTime.fromMillisecondsSinceEpoch(json['paidon'] ?? 0),
      totalAmount: json['totalAmount'] ?? 0,
      paidAmount: json['paidAmount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'packageModel': packageModel.toJson(),
      'extraServices': extraServices.map((e) => e.toJson()).toList(),
      'packageAmount': packageAmount,
      'extraServicesAmount': extraServicesAmount,
      'uid': uid,
      'selectedAddressId': selectedAddressId,
      'username': username,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'isPaid': isPaid,
      'paidon': paidon?.millisecondsSinceEpoch,
      'paidAmount': paidAmount,
      'totalAmount': totalAmount,
    };
  }

  factory PackageOrderModel.fromSnap(
      DocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data()!;

    return PackageOrderModel(
      docId: snap.id,
      packageModel:
          PackageModel.fromJson(data['packageModel'] ?? <String, dynamic>{}),
      extraServices: (data['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      packageAmount: data['packageAmount'] ?? 0,
      extraServicesAmount: data['extraServicesAmount'] ?? 0,
      uid: data['uid'] ?? '',
      selectedAddressId: data['selectedAddressId'] ?? '',
      username: data['username'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt'] ?? 0),
      isPaid: data['isPaid'] ?? false,
      paidon: DateTime.fromMillisecondsSinceEpoch(data['paidon'] ?? 0),
      paidAmount: data['paidAmount'] ?? 0,
      totalAmount: data['totalAmount'] ?? 0,
    );
  }

  factory PackageOrderModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> docSnap) {
    final data = docSnap.data()!;
    return PackageOrderModel(
      docId: docSnap.id,
      packageModel:
          PackageModel.fromJson(data['packageModel'] ?? <String, dynamic>{}),
      extraServices: (data['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      packageAmount: data['packageAmount'] ?? 0,
      extraServicesAmount: data['extraServicesAmount'] ?? 0,
      uid: data['uid'] ?? '',
      selectedAddressId: data['selectedAddressId'] ?? '',
      username: data['username'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt'] ?? 0),
      isPaid: data['isPaid'] ?? false,
      paidon: DateTime.fromMillisecondsSinceEpoch(data['paidon'] ?? 0),
      totalAmount: data['totalAmount'] ?? 0,
      paidAmount: data['paidAmount'] ?? 0,
    );
  }
  factory PackageOrderModel.forNextSnap(DocumentSnapshot docSnap) {
    final data = docSnap.data()! as Map<String, dynamic>;
    return PackageOrderModel(
      docId: docSnap.id,
      packageModel:
          PackageModel.fromJson(data['packageModel'] ?? <String, dynamic>{}),
      extraServices: (data['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      packageAmount: data['packageAmount'] ?? 0,
      extraServicesAmount: data['extraServicesAmount'] ?? 0,
      uid: data['uid'] ?? '',
      selectedAddressId: data['selectedAddressId'] ?? '',
      username: data['username'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt'] ?? 0),
      isPaid: data['isPaid'] ?? false,
      paidon: DateTime.fromMillisecondsSinceEpoch(data['paidon'] ?? 0),
      totalAmount: data['totalAmount'] ?? 0,
      paidAmount: data['paidAmount'] ?? 0,
    );
  }
}
