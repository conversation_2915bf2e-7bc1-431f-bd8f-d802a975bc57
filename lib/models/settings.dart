import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:wedding_super_admin/models/package_model.dart';

class SettingsModel {
  final int startHour;
  final int endHour;
  final int startMin;
  final int endMin;
  final int slotGap;
  final List<String> apiPlatform;
  List<PackageServiceModel> services;
  final int orderNo;

  SettingsModel({
    required this.startHour,
    required this.endHour,
    required this.startMin,
    required this.endMin,
    required this.slotGap,
    required this.services,
    required this.apiPlatform,
    required this.orderNo,
  });

  /// Convert a `SettingsModel` instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'startHour': startHour,
      'endHour': endHour,
      'startMin': startMin,
      'endMin': endMin,
      'slotGap': slotGap,
      'apiPlatform': apiPlatform,
      'orderNo': orderNo,
    };
  }

  /// Create a `SettingsModel` instance from JSON
  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      startHour: json['startHour'],
      endHour: json['endHour'],
      startMin: json['startMin'],
      endMin: json['endMin'],
      orderNo: json['orderNo'],
      slotGap: json['slotGap'],
      apiPlatform: List<String>.from(json['apiPlatform']),
      services: Map.from(json['services'] ?? {})
          .entries
          .map((service) =>
              PackageServiceModel.fromJson(service.key, service.value))
          .toList(),
    );
  }

  /// Create a `SettingsModel` instance from Firestore QueryDocumentSnapshot
  factory SettingsModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> snap) {
    return SettingsModel(
      startHour: snap['startHour'],
      apiPlatform: List<String>.from(snap['apiPlatform']),
      endHour: snap['endHour'],
      startMin: snap['startMin'],
      orderNo: snap['orderNo'],
      endMin: snap['endMin'],
      slotGap: snap['slotGap'],
      services: Map.from(snap['services'] ?? {})
          .entries
          .map((service) =>
              PackageServiceModel.fromJson(service.key, service.value))
          .toList(),
    );
  }

  /// Create a `SettingsModel` instance from Firestore DocumentSnapshot
  factory SettingsModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data();
    return SettingsModel(
      startHour: data?['startHour'] ?? 0,
      apiPlatform: List<String>.from(data?['apiPlatform'] ?? []),
      endHour: data?['endHour'] ?? 0,
      orderNo: data?['orderNo'] ?? 0,
      startMin: data?['startMin'] ?? 0,
      endMin: data?['endMin'] ?? 0,
      slotGap: data?['slotGap'] ?? 0,
      services: Map.from(data?['services'] ?? {})
          .entries
          .map((service) =>
              PackageServiceModel.fromJson(service.key, service.value))
          .toList(),
    );
  }
}
