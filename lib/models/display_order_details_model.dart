import 'order_model.dart';
import 'product.dart';
import 'variants.dart';
import 'vendor.dart';

class DisplayProductsDetails {
  final ProductModel product;
  final NewVariantModel variant;
  final VendorModel vendorData;
  final OrderProductData orderData;

  DisplayProductsDetails({
    required this.product,
    required this.variant,
    required this.vendorData,
    required this.orderData,
  });
}
