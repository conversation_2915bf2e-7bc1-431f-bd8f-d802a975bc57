import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:wedding_super_admin/models/blogs.dart';
import 'package:wedding_super_admin/models/bookings_model.dart';
import 'package:wedding_super_admin/models/gallery.dart';
import 'package:wedding_super_admin/models/offerings.dart';
import 'package:wedding_super_admin/models/order_model.dart';
import 'package:wedding_super_admin/models/package_model.dart';
import 'package:wedding_super_admin/models/package_order_model.dart';
import 'package:wedding_super_admin/models/questionaire_model.dart';
import 'package:wedding_super_admin/models/session_model.dart';
import 'package:wedding_super_admin/models/settings.dart';
import 'package:wedding_super_admin/models/testimonials.dart';
import 'package:wedding_super_admin/models/vendor_order_model.dart';
import 'package:wedding_super_admin/shared/const.dart';
import '../models/subcategories.dart';
import '../models/teammodel.dart';
import '../models/vendor.dart';
import '../shared/firebase.dart';

class HomeCtrl extends GetxController {
  bool loggedIn = false;
  bool dataLoaded = false;
  TeamModel? currentUser;
  String? currentUserType;
  List<OfferingsModel> offerings = [];
  List<SubCategory> subcategories = [];
  List<QuestionnaireModel> allQuestionaire = [];
  List<VendorModel> vendors = [];
  List<TeamModel> teamMembers = [];
  List<TeamModel> consultants = [];
  List<TeamModel> stylers = [];
  List<GalleryModel> gallery = [];
  List<BlogModel> blogs = [];
  List<TestimonialsModel> testimonials = [];
  SettingsModel? settings;
  List<BlockedSlotModel> blockedSlotModelList = [];
  List<SessionModel> activeSessionsList = [];
  List<BookingsModel> bookingList = [];
  List<PackageModel> allPackages = [];
  List<OrderModel> allOrdersList = [];
  List<VendorOrderModel> allVendorOrderList = [];
  List<OrderModel> pendingOrdersList = [];
  List<OrderModel> paymentPendingOrdersList = [];
  List<OrderModel> userActionOrderList = [];
  List<PackageOrderModel> packageOrderList = [];
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? userDataStr;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? categorystream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? questionaireStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? packagesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? gallerystream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? blogstream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? subcategorystream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? testimonialStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? teamstream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? vendorStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? blockedStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? bookingsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? sessionStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? ordersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? vendorOrdersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? packageOrdersStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? settingStream;

  @override
  void onInit() {
    super.onInit();
    userStream();
    getMainCategories();
    getSubCategories();
    getSettings();
    getBlockedSlotList();
    getBookings();
    getSessions();
    getPackages();
    getPackagesOrders();
    getOrders();
    getVendorOrders();

    // getTeam();
    // getTestimonials();
    // getGallery();
  }

  userStream() async {
    FirebaseAuth.instance.authStateChanges().listen((event) {
      loggedIn = event != null;
      if (loggedIn) {
        userDataStream();
      }
    });
  }

  userDataStream() async {
    try {
      userDataStr?.cancel();
      userDataStr = FBFireStore.teamMember
          .doc(FBAuth.auth.currentUser?.uid)
          .snapshots()
          .listen(
        (event) async {
          if (event.data() != null) {
            currentUser = TeamModel.fromDocSnap(event);
            currentUserType = currentUser?.userType;
            if (currentUserType == UserTypes.admin ||
                currentUserType == UserTypes.manager) {
              getTestimonials();
              getGallery();
              getVendor();
              getBlogs();
              getTeam();
              getQuestionaireData();
              dataLoaded = true;
              update();
            }
          } else {
            currentUser = null;
            currentUserType = null;
            dataLoaded = true;
            update();
          }
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getQuestionaireData() async {
    try {
      questionaireStream?.cancel();
      questionaireStream = FBFireStore.questionaire.snapshots().listen((event) {
        allQuestionaire =
            event.docs.map((e) => QuestionnaireModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getPackages() async {
    try {
      packagesStream?.cancel();
      packagesStream = FBFireStore.packages.snapshots().listen((event) {
        allPackages = event.docs.map((e) => PackageModel.fromSnap(e)).toList();

        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getPackagesOrders() async {
    try {
      packageOrdersStream?.cancel();
      packageOrdersStream =
          FBFireStore.packageOrders.snapshots().listen((event) {
        packageOrderList =
            event.docs.map((e) => PackageOrderModel.fromSnap(e)).toList();

        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getMainCategories() async {
    try {
      categorystream?.cancel();
      categorystream = FBFireStore.offerings.snapshots().listen((event) {
        offerings = event.docs.map((e) => OfferingsModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getSubCategories() async {
    try {
      subcategorystream?.cancel();
      subcategorystream = FBFireStore.subCategories.snapshots().listen((event) {
        subcategories = event.docs.map((e) => SubCategory.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getTeam() async {
    try {
      teamstream?.cancel();
      teamstream = FBFireStore.teamMember
          .where('userType', whereNotIn: [UserTypes.admin, UserTypes.vendor])
          // .where('userType', isNotEqualTo: UserTypes.admin)
          .snapshots()
          .listen((event) {
            teamMembers = event.docs.map((e) => TeamModel.fromSnap(e)).toList();
            consultants.clear();
            consultants = teamMembers
                .where((element) => element.userType == UserTypes.consultants)
                .toList();
            stylers.clear();
            stylers = teamMembers
                .where((element) => element.userType == UserTypes.styler)
                .toList();
            // if (temp.isNotEmpty) {
            //   teamMembers = temp
            //       .where((element) => element.userType != UserTypes.vendor)
            //       .toList();
            // }
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getVendor() async {
    try {
      vendorStream?.cancel();
      vendorStream = FBFireStore.vendors.snapshots().listen((event) {
        vendors = event.docs.map((e) => VendorModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getTestimonials() async {
    try {
      testimonialStream?.cancel();
      testimonialStream = FBFireStore.testimonials.snapshots().listen((event) {
        testimonials =
            event.docs.map((e) => TestimonialsModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getGallery() async {
    try {
      gallerystream?.cancel();
      gallerystream = FBFireStore.gallery
          .orderBy('priorityNo', descending: false)
          .snapshots()
          .listen((event) {
        gallery = event.docs.map((e) => GalleryModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getBlogs() async {
    try {
      blogstream?.cancel();
      blogstream = FBFireStore.blogs.snapshots().listen((event) {
        blogs = event.docs.map((e) => BlogModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getSettings() {
    try {
      settingStream?.cancel();
      settingStream = FBFireStore.settings.snapshots().listen((event) {
        settings = SettingsModel.fromDocSnap(event);
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getBlockedSlotList() {
    try {
      blockedStream?.cancel();
      blockedStream = FBFireStore.blocked
          .where('blockedDateTime',
              isGreaterThanOrEqualTo: DateTime(DateTime.now().year,
                      DateTime.now().month, DateTime.now().day)
                  .millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        blockedSlotModelList =
            event.docs.map((e) => BlockedSlotModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getBookings() {
    try {
      bookingsStream?.cancel();
      bookingsStream = FBFireStore.bookings
          .where('isCompleted', isEqualTo: false)
          .snapshots()
          .listen((event) {
        bookingList = event.docs.map((e) => BookingsModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getSessions() {
    try {
      sessionStream?.cancel();
      sessionStream = FBFireStore.sessions
          .where('isActive', isEqualTo: true)
          .snapshots()
          .listen((event) {
        activeSessionsList =
            event.docs.map((e) => SessionModel.fromSnap(e)).toList();
        activeSessionsList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getOrders() {
    try {
      ordersStream?.cancel();
      ordersStream = FBFireStore.orders
          .where('orderStatus',
              whereNotIn: [OrderStatus.orderCompleted, OrderStatus.cancelled])
          .snapshots()
          .listen((event) {
            allOrdersList = event.docs.map((e) {
              return OrderModel.fromSnap(e);
            }).toList();
            pendingOrdersList = allOrdersList
                .where(
                    (element) => element.orderStatus == OrderStatus.processing)
                .toList();
            paymentPendingOrdersList = allOrdersList
                .where((element) =>
                    element.orderStatus == OrderStatus.paymentPending)
                .toList();
            userActionOrderList = allOrdersList
                .where((element) =>
                    (element.orderStatus == OrderStatus.userConfirmation) ||
                    (element.orderStatus == OrderStatus.paymentPending))
                .toList();
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getVendorOrders() {
    try {
      vendorOrdersStream?.cancel();
      vendorOrdersStream = FBFireStore.vendorOrder
          .where('status', whereNotIn: [VendorOrderStatus.orderCompleted])
          .snapshots()
          .listen((event) {
            allVendorOrderList =
                event.docs.map((e) => VendorOrderModel.fromSnap(e)).toList();
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
