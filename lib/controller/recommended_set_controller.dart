import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wedding_super_admin/models/product.dart';

import '../models/offerings.dart';
import '../models/session_model.dart';
import '../models/subcategories.dart';
import '../models/variants.dart';
import '../shared/firebase.dart';
import '../views/sessions/session_page.dart';

class RecommendedSetController extends GetxController {
  final titleCtrl = TextEditingController();
  final searchCtrl = TextEditingController();
  // List<SelectedProduct> selectedProducts = [];
  // List<NewVariantModel> selectedVariants = [];
  List<StylerProdSelection> selectedVariants = [];

  List<NewVariantModel> searchedVariants = [];
  List<NewVariantModel> selProductVariants = [];
  List<SubCategory> selectedSubCategories = [];
  List<SubCategory> subCategories = [];
  ValueKey searchKey = ValueKey(DateTime.now());
  bool dataLoading = false;
  OfferingsModel? selMainCategory;
  SubCategory? selSubCategory;
  ProductModel? selectedProduct;
  List<AllProductFilter> allProductType = [];
  List<ProductModel> filteredProducts = [];
  String? selectedVaraintId;

  getVariantData(List<SelectedProduct> variants) async {
    if (variants.isEmpty) {
      return [];
    }

    int noofloops = ((variants.length) / 30).ceil();

    for (var i = 0; i < noofloops; i++) {
      final startIndex = i * 30;
      final endIndex = (startIndex + 30) > variants.length
          ? variants.length
          : (startIndex + 30);
      try {
        final variantSnap = await FBFireStore.variants
            .where(FieldPath.documentId,
                whereIn: variants
                    .map((e) => e.variantId)
                    .toList()
                    .sublist(startIndex, endIndex))
            .get();
        for (var element in variantSnap.docs
            .map((e) => NewVariantModel.fromSnap(e))
            .toList()) {
          selectedVariants.add(StylerProdSelection(
              isSelected: (variants
                      .firstWhereOrNull((e) => e.variantId == element.docId)
                      ?.isSelected) ??
                  false,
              selectedVaraint: element));
        }
      } on Exception catch (e) {
        debugPrint(e.toString());
      }
    }

    // final variantSnap = await FBFireStore.variants
    //     .where(FieldPath.documentId, arrayContainsAny: variantIds)
    //     .get();
    // selectedVariants
    //     .addAll(variantSnap.docs.map((e) => NewVariantModel.fromSnap(e)));
  }

  getData(StylerProducts? stylerProducts) async {
    allProductType.clear();
    searchCtrl.clear();
    searchedVariants.clear();
    selectedVaraintId = null;
    selMainCategory = null;
    selSubCategory = null;
    selProductVariants.clear();
    titleCtrl.clear();
    selectedVariants.clear();
    selectedProduct = null;
    selectedVaraintId = null;
    dataLoading = true;
    update();
    if (stylerProducts != null) {
      titleCtrl.text = stylerProducts.title;
      selectedVariants.clear();

      await getVariantData(stylerProducts.selectedProducts.toList());
    }
    dataLoading = false;
    update();
  }

  getProductVariantData() async {
    if (selectedProduct != null) {
      final variantSnap = await FBFireStore.variants
          .where('productId', isEqualTo: selectedProduct!.docId)
          .get();

      selProductVariants.clear();
      selProductVariants
          .addAll(variantSnap.docs.map((e) => NewVariantModel.fromSnap(e)));
      update();
    }
  }

  getSearchedData(String str) async {
    // results.addAll(await FBFireStore.vendors
    //     .where('company_name', isGreaterThanOrEqualTo: str)
    //     .where('company_name', isLessThanOrEqualTo: "$str\uf7ff")

    //     // .limit(5)
    //     .get()
    //     .then((value) => value.docs
    //         .map((e) => VendorModel.fromSnap(e))
    //         .toList()
    //         .map((e) => e.companyName.toLowerCase())));
    if (str.isEmpty) {
      searchedVariants.clear();
    }
    searchedVariants.clear();
    final varinatByNameSnap = await FBFireStore.variants
        .where('lowerName', isGreaterThanOrEqualTo: str)
        .where('lowerName', isLessThanOrEqualTo: "$str\uf7ff")
        .limit(5)
        .get();
    searchedVariants.addAll(varinatByNameSnap.docs
        .map((e) => NewVariantModel.fromDocSnap(e))
        .toList());
    update();
  }

  getFilterData(SubCategory? subCategory) {
    allProductType.clear();
    for (MapEntry<String, List<String>> element
        in subCategory?.allData.entries ?? []) {
      if (element.value.isNotEmpty) {
        allProductType.addIf(
            allProductType.where((e) {
              return e.name.toLowerCase().trim() ==
                  element.key.toLowerCase().trim();
            }).isEmpty,
            AllProductFilter(
              name: element.key.toLowerCase(),
              value: element.value,
              slelectedFilters: [],
            ));
      }
    }
    allProductType.sort((a, b) => a.name.compareTo(b.name));
  }
}

class AllProductFilter {
  final String name;
  List<String> value;
  List<String> slelectedFilters;

  AllProductFilter({
    required this.name,
    required this.value,
    required this.slelectedFilters,
  });

  factory AllProductFilter.fromJson(Map<String, dynamic> json) {
    return AllProductFilter(
      name: json['name'],
      value: List<String>.from(json['value']),
      slelectedFilters: List<String>.from(json['slelectedFilters']),
    );
  }

  toJson() {
    return {
      name: {
        'value': value,
        'slelectedFilters': slelectedFilters,
      }
    };
  }
}

class ProductDisplayModel {
  final String productId;
  final String? varientId;
  final String productName;
  final String image;

  ProductDisplayModel({
    required this.productId,
    required this.productName,
    this.varientId,
    required this.image,
  });
}

class StylerProdSelection {
  final NewVariantModel selectedVaraint;
  bool isSelected;
  StylerProdSelection({
    required this.isSelected,
    required this.selectedVaraint,
  });
}
